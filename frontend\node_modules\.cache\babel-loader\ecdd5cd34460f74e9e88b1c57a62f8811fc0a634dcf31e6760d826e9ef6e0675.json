{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport React from \"react\";\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from \"../utils\";\nimport { withClerk } from \"./withClerk\";\nconst SignInButton = withClerk(({\n  clerk,\n  children,\n  ...props\n}) => {\n  const {\n    afterSignInUrl,\n    afterSignUpUrl,\n    redirectUrl,\n    mode,\n    ...rest\n  } = props;\n  children = normalizeWithDefaultValue(children, \"Sign in\");\n  const child = assertSingleChild(children)(\"SignInButton\");\n  const clickHandler = () => {\n    const opts = {\n      afterSignInUrl,\n      afterSignUpUrl,\n      redirectUrl\n    };\n    if (mode === \"modal\") {\n      return clerk.openSignIn(opts);\n    }\n    return clerk.redirectToSignIn(opts);\n  };\n  const wrappedChildClickHandler = async e => {\n    await safeExecute(child.props.onClick)(e);\n    return clickHandler();\n  };\n  const childProps = {\n    ...rest,\n    onClick: wrappedChildClickHandler\n  };\n  return React.cloneElement(child, childProps);\n}, \"SignInButton\");\nexport { SignInButton };", "map": {"version": 3, "names": ["React", "assertSingleChild", "normalizeWithDefaultValue", "safeExecute", "with<PERSON><PERSON><PERSON>", "SignInButton", "clerk", "children", "props", "afterSignInUrl", "afterSignUpUrl", "redirectUrl", "mode", "rest", "child", "clickHandler", "opts", "openSignIn", "redirectToSignIn", "wrappedChildClickHandler", "e", "onClick", "childProps", "cloneElement"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\components\\SignInButton.tsx"], "sourcesContent": ["import React from 'react';\n\nimport type { SignInButtonProps, WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport const SignInButton = withClerk(({ clerk, children, ...props }: WithClerkProp<SignInButtonProps>) => {\n  const { afterSignInUrl, afterSignUpUrl, redirectUrl, mode, ...rest } = props;\n\n  children = normalizeWithDefaultValue(children, 'Sign in');\n  const child = assertSingleChild(children)('SignInButton');\n\n  const clickHandler = () => {\n    const opts = { afterSignInUrl, afterSignUpUrl, redirectUrl };\n    if (mode === 'modal') {\n      return clerk.openSignIn(opts);\n    }\n    return clerk.redirectToSignIn(opts);\n  };\n\n  const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n    await safeExecute((child as any).props.onClick)(e);\n    return clickHandler();\n  };\n\n  const childProps = { ...rest, onClick: wrappedChildClickHandler };\n  return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n}, 'SignInButton');\n"], "mappings": ";AAAA,OAAOA,KAAA,MAAW;AAGlB,SAASC,iBAAA,EAAmBC,yBAAA,EAA2BC,WAAA,QAAmB;AAC1E,SAASC,SAAA,QAAiB;AAEnB,MAAMC,YAAA,GAAeD,SAAA,CAAU,CAAC;EAAEE,KAAA;EAAOC,QAAA;EAAU,GAAGC;AAAM,MAAwC;EACzG,MAAM;IAAEC,cAAA;IAAgBC,cAAA;IAAgBC,WAAA;IAAaC,IAAA;IAAM,GAAGC;EAAK,IAAIL,KAAA;EAEvED,QAAA,GAAWL,yBAAA,CAA0BK,QAAA,EAAU,SAAS;EACxD,MAAMO,KAAA,GAAQb,iBAAA,CAAkBM,QAAQ,EAAE,cAAc;EAExD,MAAMQ,YAAA,GAAeA,CAAA,KAAM;IACzB,MAAMC,IAAA,GAAO;MAAEP,cAAA;MAAgBC,cAAA;MAAgBC;IAAY;IAC3D,IAAIC,IAAA,KAAS,SAAS;MACpB,OAAON,KAAA,CAAMW,UAAA,CAAWD,IAAI;IAC9B;IACA,OAAOV,KAAA,CAAMY,gBAAA,CAAiBF,IAAI;EACpC;EAEA,MAAMG,wBAAA,GAAoD,MAAMC,CAAA,IAAK;IACnE,MAAMjB,WAAA,CAAaW,KAAA,CAAcN,KAAA,CAAMa,OAAO,EAAED,CAAC;IACjD,OAAOL,YAAA,CAAa;EACtB;EAEA,MAAMO,UAAA,GAAa;IAAE,GAAGT,IAAA;IAAMQ,OAAA,EAASF;EAAyB;EAChE,OAAOnB,KAAA,CAAMuB,YAAA,CAAaT,KAAA,EAAsCQ,UAAU;AAC5E,GAAG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}