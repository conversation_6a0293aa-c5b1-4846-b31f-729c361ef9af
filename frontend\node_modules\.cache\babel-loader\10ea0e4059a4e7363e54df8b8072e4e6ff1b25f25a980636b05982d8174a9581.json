{"ast": null, "code": "// src/isomorphicAtob.ts\nvar isomorphicAtob = data => {\n  if (typeof atob !== \"undefined\" && typeof atob === \"function\") {\n    return atob(data);\n  } else if (typeof global !== \"undefined\" && global.Buffer) {\n    return new global.Buffer(data, \"base64\").toString();\n  }\n  return data;\n};\nexport { isomorphicAtob };", "map": {"version": 3, "names": ["isomorphicAtob", "data", "atob", "global", "<PERSON><PERSON><PERSON>", "toString"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\isomorphicAtob.ts"], "sourcesContent": ["/**\n * A function that decodes a string of data which has been encoded using base-64 encoding.\n * Uses `atob` if available, otherwise uses `<PERSON><PERSON><PERSON>` from `global`. If neither are available, returns the data as-is.\n */\nexport const isomorphicAtob = (data: string) => {\n  if (typeof atob !== 'undefined' && typeof atob === 'function') {\n    return atob(data);\n  } else if (typeof global !== 'undefined' && global.Buffer) {\n    return new global.Buffer(data, 'base64').toString();\n  }\n  return data;\n};\n"], "mappings": ";AAIO,IAAMA,cAAA,GAAkBC,IAAA,IAAiB;EAC9C,IAAI,OAAOC,IAAA,KAAS,eAAe,OAAOA,IAAA,KAAS,YAAY;IAC7D,OAAOA,IAAA,CAAKD,IAAI;EAClB,WAAW,OAAOE,MAAA,KAAW,eAAeA,MAAA,CAAOC,MAAA,EAAQ;IACzD,OAAO,IAAID,MAAA,CAAOC,MAAA,CAAOH,IAAA,EAAM,QAAQ,EAAEI,QAAA,CAAS;EACpD;EACA,OAAOJ,IAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}