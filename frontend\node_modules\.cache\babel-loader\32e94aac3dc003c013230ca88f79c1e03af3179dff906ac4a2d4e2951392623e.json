{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Weight = createLucideIcon(\"Weight\", [[\"circle\", {\n  cx: \"12\",\n  cy: \"5\",\n  r: \"3\",\n  key: \"rqqgnr\"\n}], [\"path\", {\n  d: \"M6.5 8a2 2 0 0 0-1.905 1.46L2.1 18.5A2 2 0 0 0 4 21h16a2 2 0 0 0 1.925-2.54L19.4 9.5A2 2 0 0 0 17.48 8Z\",\n  key: \"56o5sh\"\n}]]);\nexport { Weight as default };", "map": {"version": 3, "names": ["Weight", "createLucideIcon", "cx", "cy", "r", "key", "d"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\lucide-react\\src\\icons\\weight.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Weight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjUiIHI9IjMiIC8+CiAgPHBhdGggZD0iTTYuNSA4YTIgMiAwIDAgMC0xLjkwNSAxLjQ2TDIuMSAxOC41QTIgMiAwIDAgMCA0IDIxaDE2YTIgMiAwIDAgMCAxLjkyNS0yLjU0TDE5LjQgOS41QTIgMiAwIDAgMCAxNy40OCA4WiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/weight\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Weight = createLucideIcon('Weight', [\n  ['circle', { cx: '12', cy: '5', r: '3', key: 'rqqgnr' }],\n  [\n    'path',\n    {\n      d: 'M6.5 8a2 2 0 0 0-1.905 1.46L2.1 18.5A2 2 0 0 0 4 21h16a2 2 0 0 0 1.925-2.54L19.4 9.5A2 2 0 0 0 17.48 8Z',\n      key: '56o5sh',\n    },\n  ],\n]);\n\nexport default Weight;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,QAAU;EAAEC,EAAI;EAAMC,EAAI;EAAKC,CAAG;EAAKC,GAAK;AAAA,CAAU,GACvD,CACE,QACA;EACEC,CAAG;EACHD,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}