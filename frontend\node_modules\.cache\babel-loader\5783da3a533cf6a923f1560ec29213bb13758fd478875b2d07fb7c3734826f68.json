{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Move3d = createLucideIcon(\"Move3d\", [[\"path\", {\n  d: \"M5 3v16h16\",\n  key: \"1mqmf9\"\n}], [\"path\", {\n  d: \"m5 19 6-6\",\n  key: \"jh6hbb\"\n}], [\"path\", {\n  d: \"m2 6 3-3 3 3\",\n  key: \"tkyvxa\"\n}], [\"path\", {\n  d: \"m18 16 3 3-3 3\",\n  key: \"1d4glt\"\n}]]);\nexport { Move3d as default };", "map": {"version": 3, "names": ["Move3d", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\lucide-react\\src\\icons\\move-3d.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Move3d\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAzdjE2aDE2IiAvPgogIDxwYXRoIGQ9Im01IDE5IDYtNiIgLz4KICA8cGF0aCBkPSJtMiA2IDMtMyAzIDMiIC8+CiAgPHBhdGggZD0ibTE4IDE2IDMgMy0zIDMiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/move-3d\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Move3d = createLucideIcon('Move3d', [\n  ['path', { d: 'M5 3v16h16', key: '1mqmf9' }],\n  ['path', { d: 'm5 19 6-6', key: 'jh6hbb' }],\n  ['path', { d: 'm2 6 3-3 3 3', key: 'tkyvxa' }],\n  ['path', { d: 'm18 16 3 3-3 3', key: '1d4glt' }],\n]);\n\nexport default Move3d;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,MAAA,GAASC,gBAAA,CAAiB,QAAU,GACxC,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,cAAgB;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC7C,CAAC,MAAQ;EAAED,CAAA,EAAG,gBAAkB;EAAAC,GAAA,EAAK;AAAA,CAAU,EAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}