{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport React from \"react\";\nimport { useAuthContext } from \"../contexts/AuthContext\";\nimport { useIsomorphicClerkContext } from \"../contexts/IsomorphicClerkContext\";\nimport { useSessionContext } from \"../contexts/SessionContext\";\nimport { LoadedGuarantee } from \"../contexts/StructureContext\";\nimport { useAuth } from \"../hooks\";\nimport { withClerk } from \"./withClerk\";\nconst SignedIn = ({\n  children\n}) => {\n  const {\n    userId\n  } = useAuthContext();\n  if (userId) {\n    return /* @__PURE__ */React.createElement(React.Fragment, null, children);\n  }\n  return null;\n};\nconst SignedOut = ({\n  children\n}) => {\n  const {\n    userId\n  } = useAuthContext();\n  if (userId === null) {\n    return /* @__PURE__ */React.createElement(React.Fragment, null, children);\n  }\n  return null;\n};\nconst ClerkLoaded = ({\n  children\n}) => {\n  const isomorphicClerk = useIsomorphicClerkContext();\n  if (!isomorphicClerk.loaded) {\n    return null;\n  }\n  return /* @__PURE__ */React.createElement(LoadedGuarantee, null, children);\n};\nconst ClerkLoading = ({\n  children\n}) => {\n  const isomorphicClerk = useIsomorphicClerkContext();\n  if (isomorphicClerk.loaded) {\n    return null;\n  }\n  return /* @__PURE__ */React.createElement(React.Fragment, null, children);\n};\nconst Protect = ({\n  children,\n  fallback,\n  ...restAuthorizedParams\n}) => {\n  const {\n    isLoaded,\n    has,\n    userId\n  } = useAuth();\n  if (!isLoaded) {\n    return null;\n  }\n  const unauthorized = /* @__PURE__ */React.createElement(React.Fragment, null, fallback != null ? fallback : null);\n  const authorized = /* @__PURE__ */React.createElement(React.Fragment, null, children);\n  if (!userId) {\n    return unauthorized;\n  }\n  if (typeof restAuthorizedParams.condition === \"function\") {\n    if (restAuthorizedParams.condition(has)) {\n      return authorized;\n    }\n    return unauthorized;\n  }\n  if (restAuthorizedParams.role || restAuthorizedParams.permission) {\n    if (has(restAuthorizedParams)) {\n      return authorized;\n    }\n    return unauthorized;\n  }\n  return authorized;\n};\nconst RedirectToSignIn = withClerk(({\n  clerk,\n  ...props\n}) => {\n  const {\n    client,\n    session\n  } = clerk;\n  const {\n    __unstable__environment\n  } = clerk;\n  const hasActiveSessions = client.activeSessions && client.activeSessions.length > 0;\n  React.useEffect(() => {\n    if (session === null && hasActiveSessions && __unstable__environment) {\n      const {\n        afterSignOutOneUrl\n      } = __unstable__environment.displayConfig;\n      void clerk.navigate(afterSignOutOneUrl);\n    } else {\n      void clerk.redirectToSignIn(props);\n    }\n  }, []);\n  return null;\n}, \"RedirectToSignIn\");\nconst RedirectToSignUp = withClerk(({\n  clerk,\n  ...props\n}) => {\n  React.useEffect(() => {\n    void clerk.redirectToSignUp(props);\n  }, []);\n  return null;\n}, \"RedirectToSignUp\");\nconst RedirectToUserProfile = withClerk(({\n  clerk\n}) => {\n  React.useEffect(() => {\n    clerk.redirectToUserProfile();\n  }, []);\n  return null;\n}, \"RedirectToUserProfile\");\nconst RedirectToOrganizationProfile = withClerk(({\n  clerk\n}) => {\n  React.useEffect(() => {\n    clerk.redirectToOrganizationProfile();\n  }, []);\n  return null;\n}, \"RedirectToOrganizationProfile\");\nconst RedirectToCreateOrganization = withClerk(({\n  clerk\n}) => {\n  React.useEffect(() => {\n    clerk.redirectToCreateOrganization();\n  }, []);\n  return null;\n}, \"RedirectToCreateOrganization\");\nconst AuthenticateWithRedirectCallback = withClerk(({\n  clerk,\n  ...handleRedirectCallbackParams\n}) => {\n  React.useEffect(() => {\n    void clerk.handleRedirectCallback(handleRedirectCallbackParams);\n  }, []);\n  return null;\n}, \"AuthenticateWithRedirectCallback\");\nconst MultisessionAppSupport = ({\n  children\n}) => {\n  const session = useSessionContext();\n  return /* @__PURE__ */React.createElement(React.Fragment, {\n    key: session ? session.id : \"no-users\"\n  }, children);\n};\nexport { AuthenticateWithRedirectCallback, ClerkLoaded, ClerkLoading, MultisessionAppSupport, Protect, RedirectToCreateOrganization, RedirectToOrganizationProfile, RedirectToSignIn, RedirectToSignUp, RedirectToUserProfile, SignedIn, SignedOut };", "map": {"version": 3, "names": ["React", "useAuthContext", "useIsomorphicClerkContext", "useSessionContext", "LoadedGuarantee", "useAuth", "with<PERSON><PERSON><PERSON>", "SignedIn", "children", "userId", "createElement", "Fragment", "SignedOut", "ClerkLoaded", "isomorphicClerk", "loaded", "Clerk<PERSON><PERSON><PERSON>", "Protect", "fallback", "restAuthorizedParams", "isLoaded", "has", "unauthorized", "authorized", "condition", "role", "permission", "RedirectToSignIn", "clerk", "props", "client", "session", "__unstable__environment", "hasActiveSessions", "activeSessions", "length", "useEffect", "afterSignOutOneUrl", "displayConfig", "navigate", "redirectToSignIn", "RedirectToSignUp", "redirectToSignUp", "RedirectToUserProfile", "redirectToUserProfile", "RedirectToOrganizationProfile", "redirectToOrganizationProfile", "RedirectToCreateOrganization", "redirectToCreateOrganization", "AuthenticateWithRedirectCallback", "handleRedirectCallbackParams", "handleRedirectCallback", "MultisessionAppSupport", "key", "id"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\components\\controlComponents.tsx"], "sourcesContent": ["import type {\n  CheckAuthorizationWithCustomPermissions,\n  HandleOAuthCallbackParams,\n  OrganizationCustomPermissionKey,\n  OrganizationCustomRoleKey,\n} from '@clerk/types';\nimport React from 'react';\n\nimport { useAuthContext } from '../contexts/AuthContext';\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\nimport { useSessionContext } from '../contexts/SessionContext';\nimport { LoadedGuarantee } from '../contexts/StructureContext';\nimport { useAuth } from '../hooks';\nimport type { RedirectToSignInProps, RedirectToSignUpProps, WithClerkProp } from '../types';\nimport { withClerk } from './withClerk';\n\nexport const SignedIn = ({ children }: React.PropsWithChildren<unknown>): JSX.Element | null => {\n  const { userId } = useAuthContext();\n  if (userId) {\n    return <>{children}</>;\n  }\n  return null;\n};\n\nexport const SignedOut = ({ children }: React.PropsWithChildren<unknown>): JSX.Element | null => {\n  const { userId } = useAuthContext();\n  if (userId === null) {\n    return <>{children}</>;\n  }\n  return null;\n};\n\nexport const ClerkLoaded = ({ children }: React.PropsWithChildren<unknown>): JSX.Element | null => {\n  const isomorphicClerk = useIsomorphicClerkContext();\n  if (!isomorphicClerk.loaded) {\n    return null;\n  }\n  return <LoadedGuarantee>{children}</LoadedGuarantee>;\n};\n\nexport const ClerkLoading = ({ children }: React.PropsWithChildren<unknown>): JSX.Element | null => {\n  const isomorphicClerk = useIsomorphicClerkContext();\n  if (isomorphicClerk.loaded) {\n    return null;\n  }\n  return <>{children}</>;\n};\n\ntype ProtectProps = React.PropsWithChildren<\n  (\n    | {\n        condition?: never;\n        role: OrganizationCustomRoleKey;\n        permission?: never;\n      }\n    | {\n        condition?: never;\n        role?: never;\n        permission: OrganizationCustomPermissionKey;\n      }\n    | {\n        condition: (has: CheckAuthorizationWithCustomPermissions) => boolean;\n        role?: never;\n        permission?: never;\n      }\n    | {\n        condition?: never;\n        role?: never;\n        permission?: never;\n      }\n  ) & {\n    fallback?: React.ReactNode;\n  }\n>;\n\n/**\n * Use `<Protect/>` in order to prevent unauthenticated or unauthorized users from accessing the children passed to the component.\n *\n * Examples:\n * ```\n * <Protect permission=\"a_permission_key\" />\n * <Protect role=\"a_role_key\" />\n * <Protect condition={(has) => has({permission:\"a_permission_key\"})} />\n * <Protect condition={(has) => has({role:\"a_role_key\"})} />\n * <Protect fallback={<p>Unauthorized</p>} />\n * ```\n */\nexport const Protect = ({ children, fallback, ...restAuthorizedParams }: ProtectProps) => {\n  const { isLoaded, has, userId } = useAuth();\n\n  /**\n   * Avoid flickering children or fallback while clerk is loading sessionId or userId\n   */\n  if (!isLoaded) {\n    return null;\n  }\n\n  /**\n   * Fallback to UI provided by user or `null` if authorization checks failed\n   */\n  const unauthorized = <>{fallback ?? null}</>;\n\n  const authorized = <>{children}</>;\n\n  if (!userId) {\n    return unauthorized;\n  }\n\n  /**\n   * Check against the results of `has` called inside the callback\n   */\n  if (typeof restAuthorizedParams.condition === 'function') {\n    if (restAuthorizedParams.condition(has)) {\n      return authorized;\n    }\n    return unauthorized;\n  }\n\n  if (restAuthorizedParams.role || restAuthorizedParams.permission) {\n    if (has(restAuthorizedParams)) {\n      return authorized;\n    }\n    return unauthorized;\n  }\n\n  /**\n   * If neither of the authorization params are passed behave as the `<SignedIn/>`.\n   * If fallback is present render that instead of rendering nothing.\n   */\n  return authorized;\n};\n\nexport const RedirectToSignIn = withClerk(({ clerk, ...props }: WithClerkProp<RedirectToSignInProps>) => {\n  const { client, session } = clerk;\n  // TODO: Remove temp use of __unstable__environment\n  const { __unstable__environment } = clerk as any;\n\n  const hasActiveSessions = client.activeSessions && client.activeSessions.length > 0;\n\n  React.useEffect(() => {\n    if (session === null && hasActiveSessions && __unstable__environment) {\n      const { afterSignOutOneUrl } = __unstable__environment.displayConfig;\n      void clerk.navigate(afterSignOutOneUrl);\n    } else {\n      void clerk.redirectToSignIn(props);\n    }\n  }, []);\n\n  return null;\n}, 'RedirectToSignIn');\n\nexport const RedirectToSignUp = withClerk(({ clerk, ...props }: WithClerkProp<RedirectToSignUpProps>) => {\n  React.useEffect(() => {\n    void clerk.redirectToSignUp(props);\n  }, []);\n\n  return null;\n}, 'RedirectToSignUp');\n\nexport const RedirectToUserProfile = withClerk(({ clerk }) => {\n  React.useEffect(() => {\n    clerk.redirectToUserProfile();\n  }, []);\n\n  return null;\n}, 'RedirectToUserProfile');\n\nexport const RedirectToOrganizationProfile = withClerk(({ clerk }) => {\n  React.useEffect(() => {\n    clerk.redirectToOrganizationProfile();\n  }, []);\n\n  return null;\n}, 'RedirectToOrganizationProfile');\n\nexport const RedirectToCreateOrganization = withClerk(({ clerk }) => {\n  React.useEffect(() => {\n    clerk.redirectToCreateOrganization();\n  }, []);\n\n  return null;\n}, 'RedirectToCreateOrganization');\n\nexport const AuthenticateWithRedirectCallback = withClerk(\n  ({ clerk, ...handleRedirectCallbackParams }: WithClerkProp<HandleOAuthCallbackParams>) => {\n    React.useEffect(() => {\n      void clerk.handleRedirectCallback(handleRedirectCallbackParams);\n    }, []);\n\n    return null;\n  },\n  'AuthenticateWithRedirectCallback',\n);\n\nexport const MultisessionAppSupport = ({ children }: React.PropsWithChildren<unknown>): JSX.Element => {\n  const session = useSessionContext();\n  return <React.Fragment key={session ? session.id : 'no-users'}>{children}</React.Fragment>;\n};\n"], "mappings": ";AAMA,OAAOA,KAAA,MAAW;AAElB,SAASC,cAAA,QAAsB;AAC/B,SAASC,yBAAA,QAAiC;AAC1C,SAASC,iBAAA,QAAyB;AAClC,SAASC,eAAA,QAAuB;AAChC,SAASC,OAAA,QAAe;AAExB,SAASC,SAAA,QAAiB;AAEnB,MAAMC,QAAA,GAAWA,CAAC;EAAEC;AAAS,MAA4D;EAC9F,MAAM;IAAEC;EAAO,IAAIR,cAAA,CAAe;EAClC,IAAIQ,MAAA,EAAQ;IACV,OAAO,eAAAT,KAAA,CAAAU,aAAA,CAAAV,KAAA,CAAAW,QAAA,QAAGH,QAAS;EACrB;EACA,OAAO;AACT;AAEO,MAAMI,SAAA,GAAYA,CAAC;EAAEJ;AAAS,MAA4D;EAC/F,MAAM;IAAEC;EAAO,IAAIR,cAAA,CAAe;EAClC,IAAIQ,MAAA,KAAW,MAAM;IACnB,OAAO,eAAAT,KAAA,CAAAU,aAAA,CAAAV,KAAA,CAAAW,QAAA,QAAGH,QAAS;EACrB;EACA,OAAO;AACT;AAEO,MAAMK,WAAA,GAAcA,CAAC;EAAEL;AAAS,MAA4D;EACjG,MAAMM,eAAA,GAAkBZ,yBAAA,CAA0B;EAClD,IAAI,CAACY,eAAA,CAAgBC,MAAA,EAAQ;IAC3B,OAAO;EACT;EACA,OAAO,eAAAf,KAAA,CAAAU,aAAA,CAACN,eAAA,QAAiBI,QAAS;AACpC;AAEO,MAAMQ,YAAA,GAAeA,CAAC;EAAER;AAAS,MAA4D;EAClG,MAAMM,eAAA,GAAkBZ,yBAAA,CAA0B;EAClD,IAAIY,eAAA,CAAgBC,MAAA,EAAQ;IAC1B,OAAO;EACT;EACA,OAAO,eAAAf,KAAA,CAAAU,aAAA,CAAAV,KAAA,CAAAW,QAAA,QAAGH,QAAS;AACrB;AAyCO,MAAMS,OAAA,GAAUA,CAAC;EAAET,QAAA;EAAUU,QAAA;EAAU,GAAGC;AAAqB,MAAoB;EACxF,MAAM;IAAEC,QAAA;IAAUC,GAAA;IAAKZ;EAAO,IAAIJ,OAAA,CAAQ;EAK1C,IAAI,CAACe,QAAA,EAAU;IACb,OAAO;EACT;EAKA,MAAME,YAAA,GAAe,eAAAtB,KAAA,CAAAU,aAAA,CAAAV,KAAA,CAAAW,QAAA,QAAGO,QAAA,WAAAA,QAAA,GAAY,IAAK;EAEzC,MAAMK,UAAA,GAAa,eAAAvB,KAAA,CAAAU,aAAA,CAAAV,KAAA,CAAAW,QAAA,QAAGH,QAAS;EAE/B,IAAI,CAACC,MAAA,EAAQ;IACX,OAAOa,YAAA;EACT;EAKA,IAAI,OAAOH,oBAAA,CAAqBK,SAAA,KAAc,YAAY;IACxD,IAAIL,oBAAA,CAAqBK,SAAA,CAAUH,GAAG,GAAG;MACvC,OAAOE,UAAA;IACT;IACA,OAAOD,YAAA;EACT;EAEA,IAAIH,oBAAA,CAAqBM,IAAA,IAAQN,oBAAA,CAAqBO,UAAA,EAAY;IAChE,IAAIL,GAAA,CAAIF,oBAAoB,GAAG;MAC7B,OAAOI,UAAA;IACT;IACA,OAAOD,YAAA;EACT;EAMA,OAAOC,UAAA;AACT;AAEO,MAAMI,gBAAA,GAAmBrB,SAAA,CAAU,CAAC;EAAEsB,KAAA;EAAO,GAAGC;AAAM,MAA4C;EACvG,MAAM;IAAEC,MAAA;IAAQC;EAAQ,IAAIH,KAAA;EAE5B,MAAM;IAAEI;EAAwB,IAAIJ,KAAA;EAEpC,MAAMK,iBAAA,GAAoBH,MAAA,CAAOI,cAAA,IAAkBJ,MAAA,CAAOI,cAAA,CAAeC,MAAA,GAAS;EAElFnC,KAAA,CAAMoC,SAAA,CAAU,MAAM;IACpB,IAAIL,OAAA,KAAY,QAAQE,iBAAA,IAAqBD,uBAAA,EAAyB;MACpE,MAAM;QAAEK;MAAmB,IAAIL,uBAAA,CAAwBM,aAAA;MACvD,KAAKV,KAAA,CAAMW,QAAA,CAASF,kBAAkB;IACxC,OAAO;MACL,KAAKT,KAAA,CAAMY,gBAAA,CAAiBX,KAAK;IACnC;EACF,GAAG,EAAE;EAEL,OAAO;AACT,GAAG,kBAAkB;AAEd,MAAMY,gBAAA,GAAmBnC,SAAA,CAAU,CAAC;EAAEsB,KAAA;EAAO,GAAGC;AAAM,MAA4C;EACvG7B,KAAA,CAAMoC,SAAA,CAAU,MAAM;IACpB,KAAKR,KAAA,CAAMc,gBAAA,CAAiBb,KAAK;EACnC,GAAG,EAAE;EAEL,OAAO;AACT,GAAG,kBAAkB;AAEd,MAAMc,qBAAA,GAAwBrC,SAAA,CAAU,CAAC;EAAEsB;AAAM,MAAM;EAC5D5B,KAAA,CAAMoC,SAAA,CAAU,MAAM;IACpBR,KAAA,CAAMgB,qBAAA,CAAsB;EAC9B,GAAG,EAAE;EAEL,OAAO;AACT,GAAG,uBAAuB;AAEnB,MAAMC,6BAAA,GAAgCvC,SAAA,CAAU,CAAC;EAAEsB;AAAM,MAAM;EACpE5B,KAAA,CAAMoC,SAAA,CAAU,MAAM;IACpBR,KAAA,CAAMkB,6BAAA,CAA8B;EACtC,GAAG,EAAE;EAEL,OAAO;AACT,GAAG,+BAA+B;AAE3B,MAAMC,4BAAA,GAA+BzC,SAAA,CAAU,CAAC;EAAEsB;AAAM,MAAM;EACnE5B,KAAA,CAAMoC,SAAA,CAAU,MAAM;IACpBR,KAAA,CAAMoB,4BAAA,CAA6B;EACrC,GAAG,EAAE;EAEL,OAAO;AACT,GAAG,8BAA8B;AAE1B,MAAMC,gCAAA,GAAmC3C,SAAA,CAC9C,CAAC;EAAEsB,KAAA;EAAO,GAAGsB;AAA6B,MAAgD;EACxFlD,KAAA,CAAMoC,SAAA,CAAU,MAAM;IACpB,KAAKR,KAAA,CAAMuB,sBAAA,CAAuBD,4BAA4B;EAChE,GAAG,EAAE;EAEL,OAAO;AACT,GACA,kCACF;AAEO,MAAME,sBAAA,GAAyBA,CAAC;EAAE5C;AAAS,MAAqD;EACrG,MAAMuB,OAAA,GAAU5B,iBAAA,CAAkB;EAClC,OAAO,eAAAH,KAAA,CAAAU,aAAA,CAACV,KAAA,CAAMW,QAAA,EAAN;IAAe0C,GAAA,EAAKtB,OAAA,GAAUA,OAAA,CAAQuB,EAAA,GAAK;EAAA,GAAa9C,QAAS;AAC3E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}