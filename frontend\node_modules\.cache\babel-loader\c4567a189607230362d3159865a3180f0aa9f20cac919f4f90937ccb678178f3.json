{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport { parsePublishableKey } from \"@clerk/shared/keys\";\nimport { loadScript } from \"@clerk/shared/loadScript\";\nimport { isValidProxyUrl, proxyUrlToAbsoluteURL } from \"@clerk/shared/proxy\";\nimport { addClerkPrefix } from \"@clerk/shared/url\";\nimport { errorThrower } from \"./errorThrower\";\nimport { isDevOrStagingUrl } from \"./isDevOrStageUrl\";\nimport { versionSelector } from \"./versionSelector\";\nconst FAILED_TO_LOAD_ERROR = \"Clerk: Failed to load Clerk\";\nconst loadClerkJsScript = opts => {\n  const {\n    frontendApi,\n    publishableKey\n  } = opts;\n  if (!publishableKey && !frontendApi) {\n    errorThrower.throwMissingPublishableKeyError();\n  }\n  return loadScript(clerkJsScriptUrl(opts), {\n    async: true,\n    crossOrigin: \"anonymous\",\n    beforeLoad: applyClerkJsScriptAttributes(opts)\n  }).catch(() => {\n    throw new Error(FAILED_TO_LOAD_ERROR);\n  });\n};\nconst clerkJsScriptUrl = opts => {\n  var _a, _b;\n  const {\n    clerkJSUrl,\n    clerkJSVariant,\n    clerkJSVersion,\n    proxyUrl,\n    domain,\n    publishableKey,\n    frontendApi\n  } = opts;\n  if (clerkJSUrl) {\n    return clerkJSUrl;\n  }\n  let scriptHost = \"\";\n  if (!!proxyUrl && isValidProxyUrl(proxyUrl)) {\n    scriptHost = proxyUrlToAbsoluteURL(proxyUrl).replace(/http(s)?:\\/\\//, \"\");\n  } else if (domain && !isDevOrStagingUrl(((_a = parsePublishableKey(publishableKey)) == null ? void 0 : _a.frontendApi) || frontendApi || \"\")) {\n    scriptHost = addClerkPrefix(domain);\n  } else {\n    scriptHost = ((_b = parsePublishableKey(publishableKey)) == null ? void 0 : _b.frontendApi) || frontendApi || \"\";\n  }\n  const variant = clerkJSVariant ? `${clerkJSVariant.replace(/\\.+$/, \"\")}.` : \"\";\n  const version = versionSelector(clerkJSVersion);\n  return `https://${scriptHost}/npm/@clerk/clerk-js@${version}/dist/clerk.${variant}browser.js`;\n};\nconst applyClerkJsScriptAttributes = options => script => {\n  const {\n    publishableKey,\n    frontendApi,\n    proxyUrl,\n    domain\n  } = options;\n  if (publishableKey) {\n    script.setAttribute(\"data-clerk-publishable-key\", publishableKey);\n  } else if (frontendApi) {\n    script.setAttribute(\"data-clerk-frontend-api\", frontendApi);\n  }\n  if (proxyUrl) {\n    script.setAttribute(\"data-clerk-proxy-url\", proxyUrl);\n  }\n  if (domain) {\n    script.setAttribute(\"data-clerk-domain\", domain);\n  }\n};\nexport { loadClerkJsScript };", "map": {"version": 3, "names": ["parsePublishableKey", "loadScript", "isValidProxyUrl", "proxyUrlToAbsoluteURL", "addClerkPrefix", "errorThrower", "isDevOrStagingUrl", "versionSelector", "FAILED_TO_LOAD_ERROR", "loadClerkJsScript", "opts", "frontendApi", "publishableKey", "throwMissingPublishableKeyError", "clerkJsScriptUrl", "async", "crossOrigin", "beforeLoad", "applyClerkJsScriptAttributes", "catch", "Error", "_a", "_b", "clerk<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clerkJ<PERSON><PERSON><PERSON>", "proxyUrl", "domain", "scriptHost", "replace", "variant", "version", "options", "script", "setAttribute"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\utils\\loadClerkJsScript.ts"], "sourcesContent": ["import { parsePublishable<PERSON>ey } from '@clerk/shared/keys';\nimport { loadScript } from '@clerk/shared/loadScript';\nimport { isValidProxyUrl, proxyUrlToAbsoluteURL } from '@clerk/shared/proxy';\nimport { addClerkPrefix } from '@clerk/shared/url';\n\nimport type { IsomorphicClerkOptions } from '../types';\nimport { errorThrower } from './errorThrower';\nimport { isDevOrStagingUrl } from './isDevOrStageUrl';\nimport { versionSelector } from './versionSelector';\n\nconst FAILED_TO_LOAD_ERROR = 'Clerk: Failed to load Clerk';\n\ntype LoadClerkJsScriptOptions = Omit<IsomorphicClerkOptions, 'proxyUrl' | 'domain'> & {\n  proxyUrl: string;\n  domain: string;\n};\n\nexport const loadClerkJsScript = (opts: LoadClerkJsScriptOptions) => {\n  const { frontendApi, publishableKey } = opts;\n\n  if (!publishableKey && !frontendApi) {\n    errorThrower.throwMissingPublishableKeyError();\n  }\n\n  return loadScript(clerkJsScriptUrl(opts), {\n    async: true,\n    crossOrigin: 'anonymous',\n    beforeLoad: applyClerkJsScriptAttributes(opts),\n  }).catch(() => {\n    throw new Error(FAILED_TO_LOAD_ERROR);\n  });\n};\n\nconst clerkJsScriptUrl = (opts: LoadClerkJsScriptOptions) => {\n  const { clerkJSUrl, clerkJSVariant, clerkJSVersion, proxyUrl, domain, publishableKey, frontendApi } = opts;\n\n  if (clerkJSUrl) {\n    return clerkJSUrl;\n  }\n\n  let scriptHost = '';\n  if (!!proxyUrl && isValidProxyUrl(proxyUrl)) {\n    scriptHost = proxyUrlToAbsoluteURL(proxyUrl).replace(/http(s)?:\\/\\//, '');\n  } else if (domain && !isDevOrStagingUrl(parsePublishableKey(publishableKey)?.frontendApi || frontendApi || '')) {\n    scriptHost = addClerkPrefix(domain);\n  } else {\n    scriptHost = parsePublishableKey(publishableKey)?.frontendApi || frontendApi || '';\n  }\n\n  const variant = clerkJSVariant ? `${clerkJSVariant.replace(/\\.+$/, '')}.` : '';\n  const version = versionSelector(clerkJSVersion);\n  return `https://${scriptHost}/npm/@clerk/clerk-js@${version}/dist/clerk.${variant}browser.js`;\n};\n\nconst applyClerkJsScriptAttributes = (options: LoadClerkJsScriptOptions) => (script: HTMLScriptElement) => {\n  const { publishableKey, frontendApi, proxyUrl, domain } = options;\n  if (publishableKey) {\n    script.setAttribute('data-clerk-publishable-key', publishableKey);\n  } else if (frontendApi) {\n    script.setAttribute('data-clerk-frontend-api', frontendApi);\n  }\n\n  if (proxyUrl) {\n    script.setAttribute('data-clerk-proxy-url', proxyUrl);\n  }\n\n  if (domain) {\n    script.setAttribute('data-clerk-domain', domain);\n  }\n};\n"], "mappings": ";AAAA,SAASA,mBAAA,QAA2B;AACpC,SAASC,UAAA,QAAkB;AAC3B,SAASC,eAAA,EAAiBC,qBAAA,QAA6B;AACvD,SAASC,cAAA,QAAsB;AAG/B,SAASC,YAAA,QAAoB;AAC7B,SAASC,iBAAA,QAAyB;AAClC,SAASC,eAAA,QAAuB;AAEhC,MAAMC,oBAAA,GAAuB;AAOtB,MAAMC,iBAAA,GAAqBC,IAAA,IAAmC;EACnE,MAAM;IAAEC,WAAA;IAAaC;EAAe,IAAIF,IAAA;EAExC,IAAI,CAACE,cAAA,IAAkB,CAACD,WAAA,EAAa;IACnCN,YAAA,CAAaQ,+BAAA,CAAgC;EAC/C;EAEA,OAAOZ,UAAA,CAAWa,gBAAA,CAAiBJ,IAAI,GAAG;IACxCK,KAAA,EAAO;IACPC,WAAA,EAAa;IACbC,UAAA,EAAYC,4BAAA,CAA6BR,IAAI;EAC/C,CAAC,EAAES,KAAA,CAAM,MAAM;IACb,MAAM,IAAIC,KAAA,CAAMZ,oBAAoB;EACtC,CAAC;AACH;AAEA,MAAMM,gBAAA,GAAoBJ,IAAA,IAAmC;EAjC7D,IAAAW,EAAA,EAAAC,EAAA;EAkCE,MAAM;IAAEC,UAAA;IAAYC,cAAA;IAAgBC,cAAA;IAAgBC,QAAA;IAAUC,MAAA;IAAQf,cAAA;IAAgBD;EAAY,IAAID,IAAA;EAEtG,IAAIa,UAAA,EAAY;IACd,OAAOA,UAAA;EACT;EAEA,IAAIK,UAAA,GAAa;EACjB,IAAI,CAAC,CAACF,QAAA,IAAYxB,eAAA,CAAgBwB,QAAQ,GAAG;IAC3CE,UAAA,GAAazB,qBAAA,CAAsBuB,QAAQ,EAAEG,OAAA,CAAQ,iBAAiB,EAAE;EAC1E,WAAWF,MAAA,IAAU,CAACrB,iBAAA,GAAkBe,EAAA,GAAArB,mBAAA,CAAoBY,cAAc,MAAlC,gBAAAS,EAAA,CAAqCV,WAAA,KAAeA,WAAA,IAAe,EAAE,GAAG;IAC9GiB,UAAA,GAAaxB,cAAA,CAAeuB,MAAM;EACpC,OAAO;IACLC,UAAA,KAAaN,EAAA,GAAAtB,mBAAA,CAAoBY,cAAc,MAAlC,gBAAAU,EAAA,CAAqCX,WAAA,KAAeA,WAAA,IAAe;EAClF;EAEA,MAAMmB,OAAA,GAAUN,cAAA,GAAiB,GAAGA,cAAA,CAAeK,OAAA,CAAQ,QAAQ,EAAE,CAAC,MAAM;EAC5E,MAAME,OAAA,GAAUxB,eAAA,CAAgBkB,cAAc;EAC9C,OAAO,WAAWG,UAAU,wBAAwBG,OAAO,eAAeD,OAAO;AACnF;AAEA,MAAMZ,4BAAA,GAAgCc,OAAA,IAAuCC,MAAA,IAA8B;EACzG,MAAM;IAAErB,cAAA;IAAgBD,WAAA;IAAae,QAAA;IAAUC;EAAO,IAAIK,OAAA;EAC1D,IAAIpB,cAAA,EAAgB;IAClBqB,MAAA,CAAOC,YAAA,CAAa,8BAA8BtB,cAAc;EAClE,WAAWD,WAAA,EAAa;IACtBsB,MAAA,CAAOC,YAAA,CAAa,2BAA2BvB,WAAW;EAC5D;EAEA,IAAIe,QAAA,EAAU;IACZO,MAAA,CAAOC,YAAA,CAAa,wBAAwBR,QAAQ;EACtD;EAEA,IAAIC,MAAA,EAAQ;IACVM,MAAA,CAAOC,YAAA,CAAa,qBAAqBP,MAAM;EACjD;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}