{"ast": null, "code": "// src/utils/instance.ts\nfunction isStaging(frontendApi) {\n  return frontendApi.endsWith(\".lclstage.dev\") || frontendApi.endsWith(\".stgstage.dev\") || frontendApi.endsWith(\".clerkstage.dev\") || frontendApi.endsWith(\".accountsstage.dev\");\n}\n\n// src/url.ts\nfunction parseSearchParams(queryString = \"\") {\n  if (queryString.startsWith(\"?\")) {\n    queryString = queryString.slice(1);\n  }\n  return new URLSearchParams(queryString);\n}\nfunction stripScheme(url = \"\") {\n  return (url || \"\").replace(/^.+:\\/\\//, \"\");\n}\nfunction addClerkPrefix(str) {\n  if (!str) {\n    return \"\";\n  }\n  let regex;\n  if (str.match(/^(clerk\\.)+\\w*$/)) {\n    regex = /(clerk\\.)*(?=clerk\\.)/;\n  } else if (str.match(/\\.clerk.accounts/)) {\n    return str;\n  } else {\n    regex = /^(clerk\\.)*/gi;\n  }\n  const stripped = str.replace(regex, \"\");\n  return `clerk.${stripped}`;\n}\nvar getClerkJsMajorVersionOrTag = (frontendApi, pkgVersion) => {\n  if (!pkgVersion && isStaging(frontendApi)) {\n    return \"canary\";\n  }\n  if (!pkgVersion) {\n    return \"latest\";\n  }\n  return pkgVersion.split(\".\")[0] || \"latest\";\n};\nvar getScriptUrl = (frontendApi, {\n  pkgVersion = \"4.73.9\",\n  clerkJSVersion\n}) => {\n  const noSchemeFrontendApi = frontendApi.replace(/http(s)?:\\/\\//, \"\");\n  const major = getClerkJsMajorVersionOrTag(frontendApi, pkgVersion);\n  return `https://${noSchemeFrontendApi}/npm/@clerk/clerk-js@${clerkJSVersion || major}/dist/clerk.browser.js`;\n};\nexport { isStaging, parseSearchParams, stripScheme, addClerkPrefix, getClerkJsMajorVersionOrTag, getScriptUrl };", "map": {"version": 3, "names": ["isStaging", "frontendApi", "endsWith", "parseSearchParams", "queryString", "startsWith", "slice", "URLSearchParams", "stripScheme", "url", "replace", "addClerkPrefix", "str", "regex", "match", "stripped", "getClerkJsMajorVersionOrTag", "pkgVersion", "split", "getScriptUrl", "clerkJ<PERSON><PERSON><PERSON>", "noSchemeFrontendApi", "major"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\utils\\instance.ts", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\url.ts"], "sourcesContent": ["/**\n * Check if the frontendApi ends with a staging domain\n */\nexport function isStaging(frontendApi: string): boolean {\n  return (\n    frontendApi.endsWith('.lclstage.dev') ||\n    frontendApi.endsWith('.stgstage.dev') ||\n    frontendApi.endsWith('.clerkstage.dev') ||\n    frontendApi.endsWith('.accountsstage.dev')\n  );\n}\n", "import { isStaging } from './utils/instance';\n\nexport function parseSearchParams(queryString = ''): URLSearchParams {\n  if (queryString.startsWith('?')) {\n    queryString = queryString.slice(1);\n  }\n  return new URLSearchParams(queryString);\n}\n\nexport function stripScheme(url = ''): string {\n  return (url || '').replace(/^.+:\\/\\//, '');\n}\n\nexport function addClerkPrefix(str: string | undefined) {\n  if (!str) {\n    return '';\n  }\n  let regex;\n  if (str.match(/^(clerk\\.)+\\w*$/)) {\n    regex = /(clerk\\.)*(?=clerk\\.)/;\n  } else if (str.match(/\\.clerk.accounts/)) {\n    return str;\n  } else {\n    regex = /^(clerk\\.)*/gi;\n  }\n\n  const stripped = str.replace(regex, '');\n  return `clerk.${stripped}`;\n}\n\n/**\n *\n * Retrieve the clerk-js major tag using the major version from the pkgVersion\n * param or use the frontendApi to determine if the canary tag should be used.\n * The default tag is `latest`.\n */\nexport const getClerkJsMajorVersionOrTag = (frontendApi: string, pkgVersion?: string) => {\n  if (!pkgVersion && isStaging(frontendApi)) {\n    return 'canary';\n  }\n\n  if (!pkgVersion) {\n    return 'latest';\n  }\n\n  return pkgVersion.split('.')[0] || 'latest';\n};\n\n/**\n *\n * Retrieve the clerk-js script url from the frontendApi and the major tag\n * using the {@link getClerkJsMajorVersionOrTag} or a provided clerkJSVersion tag.\n */\nexport const getScriptUrl = (\n  frontendApi: string,\n  { pkgVersion = CLERK_JS_PACKAGE_VERSION, clerkJSVersion }: { pkgVersion?: string; clerkJSVersion?: string },\n) => {\n  const noSchemeFrontendApi = frontendApi.replace(/http(s)?:\\/\\//, '');\n  const major = getClerkJsMajorVersionOrTag(frontendApi, pkgVersion);\n  return `https://${noSchemeFrontendApi}/npm/@clerk/clerk-js@${clerkJSVersion || major}/dist/clerk.browser.js`;\n};\n"], "mappings": ";AAGO,SAASA,UAAUC,WAAA,EAA8B;EACtD,OACEA,WAAA,CAAYC,QAAA,CAAS,eAAe,KACpCD,WAAA,CAAYC,QAAA,CAAS,eAAe,KACpCD,WAAA,CAAYC,QAAA,CAAS,iBAAiB,KACtCD,WAAA,CAAYC,QAAA,CAAS,oBAAoB;AAE7C;;;ACRO,SAASC,kBAAkBC,WAAA,GAAc,IAAqB;EACnE,IAAIA,WAAA,CAAYC,UAAA,CAAW,GAAG,GAAG;IAC/BD,WAAA,GAAcA,WAAA,CAAYE,KAAA,CAAM,CAAC;EACnC;EACA,OAAO,IAAIC,eAAA,CAAgBH,WAAW;AACxC;AAEO,SAASI,YAAYC,GAAA,GAAM,IAAY;EAC5C,QAAQA,GAAA,IAAO,IAAIC,OAAA,CAAQ,YAAY,EAAE;AAC3C;AAEO,SAASC,eAAeC,GAAA,EAAyB;EACtD,IAAI,CAACA,GAAA,EAAK;IACR,OAAO;EACT;EACA,IAAIC,KAAA;EACJ,IAAID,GAAA,CAAIE,KAAA,CAAM,iBAAiB,GAAG;IAChCD,KAAA,GAAQ;EACV,WAAWD,GAAA,CAAIE,KAAA,CAAM,kBAAkB,GAAG;IACxC,OAAOF,GAAA;EACT,OAAO;IACLC,KAAA,GAAQ;EACV;EAEA,MAAME,QAAA,GAAWH,GAAA,CAAIF,OAAA,CAAQG,KAAA,EAAO,EAAE;EACtC,OAAO,SAASE,QAAQ;AAC1B;AAQO,IAAMC,2BAAA,GAA8BA,CAACf,WAAA,EAAqBgB,UAAA,KAAwB;EACvF,IAAI,CAACA,UAAA,IAAcjB,SAAA,CAAUC,WAAW,GAAG;IACzC,OAAO;EACT;EAEA,IAAI,CAACgB,UAAA,EAAY;IACf,OAAO;EACT;EAEA,OAAOA,UAAA,CAAWC,KAAA,CAAM,GAAG,EAAE,CAAC,KAAK;AACrC;AAOO,IAAMC,YAAA,GAAeA,CAC1BlB,WAAA,EACA;EAAEgB,UAAA,GAAa;EAA0BG;AAAe,MACrD;EACH,MAAMC,mBAAA,GAAsBpB,WAAA,CAAYS,OAAA,CAAQ,iBAAiB,EAAE;EACnE,MAAMY,KAAA,GAAQN,2BAAA,CAA4Bf,WAAA,EAAagB,UAAU;EACjE,OAAO,WAAWI,mBAAmB,wBAAwBD,cAAA,IAAkBE,KAAK;AACtF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}