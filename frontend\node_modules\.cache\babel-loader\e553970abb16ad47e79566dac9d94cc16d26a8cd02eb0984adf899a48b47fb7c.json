{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport { logErrorInDevMode } from \"@clerk/shared\";\nimport React, { createElement } from \"react\";\nimport { organizationProfileLinkRenderedError, organizationProfilePageRenderedError, userProfileLinkRenderedError, userProfilePageRenderedError } from \"../errors\";\nimport { useOrganizationProfileCustomPages, useUserProfileCustomPages } from \"../utils\";\nimport { withClerk } from \"./withClerk\";\nconst isMountProps = props => {\n  return \"mount\" in props;\n};\nconst isOpenProps = props => {\n  return \"open\" in props;\n};\nclass Portal extends React.PureComponent {\n  constructor() {\n    super(...arguments);\n    this.portalRef = React.createRef();\n  }\n  componentDidUpdate(prevProps) {\n    var _a, _b, _c, _d;\n    if (!isMountProps(prevProps) || !isMountProps(this.props)) {\n      return;\n    }\n    if (prevProps.props.appearance !== this.props.props.appearance || ((_b = (_a = prevProps.props) == null ? void 0 : _a.customPages) == null ? void 0 : _b.length) !== ((_d = (_c = this.props.props) == null ? void 0 : _c.customPages) == null ? void 0 : _d.length)) {\n      this.props.updateProps({\n        node: this.portalRef.current,\n        props: this.props.props\n      });\n    }\n  }\n  componentDidMount() {\n    if (this.portalRef.current) {\n      if (isMountProps(this.props)) {\n        this.props.mount(this.portalRef.current, this.props.props);\n      }\n      if (isOpenProps(this.props)) {\n        this.props.open(this.props.props);\n      }\n    }\n  }\n  componentWillUnmount() {\n    if (this.portalRef.current) {\n      if (isMountProps(this.props)) {\n        this.props.unmount(this.portalRef.current);\n      }\n      if (isOpenProps(this.props)) {\n        this.props.close();\n      }\n    }\n  }\n  render() {\n    var _a, _b;\n    return /* @__PURE__ */React.createElement(React.Fragment, null, /* @__PURE__ */React.createElement(\"div\", {\n      ref: this.portalRef\n    }), isMountProps(this.props) && ((_b = (_a = this.props) == null ? void 0 : _a.customPagesPortals) == null ? void 0 : _b.map((portal, index) => createElement(portal, {\n      key: index\n    }))));\n  }\n}\nconst SignIn = withClerk(({\n  clerk,\n  ...props\n}) => {\n  return /* @__PURE__ */React.createElement(Portal, {\n    mount: clerk.mountSignIn,\n    unmount: clerk.unmountSignIn,\n    updateProps: clerk.__unstable__updateProps,\n    props\n  });\n}, \"SignIn\");\nconst SignUp = withClerk(({\n  clerk,\n  ...props\n}) => {\n  return /* @__PURE__ */React.createElement(Portal, {\n    mount: clerk.mountSignUp,\n    unmount: clerk.unmountSignUp,\n    updateProps: clerk.__unstable__updateProps,\n    props\n  });\n}, \"SignUp\");\nfunction UserProfilePage({\n  children\n}) {\n  logErrorInDevMode(userProfilePageRenderedError);\n  return /* @__PURE__ */React.createElement(React.Fragment, null, children);\n}\nfunction UserProfileLink({\n  children\n}) {\n  logErrorInDevMode(userProfileLinkRenderedError);\n  return /* @__PURE__ */React.createElement(React.Fragment, null, children);\n}\nconst _UserProfile = withClerk(({\n  clerk,\n  ...props\n}) => {\n  const {\n    customPages,\n    customPagesPortals\n  } = useUserProfileCustomPages(props.children);\n  return /* @__PURE__ */React.createElement(Portal, {\n    mount: clerk.mountUserProfile,\n    unmount: clerk.unmountUserProfile,\n    updateProps: clerk.__unstable__updateProps,\n    props: {\n      ...props,\n      customPages\n    },\n    customPagesPortals\n  });\n}, \"UserProfile\");\nconst UserProfile = Object.assign(_UserProfile, {\n  Page: UserProfilePage,\n  Link: UserProfileLink\n});\nconst _UserButton = withClerk(({\n  clerk,\n  ...props\n}) => {\n  const {\n    customPages,\n    customPagesPortals\n  } = useUserProfileCustomPages(props.children);\n  const userProfileProps = Object.assign(props.userProfileProps || {}, {\n    customPages\n  });\n  return /* @__PURE__ */React.createElement(Portal, {\n    mount: clerk.mountUserButton,\n    unmount: clerk.unmountUserButton,\n    updateProps: clerk.__unstable__updateProps,\n    props: {\n      ...props,\n      userProfileProps\n    },\n    customPagesPortals\n  });\n}, \"UserButton\");\nconst UserButton = Object.assign(_UserButton, {\n  UserProfilePage,\n  UserProfileLink\n});\nfunction OrganizationProfilePage({\n  children\n}) {\n  logErrorInDevMode(organizationProfilePageRenderedError);\n  return /* @__PURE__ */React.createElement(React.Fragment, null, children);\n}\nfunction OrganizationProfileLink({\n  children\n}) {\n  logErrorInDevMode(organizationProfileLinkRenderedError);\n  return /* @__PURE__ */React.createElement(React.Fragment, null, children);\n}\nconst _OrganizationProfile = withClerk(({\n  clerk,\n  ...props\n}) => {\n  const {\n    customPages,\n    customPagesPortals\n  } = useOrganizationProfileCustomPages(props.children);\n  return /* @__PURE__ */React.createElement(Portal, {\n    mount: clerk.mountOrganizationProfile,\n    unmount: clerk.unmountOrganizationProfile,\n    updateProps: clerk.__unstable__updateProps,\n    props: {\n      ...props,\n      customPages\n    },\n    customPagesPortals\n  });\n}, \"OrganizationProfile\");\nconst OrganizationProfile = Object.assign(_OrganizationProfile, {\n  Page: OrganizationProfilePage,\n  Link: OrganizationProfileLink\n});\nconst CreateOrganization = withClerk(({\n  clerk,\n  ...props\n}) => {\n  return /* @__PURE__ */React.createElement(Portal, {\n    mount: clerk.mountCreateOrganization,\n    unmount: clerk.unmountCreateOrganization,\n    updateProps: clerk.__unstable__updateProps,\n    props\n  });\n}, \"CreateOrganization\");\nconst _OrganizationSwitcher = withClerk(({\n  clerk,\n  ...props\n}) => {\n  const {\n    customPages,\n    customPagesPortals\n  } = useOrganizationProfileCustomPages(props.children);\n  const organizationProfileProps = Object.assign(props.organizationProfileProps || {}, {\n    customPages\n  });\n  return /* @__PURE__ */React.createElement(Portal, {\n    mount: clerk.mountOrganizationSwitcher,\n    unmount: clerk.unmountOrganizationSwitcher,\n    updateProps: clerk.__unstable__updateProps,\n    props: {\n      ...props,\n      organizationProfileProps\n    },\n    customPagesPortals\n  });\n}, \"OrganizationSwitcher\");\nconst OrganizationSwitcher = Object.assign(_OrganizationSwitcher, {\n  OrganizationProfilePage,\n  OrganizationProfileLink\n});\nconst OrganizationList = withClerk(({\n  clerk,\n  ...props\n}) => {\n  return /* @__PURE__ */React.createElement(Portal, {\n    mount: clerk.mountOrganizationList,\n    unmount: clerk.unmountOrganizationList,\n    updateProps: clerk.__unstable__updateProps,\n    props\n  });\n}, \"OrganizationList\");\nconst GoogleOneTap = withClerk(({\n  clerk,\n  ...props\n}) => {\n  return /* @__PURE__ */React.createElement(Portal, {\n    open: clerk.openGoogleOneTap,\n    close: clerk.closeGoogleOneTap,\n    props\n  });\n}, \"GoogleOneTap\");\nexport { CreateOrganization, GoogleOneTap, OrganizationList, OrganizationProfile, OrganizationProfileLink, OrganizationProfilePage, OrganizationSwitcher, SignIn, SignUp, UserButton, UserProfile, UserProfileLink, UserProfilePage };", "map": {"version": 3, "names": ["logErrorInDevMode", "React", "createElement", "organizationProfileLinkRenderedError", "organizationProfilePageRenderedError", "userProfileLinkRenderedError", "userProfilePageRenderedError", "useOrganizationProfileCustomPages", "useUserProfileCustomPages", "with<PERSON><PERSON><PERSON>", "isMountProps", "props", "isOpenProps", "Portal", "PureComponent", "constructor", "arguments", "portalRef", "createRef", "componentDidUpdate", "prevProps", "_a", "_b", "_c", "_d", "appearance", "customPages", "length", "updateProps", "node", "current", "componentDidMount", "mount", "open", "componentWillUnmount", "unmount", "close", "render", "Fragment", "ref", "customPagesPortals", "map", "portal", "index", "key", "SignIn", "clerk", "mountSignIn", "unmountSignIn", "__unstable__updateProps", "SignUp", "mountSignUp", "unmountSignUp", "UserProfilePage", "children", "UserProfileLink", "_UserProfile", "mountUserProfile", "unmountUserProfile", "UserProfile", "Object", "assign", "Page", "Link", "_UserButton", "userProfileProps", "mountUserButton", "unmountUser<PERSON><PERSON><PERSON>", "UserButton", "OrganizationProfilePage", "OrganizationProfileLink", "_OrganizationProfile", "mountOrganizationProfile", "unmountOrganizationProfile", "OrganizationProfile", "CreateOrganization", "mountCreateOrganization", "unmountCreateOrganization", "_OrganizationSwitcher", "organizationProfileProps", "mountOrganizationSwitcher", "unmountOrganizationSwitcher", "OrganizationSwitcher", "OrganizationList", "mountOrganizationList", "unmountOrganizationList", "GoogleOneTap", "openGoogleOneTap", "closeGoogleOneTap"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\components\\uiComponents.tsx"], "sourcesContent": ["import { logErrorInDevMode } from '@clerk/shared';\nimport type {\n  CreateOrganizationProps,\n  GoogleOneTapProps,\n  OrganizationListProps,\n  OrganizationProfileProps,\n  OrganizationSwitcherProps,\n  SignInProps,\n  SignUpProps,\n  UserButtonProps,\n  UserProfileProps,\n} from '@clerk/types';\nimport type { PropsWithChildren } from 'react';\nimport React, { createElement } from 'react';\n\nimport {\n  organizationProfileLinkRenderedError,\n  organizationProfilePageRenderedError,\n  userProfileLinkRenderedError,\n  userProfilePageRenderedError,\n} from '../errors';\nimport type {\n  MountProps,\n  OpenProps,\n  OrganizationProfileLinkProps,\n  OrganizationProfilePageProps,\n  UserProfileLinkProps,\n  UserProfilePageProps,\n  WithClerkProp,\n} from '../types';\nimport { useOrganizationProfileCustomPages, useUserProfileCustomPages } from '../utils';\nimport { withClerk } from './withClerk';\n\ntype UserProfileExportType = typeof _UserProfile & {\n  Page: typeof UserProfilePage;\n  Link: typeof UserProfileLink;\n};\n\ntype UserButtonExportType = typeof _UserButton & {\n  UserProfilePage: typeof UserProfilePage;\n  UserProfileLink: typeof UserProfileLink;\n};\n\ntype UserButtonPropsWithoutCustomPages = Omit<UserButtonProps, 'userProfileProps'> & {\n  userProfileProps?: Pick<UserProfileProps, 'additionalOAuthScopes' | 'appearance'>;\n};\n\ntype OrganizationProfileExportType = typeof _OrganizationProfile & {\n  Page: typeof OrganizationProfilePage;\n  Link: typeof OrganizationProfileLink;\n};\n\ntype OrganizationSwitcherExportType = typeof _OrganizationSwitcher & {\n  OrganizationProfilePage: typeof OrganizationProfilePage;\n  OrganizationProfileLink: typeof OrganizationProfileLink;\n};\n\ntype OrganizationSwitcherPropsWithoutCustomPages = Omit<OrganizationSwitcherProps, 'organizationProfileProps'> & {\n  organizationProfileProps?: Pick<OrganizationProfileProps, 'appearance'>;\n};\n\n// README: <Portal/> should be a class pure component in order for mount and unmount\n// lifecycle props to be invoked correctly. Replacing the class component with a\n// functional component wrapped with a React.memo is not identical to the original\n// class implementation due to React intricacies such as the useEffect’s cleanup\n// seems to run AFTER unmount, while componentWillUnmount runs BEFORE.\n\n// More information can be found at https://clerkinc.slack.com/archives/C015S0BGH8R/p1624891993016300\n\n// The function Portal implementation is commented out for future reference.\n\n// const Portal = React.memo(({ props, mount, unmount }: MountProps) => {\n//   const portalRef = React.createRef<HTMLDivElement>();\n\n//   useEffect(() => {\n//     if (portalRef.current) {\n//       mount(portalRef.current, props);\n//     }\n//     return () => {\n//       if (portalRef.current) {\n//         unmount(portalRef.current);\n//       }\n//     };\n//   }, []);\n\n//   return <div ref={portalRef} />;\n// });\n\n// Portal.displayName = 'ClerkPortal';\n\nconst isMountProps = (props: any): props is MountProps => {\n  return 'mount' in props;\n};\n\nconst isOpenProps = (props: any): props is OpenProps => {\n  return 'open' in props;\n};\n\nclass Portal extends React.PureComponent<MountProps | OpenProps> {\n  private portalRef = React.createRef<HTMLDivElement>();\n\n  componentDidUpdate(prevProps: Readonly<MountProps | OpenProps>) {\n    if (!isMountProps(prevProps) || !isMountProps(this.props)) {\n      return;\n    }\n\n    if (\n      prevProps.props.appearance !== this.props.props.appearance ||\n      prevProps.props?.customPages?.length !== this.props.props?.customPages?.length\n    ) {\n      this.props.updateProps({ node: this.portalRef.current, props: this.props.props });\n    }\n  }\n\n  componentDidMount() {\n    if (this.portalRef.current) {\n      if (isMountProps(this.props)) {\n        this.props.mount(this.portalRef.current, this.props.props);\n      }\n\n      if (isOpenProps(this.props)) {\n        this.props.open(this.props.props);\n      }\n    }\n  }\n\n  componentWillUnmount() {\n    if (this.portalRef.current) {\n      if (isMountProps(this.props)) {\n        this.props.unmount(this.portalRef.current);\n      }\n      if (isOpenProps(this.props)) {\n        this.props.close();\n      }\n    }\n  }\n\n  render() {\n    return (\n      <>\n        <div ref={this.portalRef} />\n        {isMountProps(this.props) &&\n          this.props?.customPagesPortals?.map((portal, index) => createElement(portal, { key: index }))}\n      </>\n    );\n  }\n}\n\nexport const SignIn = withClerk(({ clerk, ...props }: WithClerkProp<SignInProps>) => {\n  return (\n    <Portal\n      mount={clerk.mountSignIn}\n      unmount={clerk.unmountSignIn}\n      updateProps={(clerk as any).__unstable__updateProps}\n      props={props}\n    />\n  );\n}, 'SignIn');\n\nexport const SignUp = withClerk(({ clerk, ...props }: WithClerkProp<SignUpProps>) => {\n  return (\n    <Portal\n      mount={clerk.mountSignUp}\n      unmount={clerk.unmountSignUp}\n      updateProps={(clerk as any).__unstable__updateProps}\n      props={props}\n    />\n  );\n}, 'SignUp');\n\nexport function UserProfilePage({ children }: PropsWithChildren<UserProfilePageProps>) {\n  logErrorInDevMode(userProfilePageRenderedError);\n  return <>{children}</>;\n}\n\nexport function UserProfileLink({ children }: PropsWithChildren<UserProfileLinkProps>) {\n  logErrorInDevMode(userProfileLinkRenderedError);\n  return <>{children}</>;\n}\n\nconst _UserProfile = withClerk(\n  ({ clerk, ...props }: WithClerkProp<PropsWithChildren<Omit<UserProfileProps, 'customPages'>>>) => {\n    const { customPages, customPagesPortals } = useUserProfileCustomPages(props.children);\n    return (\n      <Portal\n        mount={clerk.mountUserProfile}\n        unmount={clerk.unmountUserProfile}\n        updateProps={(clerk as any).__unstable__updateProps}\n        props={{ ...props, customPages }}\n        customPagesPortals={customPagesPortals}\n      />\n    );\n  },\n  'UserProfile',\n);\n\nexport const UserProfile: UserProfileExportType = Object.assign(_UserProfile, {\n  Page: UserProfilePage,\n  Link: UserProfileLink,\n});\n\nconst _UserButton = withClerk(\n  ({ clerk, ...props }: WithClerkProp<PropsWithChildren<UserButtonPropsWithoutCustomPages>>) => {\n    const { customPages, customPagesPortals } = useUserProfileCustomPages(props.children);\n    const userProfileProps = Object.assign(props.userProfileProps || {}, { customPages });\n    return (\n      <Portal\n        mount={clerk.mountUserButton}\n        unmount={clerk.unmountUserButton}\n        updateProps={(clerk as any).__unstable__updateProps}\n        props={{ ...props, userProfileProps }}\n        customPagesPortals={customPagesPortals}\n      />\n    );\n  },\n  'UserButton',\n);\n\nexport const UserButton: UserButtonExportType = Object.assign(_UserButton, {\n  UserProfilePage,\n  UserProfileLink,\n});\n\nexport function OrganizationProfilePage({ children }: PropsWithChildren<OrganizationProfilePageProps>) {\n  logErrorInDevMode(organizationProfilePageRenderedError);\n  return <>{children}</>;\n}\n\nexport function OrganizationProfileLink({ children }: PropsWithChildren<OrganizationProfileLinkProps>) {\n  logErrorInDevMode(organizationProfileLinkRenderedError);\n  return <>{children}</>;\n}\n\nconst _OrganizationProfile = withClerk(\n  ({ clerk, ...props }: WithClerkProp<PropsWithChildren<Omit<OrganizationProfileProps, 'customPages'>>>) => {\n    const { customPages, customPagesPortals } = useOrganizationProfileCustomPages(props.children);\n    return (\n      <Portal\n        mount={clerk.mountOrganizationProfile}\n        unmount={clerk.unmountOrganizationProfile}\n        updateProps={(clerk as any).__unstable__updateProps}\n        props={{ ...props, customPages }}\n        customPagesPortals={customPagesPortals}\n      />\n    );\n  },\n  'OrganizationProfile',\n);\n\nexport const OrganizationProfile: OrganizationProfileExportType = Object.assign(_OrganizationProfile, {\n  Page: OrganizationProfilePage,\n  Link: OrganizationProfileLink,\n});\n\nexport const CreateOrganization = withClerk(({ clerk, ...props }: WithClerkProp<CreateOrganizationProps>) => {\n  return (\n    <Portal\n      mount={clerk.mountCreateOrganization}\n      unmount={clerk.unmountCreateOrganization}\n      updateProps={(clerk as any).__unstable__updateProps}\n      props={props}\n    />\n  );\n}, 'CreateOrganization');\n\nconst _OrganizationSwitcher = withClerk(\n  ({ clerk, ...props }: WithClerkProp<PropsWithChildren<OrganizationSwitcherPropsWithoutCustomPages>>) => {\n    const { customPages, customPagesPortals } = useOrganizationProfileCustomPages(props.children);\n    const organizationProfileProps = Object.assign(props.organizationProfileProps || {}, { customPages });\n    return (\n      <Portal\n        mount={clerk.mountOrganizationSwitcher}\n        unmount={clerk.unmountOrganizationSwitcher}\n        updateProps={(clerk as any).__unstable__updateProps}\n        props={{ ...props, organizationProfileProps }}\n        customPagesPortals={customPagesPortals}\n      />\n    );\n  },\n  'OrganizationSwitcher',\n);\n\nexport const OrganizationSwitcher: OrganizationSwitcherExportType = Object.assign(_OrganizationSwitcher, {\n  OrganizationProfilePage,\n  OrganizationProfileLink,\n});\n\nexport const OrganizationList = withClerk(({ clerk, ...props }: WithClerkProp<OrganizationListProps>) => {\n  return (\n    <Portal\n      mount={clerk.mountOrganizationList}\n      unmount={clerk.unmountOrganizationList}\n      updateProps={(clerk as any).__unstable__updateProps}\n      props={props}\n    />\n  );\n}, 'OrganizationList');\n\nexport const GoogleOneTap = withClerk(({ clerk, ...props }: WithClerkProp<GoogleOneTapProps>) => {\n  return (\n    <Portal\n      open={clerk.openGoogleOneTap}\n      close={clerk.closeGoogleOneTap}\n      props={props}\n    />\n  );\n}, 'GoogleOneTap');\n"], "mappings": ";AAAA,SAASA,iBAAA,QAAyB;AAalC,OAAOC,KAAA,IAASC,aAAA,QAAqB;AAErC,SACEC,oCAAA,EACAC,oCAAA,EACAC,4BAAA,EACAC,4BAAA,QACK;AAUP,SAASC,iCAAA,EAAmCC,yBAAA,QAAiC;AAC7E,SAASC,SAAA,QAAiB;AA2D1B,MAAMC,YAAA,GAAgBC,KAAA,IAAoC;EACxD,OAAO,WAAWA,KAAA;AACpB;AAEA,MAAMC,WAAA,GAAeD,KAAA,IAAmC;EACtD,OAAO,UAAUA,KAAA;AACnB;AAEA,MAAME,MAAA,SAAeZ,KAAA,CAAMa,aAAA,CAAsC;EAAjEC,YAAA;IAAA,SAAAC,SAAA;IACE,KAAQC,SAAA,GAAYhB,KAAA,CAAMiB,SAAA,CAA0B;EAAA;EAEpDC,mBAAmBC,SAAA,EAA6C;IArGlE,IAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;IAsGI,IAAI,CAACd,YAAA,CAAaU,SAAS,KAAK,CAACV,YAAA,CAAa,KAAKC,KAAK,GAAG;MACzD;IACF;IAEA,IACES,SAAA,CAAUT,KAAA,CAAMc,UAAA,KAAe,KAAKd,KAAA,CAAMA,KAAA,CAAMc,UAAA,MAChDH,EAAA,IAAAD,EAAA,GAAAD,SAAA,CAAUT,KAAA,KAAV,gBAAAU,EAAA,CAAiBK,WAAA,KAAjB,gBAAAJ,EAAA,CAA8BK,MAAA,QAAWH,EAAA,IAAAD,EAAA,QAAKZ,KAAA,CAAMA,KAAA,KAAX,gBAAAY,EAAA,CAAkBG,WAAA,KAAlB,gBAAAF,EAAA,CAA+BG,MAAA,GACxE;MACA,KAAKhB,KAAA,CAAMiB,WAAA,CAAY;QAAEC,IAAA,EAAM,KAAKZ,SAAA,CAAUa,OAAA;QAASnB,KAAA,EAAO,KAAKA,KAAA,CAAMA;MAAM,CAAC;IAClF;EACF;EAEAoB,kBAAA,EAAoB;IAClB,IAAI,KAAKd,SAAA,CAAUa,OAAA,EAAS;MAC1B,IAAIpB,YAAA,CAAa,KAAKC,KAAK,GAAG;QAC5B,KAAKA,KAAA,CAAMqB,KAAA,CAAM,KAAKf,SAAA,CAAUa,OAAA,EAAS,KAAKnB,KAAA,CAAMA,KAAK;MAC3D;MAEA,IAAIC,WAAA,CAAY,KAAKD,KAAK,GAAG;QAC3B,KAAKA,KAAA,CAAMsB,IAAA,CAAK,KAAKtB,KAAA,CAAMA,KAAK;MAClC;IACF;EACF;EAEAuB,qBAAA,EAAuB;IACrB,IAAI,KAAKjB,SAAA,CAAUa,OAAA,EAAS;MAC1B,IAAIpB,YAAA,CAAa,KAAKC,KAAK,GAAG;QAC5B,KAAKA,KAAA,CAAMwB,OAAA,CAAQ,KAAKlB,SAAA,CAAUa,OAAO;MAC3C;MACA,IAAIlB,WAAA,CAAY,KAAKD,KAAK,GAAG;QAC3B,KAAKA,KAAA,CAAMyB,KAAA,CAAM;MACnB;IACF;EACF;EAEAC,OAAA,EAAS;IAzIX,IAAAhB,EAAA,EAAAC,EAAA;IA0II,OACE,eAAArB,KAAA,CAAAC,aAAA,CAAAD,KAAA,CAAAqC,QAAA,QACE,eAAArC,KAAA,CAAAC,aAAA,CAAC;MAAIqC,GAAA,EAAK,KAAKtB;IAAA,CAAW,GACzBP,YAAA,CAAa,KAAKC,KAAK,OACtBW,EAAA,IAAAD,EAAA,QAAKV,KAAA,KAAL,gBAAAU,EAAA,CAAYmB,kBAAA,KAAZ,gBAAAlB,EAAA,CAAgCmB,GAAA,CAAI,CAACC,MAAA,EAAQC,KAAA,KAAUzC,aAAA,CAAcwC,MAAA,EAAQ;MAAEE,GAAA,EAAKD;IAAM,CAAC,GAC/F;EAEJ;AACF;AAEO,MAAME,MAAA,GAASpC,SAAA,CAAU,CAAC;EAAEqC,KAAA;EAAO,GAAGnC;AAAM,MAAkC;EACnF,OACE,eAAAV,KAAA,CAAAC,aAAA,CAACW,MAAA;IACCmB,KAAA,EAAOc,KAAA,CAAMC,WAAA;IACbZ,OAAA,EAASW,KAAA,CAAME,aAAA;IACfpB,WAAA,EAAckB,KAAA,CAAcG,uBAAA;IAC5BtC;EAAA,CACF;AAEJ,GAAG,QAAQ;AAEJ,MAAMuC,MAAA,GAASzC,SAAA,CAAU,CAAC;EAAEqC,KAAA;EAAO,GAAGnC;AAAM,MAAkC;EACnF,OACE,eAAAV,KAAA,CAAAC,aAAA,CAACW,MAAA;IACCmB,KAAA,EAAOc,KAAA,CAAMK,WAAA;IACbhB,OAAA,EAASW,KAAA,CAAMM,aAAA;IACfxB,WAAA,EAAckB,KAAA,CAAcG,uBAAA;IAC5BtC;EAAA,CACF;AAEJ,GAAG,QAAQ;AAEJ,SAAS0C,gBAAgB;EAAEC;AAAS,GAA4C;EACrFtD,iBAAA,CAAkBM,4BAA4B;EAC9C,OAAO,eAAAL,KAAA,CAAAC,aAAA,CAAAD,KAAA,CAAAqC,QAAA,QAAGgB,QAAS;AACrB;AAEO,SAASC,gBAAgB;EAAED;AAAS,GAA4C;EACrFtD,iBAAA,CAAkBK,4BAA4B;EAC9C,OAAO,eAAAJ,KAAA,CAAAC,aAAA,CAAAD,KAAA,CAAAqC,QAAA,QAAGgB,QAAS;AACrB;AAEA,MAAME,YAAA,GAAe/C,SAAA,CACnB,CAAC;EAAEqC,KAAA;EAAO,GAAGnC;AAAM,MAA+E;EAChG,MAAM;IAAEe,WAAA;IAAac;EAAmB,IAAIhC,yBAAA,CAA0BG,KAAA,CAAM2C,QAAQ;EACpF,OACE,eAAArD,KAAA,CAAAC,aAAA,CAACW,MAAA;IACCmB,KAAA,EAAOc,KAAA,CAAMW,gBAAA;IACbtB,OAAA,EAASW,KAAA,CAAMY,kBAAA;IACf9B,WAAA,EAAckB,KAAA,CAAcG,uBAAA;IAC5BtC,KAAA,EAAO;MAAE,GAAGA,KAAA;MAAOe;IAAY;IAC/Bc;EAAA,CACF;AAEJ,GACA,aACF;AAEO,MAAMmB,WAAA,GAAqCC,MAAA,CAAOC,MAAA,CAAOL,YAAA,EAAc;EAC5EM,IAAA,EAAMT,eAAA;EACNU,IAAA,EAAMR;AACR,CAAC;AAED,MAAMS,WAAA,GAAcvD,SAAA,CAClB,CAAC;EAAEqC,KAAA;EAAO,GAAGnC;AAAM,MAA2E;EAC5F,MAAM;IAAEe,WAAA;IAAac;EAAmB,IAAIhC,yBAAA,CAA0BG,KAAA,CAAM2C,QAAQ;EACpF,MAAMW,gBAAA,GAAmBL,MAAA,CAAOC,MAAA,CAAOlD,KAAA,CAAMsD,gBAAA,IAAoB,CAAC,GAAG;IAAEvC;EAAY,CAAC;EACpF,OACE,eAAAzB,KAAA,CAAAC,aAAA,CAACW,MAAA;IACCmB,KAAA,EAAOc,KAAA,CAAMoB,eAAA;IACb/B,OAAA,EAASW,KAAA,CAAMqB,iBAAA;IACfvC,WAAA,EAAckB,KAAA,CAAcG,uBAAA;IAC5BtC,KAAA,EAAO;MAAE,GAAGA,KAAA;MAAOsD;IAAiB;IACpCzB;EAAA,CACF;AAEJ,GACA,YACF;AAEO,MAAM4B,UAAA,GAAmCR,MAAA,CAAOC,MAAA,CAAOG,WAAA,EAAa;EACzEX,eAAA;EACAE;AACF,CAAC;AAEM,SAASc,wBAAwB;EAAEf;AAAS,GAAoD;EACrGtD,iBAAA,CAAkBI,oCAAoC;EACtD,OAAO,eAAAH,KAAA,CAAAC,aAAA,CAAAD,KAAA,CAAAqC,QAAA,QAAGgB,QAAS;AACrB;AAEO,SAASgB,wBAAwB;EAAEhB;AAAS,GAAoD;EACrGtD,iBAAA,CAAkBG,oCAAoC;EACtD,OAAO,eAAAF,KAAA,CAAAC,aAAA,CAAAD,KAAA,CAAAqC,QAAA,QAAGgB,QAAS;AACrB;AAEA,MAAMiB,oBAAA,GAAuB9D,SAAA,CAC3B,CAAC;EAAEqC,KAAA;EAAO,GAAGnC;AAAM,MAAuF;EACxG,MAAM;IAAEe,WAAA;IAAac;EAAmB,IAAIjC,iCAAA,CAAkCI,KAAA,CAAM2C,QAAQ;EAC5F,OACE,eAAArD,KAAA,CAAAC,aAAA,CAACW,MAAA;IACCmB,KAAA,EAAOc,KAAA,CAAM0B,wBAAA;IACbrC,OAAA,EAASW,KAAA,CAAM2B,0BAAA;IACf7C,WAAA,EAAckB,KAAA,CAAcG,uBAAA;IAC5BtC,KAAA,EAAO;MAAE,GAAGA,KAAA;MAAOe;IAAY;IAC/Bc;EAAA,CACF;AAEJ,GACA,qBACF;AAEO,MAAMkC,mBAAA,GAAqDd,MAAA,CAAOC,MAAA,CAAOU,oBAAA,EAAsB;EACpGT,IAAA,EAAMO,uBAAA;EACNN,IAAA,EAAMO;AACR,CAAC;AAEM,MAAMK,kBAAA,GAAqBlE,SAAA,CAAU,CAAC;EAAEqC,KAAA;EAAO,GAAGnC;AAAM,MAA8C;EAC3G,OACE,eAAAV,KAAA,CAAAC,aAAA,CAACW,MAAA;IACCmB,KAAA,EAAOc,KAAA,CAAM8B,uBAAA;IACbzC,OAAA,EAASW,KAAA,CAAM+B,yBAAA;IACfjD,WAAA,EAAckB,KAAA,CAAcG,uBAAA;IAC5BtC;EAAA,CACF;AAEJ,GAAG,oBAAoB;AAEvB,MAAMmE,qBAAA,GAAwBrE,SAAA,CAC5B,CAAC;EAAEqC,KAAA;EAAO,GAAGnC;AAAM,MAAqF;EACtG,MAAM;IAAEe,WAAA;IAAac;EAAmB,IAAIjC,iCAAA,CAAkCI,KAAA,CAAM2C,QAAQ;EAC5F,MAAMyB,wBAAA,GAA2BnB,MAAA,CAAOC,MAAA,CAAOlD,KAAA,CAAMoE,wBAAA,IAA4B,CAAC,GAAG;IAAErD;EAAY,CAAC;EACpG,OACE,eAAAzB,KAAA,CAAAC,aAAA,CAACW,MAAA;IACCmB,KAAA,EAAOc,KAAA,CAAMkC,yBAAA;IACb7C,OAAA,EAASW,KAAA,CAAMmC,2BAAA;IACfrD,WAAA,EAAckB,KAAA,CAAcG,uBAAA;IAC5BtC,KAAA,EAAO;MAAE,GAAGA,KAAA;MAAOoE;IAAyB;IAC5CvC;EAAA,CACF;AAEJ,GACA,sBACF;AAEO,MAAM0C,oBAAA,GAAuDtB,MAAA,CAAOC,MAAA,CAAOiB,qBAAA,EAAuB;EACvGT,uBAAA;EACAC;AACF,CAAC;AAEM,MAAMa,gBAAA,GAAmB1E,SAAA,CAAU,CAAC;EAAEqC,KAAA;EAAO,GAAGnC;AAAM,MAA4C;EACvG,OACE,eAAAV,KAAA,CAAAC,aAAA,CAACW,MAAA;IACCmB,KAAA,EAAOc,KAAA,CAAMsC,qBAAA;IACbjD,OAAA,EAASW,KAAA,CAAMuC,uBAAA;IACfzD,WAAA,EAAckB,KAAA,CAAcG,uBAAA;IAC5BtC;EAAA,CACF;AAEJ,GAAG,kBAAkB;AAEd,MAAM2E,YAAA,GAAe7E,SAAA,CAAU,CAAC;EAAEqC,KAAA;EAAO,GAAGnC;AAAM,MAAwC;EAC/F,OACE,eAAAV,KAAA,CAAAC,aAAA,CAACW,MAAA;IACCoB,IAAA,EAAMa,KAAA,CAAMyC,gBAAA;IACZnD,KAAA,EAAOU,KAAA,CAAM0C,iBAAA;IACb7E;EAAA,CACF;AAEJ,GAAG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}