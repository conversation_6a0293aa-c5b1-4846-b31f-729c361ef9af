#!/usr/bin/env python3
"""
用户管理系统集成测试
"""

import asyncio
import httpx
import json
import sys
import os
from dotenv import load_dotenv

load_dotenv()

# API 配置
API_BASE_URL = "http://localhost:8000"

# 模拟的 Clerk token（仅用于测试）
MOCK_CLERK_TOKEN = "mock_token_for_testing"

async def test_user_management_system():
    """测试用户管理系统"""
    print("🚀 开始测试用户管理系统...")
    
    # 测试用例
    test_cases = [
        {
            "name": "健康检查",
            "method": "GET",
            "url": f"{API_BASE_URL}/",
            "headers": {},
            "data": None
        },
        {
            "name": "用户登录/注册（需要真实 Clerk token）",
            "method": "POST", 
            "url": f"{API_BASE_URL}/api/users/auth/login",
            "headers": {"Authorization": f"Bearer {MOCK_CLERK_TOKEN}"},
            "data": None,
            "skip": True  # 跳过，因为需要真实的 Clerk token
        },
        {
            "name": "获取用户资料（需要认证）",
            "method": "GET",
            "url": f"{API_BASE_URL}/api/users/profile",
            "headers": {"Authorization": f"Bearer {MOCK_CLERK_TOKEN}"},
            "data": None,
            "skip": True  # 跳过，因为需要真实的 Clerk token
        },
        {
            "name": "聊天接口测试",
            "method": "POST",
            "url": f"{API_BASE_URL}/api/chat/",
            "headers": {"Content-Type": "application/json"},
            "data": {
                "user_id": "test_user_001",
                "message": "我今天感觉很开心，想看一些明亮的艺术作品"
            }
        },
        {
            "name": "推荐接口测试",
            "method": "POST",
            "url": f"{API_BASE_URL}/api/recommend/",
            "headers": {"Content-Type": "application/json"},
            "data": {
                "user_id": "test_user_001",
                "extracted_elements": {
                    "mood": "happy",
                    "emotion_intensity": "high",
                    "colors": ["yellow", "orange"],
                    "themes": ["nature"],
                    "styles": ["印象派"],
                    "is_recommendation_query": True,
                    "needs_guidance": False,
                    "is_malicious": False
                },
                "user_profile": {
                    "user_id": "test_user_001",
                    "preferences": {
                        "colors": ["blue", "green"],
                        "themes": ["nature", "landscape"],
                        "styles": ["印象派", "后印象派"]
                    },
                    "mood": "happy",
                    "last_updated": "2024-01-01T12:00:00"
                }
            }
        }
    ]
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📝 测试用例 {i}: {test_case['name']}")
            
            if test_case.get('skip'):
                print("⏭️  跳过测试（需要真实认证）")
                continue
            
            try:
                # 发送请求
                if test_case['method'] == 'GET':
                    response = await client.get(
                        test_case['url'],
                        headers=test_case['headers']
                    )
                elif test_case['method'] == 'POST':
                    response = await client.post(
                        test_case['url'],
                        headers=test_case['headers'],
                        json=test_case['data']
                    )
                
                # 检查状态码
                if response.status_code == 200:
                    print("✅ API 调用成功")
                    
                    # 解析响应
                    try:
                        result = response.json()
                        print(f"📄 响应数据:")
                        print(json.dumps(result, ensure_ascii=False, indent=2)[:500] + "...")
                    except:
                        print(f"📄 响应文本: {response.text[:200]}...")
                        
                elif response.status_code == 401:
                    print("🔐 需要认证（预期行为）")
                elif response.status_code == 500:
                    print("❌ 服务器内部错误")
                    print(f"   错误信息: {response.text}")
                else:
                    print(f"⚠️  意外状态码: HTTP {response.status_code}")
                    print(f"   响应: {response.text}")
                    
            except httpx.ConnectError:
                print("❌ 连接失败: 请确保后端服务正在运行")
                return False
            except httpx.TimeoutException:
                print("❌ 请求超时")
                return False
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                return False
    
    print("\n🎉 用户管理系统测试完成!")
    return True

async def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    
    try:
        from database.config import init_database, close_database, get_db_manager
        
        # 初始化数据库
        await init_database()
        print("✅ 数据库连接初始化成功")
        
        # 测试查询
        db_manager = get_db_manager()
        result = await db_manager.execute_query("SELECT 1 as test")
        print(f"✅ 数据库查询测试成功: {result}")
        
        # 关闭连接
        await close_database()
        print("✅ 数据库连接关闭成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {e}")
        return False

async def test_user_repository():
    """测试用户数据访问层"""
    print("🔍 测试用户数据访问层...")
    
    try:
        from database.config import init_database, close_database, get_db_manager
        from user_management.repository import UserRepository
        from user_management.models import CreateUserRequest
        
        # 初始化数据库
        await init_database()
        
        # 创建 Repository
        db_manager = get_db_manager()
        user_repo = UserRepository(db_manager)
        
        # 测试创建用户（使用测试数据）
        test_user_data = CreateUserRequest(
            clerk_user_id="test_clerk_123",
            email="<EMAIL>",
            username="testuser",
            first_name="Test",
            last_name="User"
        )
        
        print("📝 测试用户创建...")
        # 注意：这里可能会因为重复的 clerk_user_id 而失败，这是正常的
        try:
            user = await user_repo.create_user(test_user_data)
            print(f"✅ 用户创建成功: {user.email}")
        except Exception as e:
            if "already exists" in str(e) or "duplicate" in str(e).lower():
                print("ℹ️  用户已存在（预期行为）")
            else:
                raise e
        
        # 测试获取用户
        print("📝 测试用户查询...")
        user = await user_repo.get_user_by_clerk_id("test_clerk_123")
        if user:
            print(f"✅ 用户查询成功: {user.email}")
        else:
            print("ℹ️  用户不存在")
        
        # 关闭连接
        await close_database()
        
        return True
        
    except Exception as e:
        print(f"❌ 用户数据访问层测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("=" * 60)
    print("用户管理系统集成测试")
    print("=" * 60)
    
    # 检查环境变量
    required_vars = ["DB_HOST", "DB_NAME", "DB_USER"]
    missing_vars = [var for var in required_vars if not os.getenv(var)]
    
    if missing_vars:
        print("⚠️  警告: 缺少数据库环境变量，跳过数据库测试")
        print(f"   缺少: {missing_vars}")
        db_available = False
    else:
        db_available = True
    
    print("\n📋 测试计划:")
    print("1. API 接口测试")
    if db_available:
        print("2. 数据库连接测试")
        print("3. 用户数据访问层测试")
    print()
    
    # 测试 1: API 接口
    print("🔄 测试 1: API 接口测试...")
    api_success = await test_user_management_system()
    
    if db_available:
        # 测试 2: 数据库连接
        print("\n🔄 测试 2: 数据库连接测试...")
        db_success = await test_database_connection()
        
        # 测试 3: 用户数据访问层
        print("\n🔄 测试 3: 用户数据访问层测试...")
        repo_success = await test_user_repository()
    else:
        db_success = True
        repo_success = True
    
    # 总结
    print("\n" + "=" * 60)
    if api_success and db_success and repo_success:
        print("🎉 所有测试通过!")
        print("\n📝 系统状态:")
        print("✅ API 接口正常")
        if db_available:
            print("✅ 数据库连接正常")
            print("✅ 数据访问层正常")
        print("\n🚀 系统已准备就绪!")
    else:
        print("❌ 部分测试失败!")
        print("\n📝 失败的测试:")
        if not api_success:
            print("❌ API 接口测试")
        if not db_success:
            print("❌ 数据库连接测试")
        if not repo_success:
            print("❌ 数据访问层测试")

if __name__ == "__main__":
    asyncio.run(main())
