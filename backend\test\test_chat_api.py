#!/usr/bin/env python3
"""
测试 Chat API 的脚本
"""

import asyncio
import httpx
import json
import sys
import os
from dotenv import load_dotenv

load_dotenv()

# API 配置
API_BASE_URL = "http://localhost:8000"
CHAT_ENDPOINT = f"{API_BASE_URL}/api/chat/"

async def test_chat_api():
    """测试聊天 API"""
    print("🚀 开始测试 Chat API...")
    
    # 测试用例
    test_cases = [
        {
            "name": "空消息测试",
            "data": {
                "user_id": "test_user_001",
                "message": ""
            }
        },
        {
            "name": "简单问候测试",
            "data": {
                "user_id": "test_user_002", 
                "message": "你好"
            }
        },
        {
            "name": "情感表达测试",
            "data": {
                "user_id": "test_user_003",
                "message": "我今天感觉很开心，想看一些明亮的艺术作品"
            }
        },
        {
            "name": "艺术偏好测试",
            "data": {
                "user_id": "test_user_004",
                "message": "我喜欢蓝色和自然主题的画作，特别是印象派风格"
            }
        },
        {
            "name": "负面情绪测试",
            "data": {
                "user_id": "test_user_005",
                "message": "我有点难过，需要一些安慰"
            }
        }
    ]
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📝 测试用例 {i}: {test_case['name']}")
            print(f"   输入: {test_case['data']['message']}")
            
            try:
                # 发送请求
                response = await client.post(
                    CHAT_ENDPOINT,
                    json=test_case['data'],
                    headers={"Content-Type": "application/json"}
                )
                
                # 检查状态码
                if response.status_code == 200:
                    print("✅ API 调用成功")
                    
                    # 解析响应
                    result = response.json()
                    
                    # 验证响应结构
                    required_fields = ["user_id", "ai_response", "extracted_elements", "recommendation_triggered", "user_profile"]
                    missing_fields = []
                    
                    for field in required_fields:
                        if field not in result:
                            missing_fields.append(field)
                    
                    if missing_fields:
                        print(f"❌ 缺少必需字段: {missing_fields}")
                    else:
                        print("✅ 响应结构完整")
                    
                    # 显示关键信息
                    print(f"   用户ID: {result.get('user_id', 'N/A')}")
                    print(f"   AI回复: {result.get('ai_response', 'N/A')[:100]}...")
                    print(f"   推荐触发: {result.get('recommendation_triggered', False)}")
                    
                    # 显示提取的元素
                    extracted = result.get('extracted_elements', {})
                    if extracted:
                        print(f"   情绪: {extracted.get('mood', 'None')}")
                        print(f"   情感强度: {extracted.get('emotion_intensity', 'None')}")
                        print(f"   推荐查询: {extracted.get('is_recommendation_query', False)}")
                        print(f"   需要引导: {extracted.get('needs_guidance', True)}")
                    
                    # 显示用户画像
                    profile = result.get('user_profile', {})
                    if profile:
                        preferences = profile.get('preferences', {})
                        print(f"   用户偏好: {preferences}")
                        print(f"   用户情绪: {profile.get('mood', 'None')}")
                    
                    # 显示完整 JSON（可选）
                    print(f"\n📄 完整响应 JSON:")
                    print(json.dumps(result, ensure_ascii=False, indent=2))
                    
                else:
                    print(f"❌ API 调用失败: HTTP {response.status_code}")
                    print(f"   错误信息: {response.text}")
                    
            except httpx.ConnectError:
                print("❌ 连接失败: 请确保后端服务正在运行 (http://localhost:8000)")
                return False
            except httpx.TimeoutException:
                print("❌ 请求超时")
                return False
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                return False
    
    print("\n🎉 Chat API 测试完成!")
    return True

async def test_server_health():
    """测试服务器健康状态"""
    print("🔍 检查服务器状态...")
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(f"{API_BASE_URL}/")
            
            if response.status_code == 200:
                print("✅ 服务器运行正常")
                result = response.json()
                print(f"   消息: {result.get('message', 'N/A')}")
                return True
            else:
                print(f"❌ 服务器响应异常: HTTP {response.status_code}")
                return False
                
    except httpx.ConnectError:
        print("❌ 无法连接到服务器")
        print("   请确保后端服务正在运行:")
        print("   cd backend && python main.py")
        return False
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False

async def main():
    """主函数"""
    print("=" * 60)
    print("Chat API 测试脚本")
    print("=" * 60)
    
    # 检查环境变量
    if not os.getenv("GEMINI_API_KEY"):
        print("⚠️  警告: 未找到 GEMINI_API_KEY 环境变量")
        print("   请确保在 .env 文件中设置了 GEMINI_API_KEY")
    
    # 检查服务器状态
    server_ok = await test_server_health()
    if not server_ok:
        print("\n❌ 服务器不可用，无法进行 API 测试")
        return
    
    print("\n" + "=" * 60)
    
    # 运行 API 测试
    success = await test_chat_api()
    
    if success:
        print("\n🎉 所有测试通过!")
    else:
        print("\n❌ 部分测试失败!")

if __name__ == "__main__":
    asyncio.run(main())
