{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport React from \"react\";\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from \"../utils\";\nimport { withClerk } from \"./withClerk\";\nconst SignInWithMetamaskButton = withClerk(({\n  clerk,\n  children,\n  ...props\n}) => {\n  const {\n    redirectUrl,\n    ...rest\n  } = props;\n  children = normalizeWithDefaultValue(children, \"Sign in with Metamask\");\n  const child = assertSingleChild(children)(\"SignInWithMetamaskButton\");\n  const clickHandler = async () => {\n    async function authenticate() {\n      await clerk.authenticateWithMetamask({\n        redirectUrl\n      });\n    }\n    void authenticate();\n  };\n  const wrappedChildClickHandler = async e => {\n    await safeExecute(child.props.onClick)(e);\n    return clickHandler();\n  };\n  const childProps = {\n    ...rest,\n    onClick: wrappedChildClickHandler\n  };\n  return React.cloneElement(child, childProps);\n}, \"SignInWithMetamask\");\nexport { SignInWithMetamaskButton };", "map": {"version": 3, "names": ["React", "assertSingleChild", "normalizeWithDefaultValue", "safeExecute", "with<PERSON><PERSON><PERSON>", "SignInWithMetamaskButton", "clerk", "children", "props", "redirectUrl", "rest", "child", "clickHandler", "authenticate", "authenticateWithMetamask", "wrappedChildClickHandler", "e", "onClick", "childProps", "cloneElement"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\components\\SignInWithMetamaskButton.tsx"], "sourcesContent": ["import React from 'react';\n\nimport type { SignInWithMetamaskButtonProps, WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport const SignInWithMetamaskButton = withClerk(\n  ({ clerk, children, ...props }: WithClerkProp<SignInWithMetamaskButtonProps>) => {\n    const { redirectUrl, ...rest } = props;\n\n    children = normalizeWithDefaultValue(children, 'Sign in with Metamask');\n    const child = assertSingleChild(children)('SignInWithMetamaskButton');\n\n    // TODO: Properly fix this code\n    // eslint-disable-next-line @typescript-eslint/require-await\n    const clickHandler = async () => {\n      async function authenticate() {\n        await clerk.authenticateWithMetamask({ redirectUrl });\n      }\n      void authenticate();\n    };\n\n    const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n      await safeExecute((child as any).props.onClick)(e);\n      return clickHandler();\n    };\n\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n  },\n  'SignInWithMetamask',\n);\n"], "mappings": ";AAAA,OAAOA,KAAA,MAAW;AAGlB,SAASC,iBAAA,EAAmBC,yBAAA,EAA2BC,WAAA,QAAmB;AAC1E,SAASC,SAAA,QAAiB;AAEnB,MAAMC,wBAAA,GAA2BD,SAAA,CACtC,CAAC;EAAEE,KAAA;EAAOC,QAAA;EAAU,GAAGC;AAAM,MAAoD;EAC/E,MAAM;IAAEC,WAAA;IAAa,GAAGC;EAAK,IAAIF,KAAA;EAEjCD,QAAA,GAAWL,yBAAA,CAA0BK,QAAA,EAAU,uBAAuB;EACtE,MAAMI,KAAA,GAAQV,iBAAA,CAAkBM,QAAQ,EAAE,0BAA0B;EAIpE,MAAMK,YAAA,GAAe,MAAAA,CAAA,KAAY;IAC/B,eAAeC,aAAA,EAAe;MAC5B,MAAMP,KAAA,CAAMQ,wBAAA,CAAyB;QAAEL;MAAY,CAAC;IACtD;IACA,KAAKI,YAAA,CAAa;EACpB;EAEA,MAAME,wBAAA,GAAoD,MAAMC,CAAA,IAAK;IACnE,MAAMb,WAAA,CAAaQ,KAAA,CAAcH,KAAA,CAAMS,OAAO,EAAED,CAAC;IACjD,OAAOJ,YAAA,CAAa;EACtB;EAEA,MAAMM,UAAA,GAAa;IAAE,GAAGR,IAAA;IAAMO,OAAA,EAASF;EAAyB;EAChE,OAAOf,KAAA,CAAMmB,YAAA,CAAaR,KAAA,EAAsCO,UAAU;AAC5E,GACA,oBACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}