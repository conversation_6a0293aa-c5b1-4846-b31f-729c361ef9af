{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Ligature = createLucideIcon(\"Ligature\", [[\"path\", {\n  d: \"M8 20V8c0-2.2 1.8-4 4-4 1.5 0 2.8.8 3.5 2\",\n  key: \"1rtphz\"\n}], [\"path\", {\n  d: \"M6 12h4\",\n  key: \"a4o3ry\"\n}], [\"path\", {\n  d: \"M14 12h2v8\",\n  key: \"c1fccl\"\n}], [\"path\", {\n  d: \"M6 20h4\",\n  key: \"1i6q5t\"\n}], [\"path\", {\n  d: \"M14 20h4\",\n  key: \"lzx1xo\"\n}]]);\nexport { Ligature as default };", "map": {"version": 3, "names": ["Ligature", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\lucide-react\\src\\icons\\ligature.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Ligature\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNOCAyMFY4YzAtMi4yIDEuOC00IDQtNCAxLjUgMCAyLjguOCAzLjUgMiIgLz4KICA8cGF0aCBkPSJNNiAxMmg0IiAvPgogIDxwYXRoIGQ9Ik0xNCAxMmgydjgiIC8+CiAgPHBhdGggZD0iTTYgMjBoNCIgLz4KICA8cGF0aCBkPSJNMTQgMjBoNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/ligature\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Ligature = createLucideIcon('Ligature', [\n  ['path', { d: 'M8 20V8c0-2.2 1.8-4 4-4 1.5 0 2.8.8 3.5 2', key: '1rtphz' }],\n  ['path', { d: 'M6 12h4', key: 'a4o3ry' }],\n  ['path', { d: 'M14 12h2v8', key: 'c1fccl' }],\n  ['path', { d: 'M6 20h4', key: '1i6q5t' }],\n  ['path', { d: 'M14 20h4', key: 'lzx1xo' }],\n]);\n\nexport default Ligature;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CAAC,MAAQ;EAAEC,CAAA,EAAG,2CAA6C;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1E,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,SAAW;EAAAC,GAAA,EAAK;AAAA,CAAU,GACxC,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC1C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}