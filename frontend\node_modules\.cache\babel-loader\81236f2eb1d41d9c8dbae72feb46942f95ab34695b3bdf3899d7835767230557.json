{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport { useOrganization } from \"@clerk/shared/react\";\nexport { useOrganization };", "map": {"version": 3, "names": ["useOrganization"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\hooks\\useOrganization.ts"], "sourcesContent": ["export { useOrganization } from '@clerk/shared/react';\n"], "mappings": ";AAAA,SAASA,eAAA,QAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}