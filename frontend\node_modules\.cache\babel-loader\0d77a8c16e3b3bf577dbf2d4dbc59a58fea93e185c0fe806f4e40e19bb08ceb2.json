{"ast": null, "code": "// src/browser.ts\nfunction inBrowser() {\n  return typeof window !== \"undefined\";\n}\nvar botAgents = [\"bot\", \"spider\", \"crawl\", \"APIs-Google\", \"AdsBot\", \"Googlebot\", \"mediapartners\", \"Google Favicon\", \"FeedFetcher\", \"Google-Read-Aloud\", \"DuplexWeb-Google\", \"googleweblight\", \"bing\", \"yandex\", \"baidu\", \"duckduck\", \"yahoo\", \"ecosia\", \"ia_archiver\", \"facebook\", \"instagram\", \"pinterest\", \"reddit\", \"slack\", \"twitter\", \"whatsapp\", \"youtube\", \"semrush\"];\nvar botAgentRegex = new RegExp(botAgents.join(\"|\"), \"i\");\nfunction userAgentIsRobot(userAgent) {\n  return !userAgent ? false : botAgentRegex.test(userAgent);\n}\nfunction isValidBrowser() {\n  const navigator = inBrowser() ? window == null ? void 0 : window.navigator : null;\n  if (!navigator) {\n    return false;\n  }\n  return !userAgentIsRobot(navigator == null ? void 0 : navigator.userAgent) && !(navigator == null ? void 0 : navigator.webdriver);\n}\nfunction isBrowserOnline() {\n  var _a, _b;\n  const navigator = inBrowser() ? window == null ? void 0 : window.navigator : null;\n  if (!navigator) {\n    return false;\n  }\n  const isNavigatorOnline = navigator == null ? void 0 : navigator.onLine;\n  const isExperimentalConnectionOnline = ((_a = navigator == null ? void 0 : navigator.connection) == null ? void 0 : _a.rtt) !== 0 && ((_b = navigator == null ? void 0 : navigator.connection) == null ? void 0 : _b.downlink) !== 0;\n  return isExperimentalConnectionOnline && isNavigatorOnline;\n}\nfunction isValidBrowserOnline() {\n  return isBrowserOnline() && isValidBrowser();\n}\nexport { inBrowser, userAgentIsRobot, isValidBrowser, isBrowserOnline, isValidBrowserOnline };", "map": {"version": 3, "names": ["inBrowser", "window", "botAgents", "botAgentRegex", "RegExp", "join", "userAgentIsRobot", "userAgent", "test", "is<PERSON><PERSON>d<PERSON><PERSON><PERSON>", "navigator", "webdriver", "isBrowserOnline", "_a", "_b", "isNavigatorOnline", "onLine", "isExperimentalConnectionOnline", "connection", "rtt", "downlink", "isValidBrowserOnline"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\browser.ts"], "sourcesContent": ["/**\n * Checks if the window object is defined. You can also use this to check if something is happening on the client side.\n * @returns {boolean}\n */\nexport function inBrowser(): boolean {\n  return typeof window !== 'undefined';\n}\n\nconst botAgents = [\n  'bot',\n  'spider',\n  'crawl',\n  'APIs-Google',\n  'AdsBot',\n  'Googlebot',\n  'mediapartners',\n  'Google Favicon',\n  'FeedFetcher',\n  'Google-Read-Aloud',\n  'DuplexWeb-Google',\n  'googleweblight',\n  'bing',\n  'yandex',\n  'baidu',\n  'duckduck',\n  'yahoo',\n  'ecosia',\n  'ia_archiver',\n  'facebook',\n  'instagram',\n  'pinterest',\n  'reddit',\n  'slack',\n  'twitter',\n  'whatsapp',\n  'youtube',\n  'semrush',\n];\nconst botAgentRegex = new RegExp(botAgents.join('|'), 'i');\n\n/**\n * Checks if the user agent is a bot.\n * @param userAgent - Any user agent string\n * @returns {boolean}\n */\nexport function userAgentIsRobot(userAgent: string): boolean {\n  return !userAgent ? false : botAgentRegex.test(userAgent);\n}\n\n/**\n * Checks if the current environment is a browser and the user agent is not a bot.\n * @returns {boolean}\n */\nexport function isValidBrowser(): boolean {\n  const navigator = inBrowser() ? window?.navigator : null;\n  if (!navigator) {\n    return false;\n  }\n  return !userAgentIsRobot(navigator?.userAgent) && !navigator?.webdriver;\n}\n\n/**\n * Checks if the current environment is a browser and if the navigator is online.\n * @returns {boolean}\n */\nexport function isBrowserOnline(): boolean {\n  const navigator = inBrowser() ? window?.navigator : null;\n  if (!navigator) {\n    return false;\n  }\n\n  const isNavigatorOnline = navigator?.onLine;\n\n  // Being extra safe with the experimental `connection` property, as it is not defined in all browsers\n  // https://developer.mozilla.org/en-US/docs/Web/API/Navigator/connection#browser_compatibility\n  // @ts-ignore\n  const isExperimentalConnectionOnline = navigator?.connection?.rtt !== 0 && navigator?.connection?.downlink !== 0;\n  return isExperimentalConnectionOnline && isNavigatorOnline;\n}\n\n/**\n * Runs `isBrowserOnline` and `isValidBrowser` to check if the current environment is a valid browser and if the navigator is online.\n * @returns {boolean}\n */\nexport function isValidBrowserOnline(): boolean {\n  return isBrowserOnline() && isValidBrowser();\n}\n"], "mappings": ";AAIO,SAASA,UAAA,EAAqB;EACnC,OAAO,OAAOC,MAAA,KAAW;AAC3B;AAEA,IAAMC,SAAA,GAAY,CAChB,OACA,UACA,SACA,eACA,UACA,aACA,iBACA,kBACA,eACA,qBACA,oBACA,kBACA,QACA,UACA,SACA,YACA,SACA,UACA,eACA,YACA,aACA,aACA,UACA,SACA,WACA,YACA,WACA,UACF;AACA,IAAMC,aAAA,GAAgB,IAAIC,MAAA,CAAOF,SAAA,CAAUG,IAAA,CAAK,GAAG,GAAG,GAAG;AAOlD,SAASC,iBAAiBC,SAAA,EAA4B;EAC3D,OAAO,CAACA,SAAA,GAAY,QAAQJ,aAAA,CAAcK,IAAA,CAAKD,SAAS;AAC1D;AAMO,SAASE,eAAA,EAA0B;EACxC,MAAMC,SAAA,GAAYV,SAAA,CAAU,IAAIC,MAAA,oBAAAA,MAAA,CAAQS,SAAA,GAAY;EACpD,IAAI,CAACA,SAAA,EAAW;IACd,OAAO;EACT;EACA,OAAO,CAACJ,gBAAA,CAAiBI,SAAA,oBAAAA,SAAA,CAAWH,SAAS,KAAK,EAACG,SAAA,oBAAAA,SAAA,CAAWC,SAAA;AAChE;AAMO,SAASC,gBAAA,EAA2B;EAjE3C,IAAAC,EAAA,EAAAC,EAAA;EAkEE,MAAMJ,SAAA,GAAYV,SAAA,CAAU,IAAIC,MAAA,oBAAAA,MAAA,CAAQS,SAAA,GAAY;EACpD,IAAI,CAACA,SAAA,EAAW;IACd,OAAO;EACT;EAEA,MAAMK,iBAAA,GAAoBL,SAAA,oBAAAA,SAAA,CAAWM,MAAA;EAKrC,MAAMC,8BAAA,KAAiCJ,EAAA,GAAAH,SAAA,oBAAAA,SAAA,CAAWQ,UAAA,KAAX,gBAAAL,EAAA,CAAuBM,GAAA,MAAQ,OAAKL,EAAA,GAAAJ,SAAA,oBAAAA,SAAA,CAAWQ,UAAA,KAAX,gBAAAJ,EAAA,CAAuBM,QAAA,MAAa;EAC/G,OAAOH,8BAAA,IAAkCF,iBAAA;AAC3C;AAMO,SAASM,qBAAA,EAAgC;EAC9C,OAAOT,eAAA,CAAgB,KAAKH,cAAA,CAAe;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}