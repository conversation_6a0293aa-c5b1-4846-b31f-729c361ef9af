{"ast": null, "code": "// src/callWithRetry.ts\nfunction wait(ms) {\n  return new Promise(res => setTimeout(res, ms));\n}\nvar MAX_NUMBER_OF_RETRIES = 5;\nasync function callWithRetry(fn, attempt = 1, maxAttempts = MAX_NUMBER_OF_RETRIES) {\n  try {\n    return await fn();\n  } catch (e) {\n    if (attempt >= maxAttempts) {\n      throw e;\n    }\n    await wait(2 ** attempt * 100);\n    return callWithRetry(fn, attempt + 1, maxAttempts);\n  }\n}\nexport { callWithRetry };", "map": {"version": 3, "names": ["wait", "ms", "Promise", "res", "setTimeout", "MAX_NUMBER_OF_RETRIES", "callWithRetry", "fn", "attempt", "maxAttempts", "e"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\callWithRetry.ts"], "sourcesContent": ["function wait(ms: number) {\n  return new Promise(res => setTimeout(res, ms));\n}\n\nconst MAX_NUMBER_OF_RETRIES = 5;\n\n/**\n * Retry callback function every few hundred ms (with an exponential backoff\n * based on the current attempt) until the maximum attempts has reached or\n * the callback is executed successfully. The default number of maximum\n * attempts is 5 and retries are triggered when callback throws an error.\n */\nexport async function callWithRetry<T>(\n  fn: (...args: unknown[]) => Promise<T>,\n  attempt = 1,\n  maxAttempts = MAX_NUMBER_OF_RETRIES,\n): Promise<T> {\n  try {\n    return await fn();\n  } catch (e) {\n    if (attempt >= maxAttempts) {\n      throw e;\n    }\n    await wait(2 ** attempt * 100);\n\n    return callWithRetry(fn, attempt + 1, maxAttempts);\n  }\n}\n"], "mappings": ";AAAA,SAASA,KAAKC,EAAA,EAAY;EACxB,OAAO,IAAIC,OAAA,CAAQC,GAAA,IAAOC,UAAA,CAAWD,GAAA,EAAKF,EAAE,CAAC;AAC/C;AAEA,IAAMI,qBAAA,GAAwB;AAQ9B,eAAsBC,cACpBC,EAAA,EACAC,OAAA,GAAU,GACVC,WAAA,GAAcJ,qBAAA,EACF;EACZ,IAAI;IACF,OAAO,MAAME,EAAA,CAAG;EAClB,SAASG,CAAA,EAAG;IACV,IAAIF,OAAA,IAAWC,WAAA,EAAa;MAC1B,MAAMC,CAAA;IACR;IACA,MAAMV,IAAA,CAAK,KAAKQ,OAAA,GAAU,GAAG;IAE7B,OAAOF,aAAA,CAAcC,EAAA,EAAIC,OAAA,GAAU,GAAGC,WAAW;EACnD;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}