{"ast": null, "code": "import { useRef, useCallback } from 'react';\nimport useS<PERSON> from 'swr';\nimport { serialize, withMiddleware, createCacheHelper, isUndefined, useIsomorphicLayoutEffect, UNDEFINED, isFunction } from 'swr/_internal';\nimport { useSyncExternalStore } from 'use-sync-external-store/shim/index.js';\nconst INFINITE_PREFIX = '$inf$';\nconst getFirstPageKey = getKey => {\n  return serialize(getKey ? getKey(0, null) : null)[0];\n};\nconst unstable_serialize = getKey => {\n  return INFINITE_PREFIX + getFirstPageKey(getKey);\n};\n\n// We have to several type castings here because `useSWRInfinite` is a special\n// const INFINITE_PREFIX = '$inf$'\nconst EMPTY_PROMISE = Promise.resolve();\n// export const unstable_serialize = (getKey: SWRInfiniteKeyLoader) => {\n//   return INFINITE_PREFIX + getFirstPageKey(getKey)\n// }\nconst infinite = useSWRNext => (getKey, fn, config) => {\n  const didMountRef = useRef(false);\n  const {\n    cache,\n    initialSize = 1,\n    revalidateAll = false,\n    persistSize = false,\n    revalidateFirstPage = true,\n    revalidateOnMount = false,\n    parallel = false\n  } = config;\n  // The serialized key of the first page. This key will be used to store\n  // metadata of this SWR infinite hook.\n  let infiniteKey;\n  try {\n    infiniteKey = getFirstPageKey(getKey);\n    if (infiniteKey) infiniteKey = INFINITE_PREFIX + infiniteKey;\n  } catch (err) {\n    // Not ready yet.\n  }\n  const [get, set, subscribeCache] = createCacheHelper(cache, infiniteKey);\n  const getSnapshot = useCallback(() => {\n    const size = isUndefined(get()._l) ? initialSize : get()._l;\n    return size;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [cache, infiniteKey, initialSize]);\n  useSyncExternalStore(useCallback(callback => {\n    if (infiniteKey) return subscribeCache(infiniteKey, () => {\n      callback();\n    });\n    return () => {};\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [cache, infiniteKey]), getSnapshot, getSnapshot);\n  const resolvePageSize = useCallback(() => {\n    const cachedPageSize = get()._l;\n    return isUndefined(cachedPageSize) ? initialSize : cachedPageSize;\n    // `cache` isn't allowed to change during the lifecycle\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [infiniteKey, initialSize]);\n  // keep the last page size to restore it with the persistSize option\n  const lastPageSizeRef = useRef(resolvePageSize());\n  // When the page key changes, we reset the page size if it's not persisted\n  useIsomorphicLayoutEffect(() => {\n    if (!didMountRef.current) {\n      didMountRef.current = true;\n      return;\n    }\n    if (infiniteKey) {\n      // If the key has been changed, we keep the current page size if persistSize is enabled\n      // Otherwise, we reset the page size to cached pageSize\n      set({\n        _l: persistSize ? lastPageSizeRef.current : resolvePageSize()\n      });\n    }\n    // `initialSize` isn't allowed to change during the lifecycle\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [infiniteKey, cache]);\n  // Needs to check didMountRef during mounting, not in the fetcher\n  const shouldRevalidateOnMount = revalidateOnMount && !didMountRef.current;\n  // Actual SWR hook to load all pages in one fetcher.\n  const swr = useSWRNext(infiniteKey, async key => {\n    // get the revalidate context\n    const forceRevalidateAll = get()._i;\n    // return an array of page data\n    const data = [];\n    const pageSize = resolvePageSize();\n    const [getCache] = createCacheHelper(cache, key);\n    const cacheData = getCache().data;\n    const revalidators = [];\n    let previousPageData = null;\n    for (let i = 0; i < pageSize; ++i) {\n      const [pageKey, pageArg] = serialize(getKey(i, parallel ? null : previousPageData));\n      if (!pageKey) {\n        break;\n      }\n      const [getSWRCache, setSWRCache] = createCacheHelper(cache, pageKey);\n      // Get the cached page data.\n      let pageData = getSWRCache().data;\n      // should fetch (or revalidate) if:\n      // - `revalidateAll` is enabled\n      // - `mutate()` called\n      // - the cache is missing\n      // - it's the first page and it's not the initial render\n      // - `revalidateOnMount` is enabled and it's on mount\n      // - cache for that page has changed\n      const shouldFetchPage = revalidateAll || forceRevalidateAll || isUndefined(pageData) || revalidateFirstPage && !i && !isUndefined(cacheData) || shouldRevalidateOnMount || cacheData && !isUndefined(cacheData[i]) && !config.compare(cacheData[i], pageData);\n      if (fn && shouldFetchPage) {\n        const revalidate = async () => {\n          pageData = await fn(pageArg);\n          setSWRCache({\n            data: pageData,\n            _k: pageArg\n          });\n          data[i] = pageData;\n        };\n        if (parallel) {\n          revalidators.push(revalidate);\n        } else {\n          await revalidate();\n        }\n      } else {\n        data[i] = pageData;\n      }\n      if (!parallel) {\n        previousPageData = pageData;\n      }\n    }\n    // flush all revalidateions in parallel\n    if (parallel) {\n      await Promise.all(revalidators.map(r => r()));\n    }\n    // once we executed the data fetching based on the context, clear the context\n    set({\n      _i: UNDEFINED\n    });\n    // return the data\n    return data;\n  }, config);\n  const mutate = useCallback(\n  // eslint-disable-next-line func-names\n  function (data, opts) {\n    // When passing as a boolean, it's explicitly used to disable/enable\n    // revalidation.\n    const options = typeof opts === 'boolean' ? {\n      revalidate: opts\n    } : opts || {};\n    // Default to true.\n    const shouldRevalidate = options.revalidate !== false;\n    // It is possible that the key is still falsy.\n    if (!infiniteKey) return EMPTY_PROMISE;\n    if (shouldRevalidate) {\n      if (!isUndefined(data)) {\n        // We only revalidate the pages that are changed\n        set({\n          _i: false\n        });\n      } else {\n        // Calling `mutate()`, we revalidate all pages\n        set({\n          _i: true\n        });\n      }\n    }\n    return arguments.length ? swr.mutate(data, {\n      ...options,\n      revalidate: shouldRevalidate\n    }) : swr.mutate();\n  },\n  // swr.mutate is always the same reference\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [infiniteKey, cache]);\n  // Extend the SWR API\n  const setSize = useCallback(arg => {\n    // It is possible that the key is still falsy.\n    if (!infiniteKey) return EMPTY_PROMISE;\n    const [, changeSize] = createCacheHelper(cache, infiniteKey);\n    let size;\n    if (isFunction(arg)) {\n      size = arg(resolvePageSize());\n    } else if (typeof arg == 'number') {\n      size = arg;\n    }\n    if (typeof size != 'number') return EMPTY_PROMISE;\n    changeSize({\n      _l: size\n    });\n    lastPageSizeRef.current = size;\n    // Calculate the page data after the size change.\n    const data = [];\n    const [getInfiniteCache] = createCacheHelper(cache, infiniteKey);\n    let previousPageData = null;\n    for (let i = 0; i < size; ++i) {\n      const [pageKey] = serialize(getKey(i, previousPageData));\n      const [getCache] = createCacheHelper(cache, pageKey);\n      // Get the cached page data.\n      const pageData = pageKey ? getCache().data : UNDEFINED;\n      // Call `mutate` with infinte cache data if we can't get it from the page cache.\n      if (isUndefined(pageData)) {\n        return mutate(getInfiniteCache().data);\n      }\n      data.push(pageData);\n      previousPageData = pageData;\n    }\n    return mutate(data);\n  },\n  // exclude getKey from the dependencies, which isn't allowed to change during the lifecycle\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [infiniteKey, cache, mutate, resolvePageSize]);\n  // Use getter functions to avoid unnecessary re-renders caused by triggering\n  // all the getters of the returned swr object.\n  return {\n    size: resolvePageSize(),\n    setSize,\n    mutate,\n    get data() {\n      return swr.data;\n    },\n    get error() {\n      return swr.error;\n    },\n    get isValidating() {\n      return swr.isValidating;\n    },\n    get isLoading() {\n      return swr.isLoading;\n    }\n  };\n};\nconst useSWRInfinite = withMiddleware(useSWR, infinite);\nexport { useSWRInfinite as default, infinite, unstable_serialize };", "map": {"version": 3, "names": ["useRef", "useCallback", "useSWR", "serialize", "withMiddleware", "createCacheHelper", "isUndefined", "useIsomorphicLayoutEffect", "UNDEFINED", "isFunction", "useSyncExternalStore", "INFINITE_PREFIX", "getFirstPageKey", "<PERSON><PERSON><PERSON>", "unstable_serialize", "EMPTY_PROMISE", "Promise", "resolve", "infinite", "useSWRNext", "fn", "config", "didMountRef", "cache", "initialSize", "revalidateAll", "persistSize", "revalidateFirstPage", "revalidateOnMount", "parallel", "infiniteKey", "err", "get", "set", "subscribeCache", "getSnapshot", "size", "_l", "callback", "resolvePageSize", "cachedPageSize", "lastPageSizeRef", "current", "shouldRevalidateOnMount", "swr", "key", "forceRevalidateAll", "_i", "data", "pageSize", "getCache", "cacheData", "revalidators", "previousPageData", "i", "page<PERSON><PERSON>", "pageArg", "getSWRCache", "setSWRCache", "pageData", "shouldFetchPage", "compare", "revalidate", "_k", "push", "all", "map", "r", "mutate", "opts", "options", "shouldRevalidate", "arguments", "length", "setSize", "arg", "changeSize", "getInfiniteCache", "error", "isValidating", "isLoading", "useSWRInfinite", "default"], "sources": ["C:/Users/<USER>/Desktop/file/u3summer/artech/artech/frontend/node_modules/swr/infinite/dist/index.mjs"], "sourcesContent": ["import { useRef, useCallback } from 'react';\nimport useS<PERSON> from 'swr';\nimport { serialize, withMiddleware, createCacheHelper, isUndefined, useIsomorphicLayoutEffect, UNDEFINED, isFunction } from 'swr/_internal';\nimport { useSyncExternalStore } from 'use-sync-external-store/shim/index.js';\n\nconst INFINITE_PREFIX = '$inf$';\nconst getFirstPageKey = (getKey)=>{\n    return serialize(getKey ? getKey(0, null) : null)[0];\n};\nconst unstable_serialize = (getKey)=>{\n    return INFINITE_PREFIX + getFirstPageKey(getKey);\n};\n\n// We have to several type castings here because `useSWRInfinite` is a special\n// const INFINITE_PREFIX = '$inf$'\nconst EMPTY_PROMISE = Promise.resolve();\n// export const unstable_serialize = (getKey: SWRInfiniteKeyLoader) => {\n//   return INFINITE_PREFIX + getFirstPageKey(getKey)\n// }\nconst infinite = (useSWRNext)=>(getKey, fn, config)=>{\n        const didMountRef = useRef(false);\n        const { cache , initialSize =1 , revalidateAll =false , persistSize =false , revalidateFirstPage =true , revalidateOnMount =false , parallel =false  } = config;\n        // The serialized key of the first page. This key will be used to store\n        // metadata of this SWR infinite hook.\n        let infiniteKey;\n        try {\n            infiniteKey = getFirstPageKey(getKey);\n            if (infiniteKey) infiniteKey = INFINITE_PREFIX + infiniteKey;\n        } catch (err) {\n        // Not ready yet.\n        }\n        const [get, set, subscribeCache] = createCacheHelper(cache, infiniteKey);\n        const getSnapshot = useCallback(()=>{\n            const size = isUndefined(get()._l) ? initialSize : get()._l;\n            return size;\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            cache,\n            infiniteKey,\n            initialSize\n        ]);\n        useSyncExternalStore(useCallback((callback)=>{\n            if (infiniteKey) return subscribeCache(infiniteKey, ()=>{\n                callback();\n            });\n            return ()=>{};\n        }, // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            cache,\n            infiniteKey\n        ]), getSnapshot, getSnapshot);\n        const resolvePageSize = useCallback(()=>{\n            const cachedPageSize = get()._l;\n            return isUndefined(cachedPageSize) ? initialSize : cachedPageSize;\n        // `cache` isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            infiniteKey,\n            initialSize\n        ]);\n        // keep the last page size to restore it with the persistSize option\n        const lastPageSizeRef = useRef(resolvePageSize());\n        // When the page key changes, we reset the page size if it's not persisted\n        useIsomorphicLayoutEffect(()=>{\n            if (!didMountRef.current) {\n                didMountRef.current = true;\n                return;\n            }\n            if (infiniteKey) {\n                // If the key has been changed, we keep the current page size if persistSize is enabled\n                // Otherwise, we reset the page size to cached pageSize\n                set({\n                    _l: persistSize ? lastPageSizeRef.current : resolvePageSize()\n                });\n            }\n        // `initialSize` isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        }, [\n            infiniteKey,\n            cache\n        ]);\n        // Needs to check didMountRef during mounting, not in the fetcher\n        const shouldRevalidateOnMount = revalidateOnMount && !didMountRef.current;\n        // Actual SWR hook to load all pages in one fetcher.\n        const swr = useSWRNext(infiniteKey, async (key)=>{\n            // get the revalidate context\n            const forceRevalidateAll = get()._i;\n            // return an array of page data\n            const data = [];\n            const pageSize = resolvePageSize();\n            const [getCache] = createCacheHelper(cache, key);\n            const cacheData = getCache().data;\n            const revalidators = [];\n            let previousPageData = null;\n            for(let i = 0; i < pageSize; ++i){\n                const [pageKey, pageArg] = serialize(getKey(i, parallel ? null : previousPageData));\n                if (!pageKey) {\n                    break;\n                }\n                const [getSWRCache, setSWRCache] = createCacheHelper(cache, pageKey);\n                // Get the cached page data.\n                let pageData = getSWRCache().data;\n                // should fetch (or revalidate) if:\n                // - `revalidateAll` is enabled\n                // - `mutate()` called\n                // - the cache is missing\n                // - it's the first page and it's not the initial render\n                // - `revalidateOnMount` is enabled and it's on mount\n                // - cache for that page has changed\n                const shouldFetchPage = revalidateAll || forceRevalidateAll || isUndefined(pageData) || revalidateFirstPage && !i && !isUndefined(cacheData) || shouldRevalidateOnMount || cacheData && !isUndefined(cacheData[i]) && !config.compare(cacheData[i], pageData);\n                if (fn && shouldFetchPage) {\n                    const revalidate = async ()=>{\n                        pageData = await fn(pageArg);\n                        setSWRCache({\n                            data: pageData,\n                            _k: pageArg\n                        });\n                        data[i] = pageData;\n                    };\n                    if (parallel) {\n                        revalidators.push(revalidate);\n                    } else {\n                        await revalidate();\n                    }\n                } else {\n                    data[i] = pageData;\n                }\n                if (!parallel) {\n                    previousPageData = pageData;\n                }\n            }\n            // flush all revalidateions in parallel\n            if (parallel) {\n                await Promise.all(revalidators.map((r)=>r()));\n            }\n            // once we executed the data fetching based on the context, clear the context\n            set({\n                _i: UNDEFINED\n            });\n            // return the data\n            return data;\n        }, config);\n        const mutate = useCallback(// eslint-disable-next-line func-names\n        function(data, opts) {\n            // When passing as a boolean, it's explicitly used to disable/enable\n            // revalidation.\n            const options = typeof opts === 'boolean' ? {\n                revalidate: opts\n            } : opts || {};\n            // Default to true.\n            const shouldRevalidate = options.revalidate !== false;\n            // It is possible that the key is still falsy.\n            if (!infiniteKey) return EMPTY_PROMISE;\n            if (shouldRevalidate) {\n                if (!isUndefined(data)) {\n                    // We only revalidate the pages that are changed\n                    set({\n                        _i: false\n                    });\n                } else {\n                    // Calling `mutate()`, we revalidate all pages\n                    set({\n                        _i: true\n                    });\n                }\n            }\n            return arguments.length ? swr.mutate(data, {\n                ...options,\n                revalidate: shouldRevalidate\n            }) : swr.mutate();\n        }, // swr.mutate is always the same reference\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            infiniteKey,\n            cache\n        ]);\n        // Extend the SWR API\n        const setSize = useCallback((arg)=>{\n            // It is possible that the key is still falsy.\n            if (!infiniteKey) return EMPTY_PROMISE;\n            const [, changeSize] = createCacheHelper(cache, infiniteKey);\n            let size;\n            if (isFunction(arg)) {\n                size = arg(resolvePageSize());\n            } else if (typeof arg == 'number') {\n                size = arg;\n            }\n            if (typeof size != 'number') return EMPTY_PROMISE;\n            changeSize({\n                _l: size\n            });\n            lastPageSizeRef.current = size;\n            // Calculate the page data after the size change.\n            const data = [];\n            const [getInfiniteCache] = createCacheHelper(cache, infiniteKey);\n            let previousPageData = null;\n            for(let i = 0; i < size; ++i){\n                const [pageKey] = serialize(getKey(i, previousPageData));\n                const [getCache] = createCacheHelper(cache, pageKey);\n                // Get the cached page data.\n                const pageData = pageKey ? getCache().data : UNDEFINED;\n                // Call `mutate` with infinte cache data if we can't get it from the page cache.\n                if (isUndefined(pageData)) {\n                    return mutate(getInfiniteCache().data);\n                }\n                data.push(pageData);\n                previousPageData = pageData;\n            }\n            return mutate(data);\n        }, // exclude getKey from the dependencies, which isn't allowed to change during the lifecycle\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        [\n            infiniteKey,\n            cache,\n            mutate,\n            resolvePageSize\n        ]);\n        // Use getter functions to avoid unnecessary re-renders caused by triggering\n        // all the getters of the returned swr object.\n        return {\n            size: resolvePageSize(),\n            setSize,\n            mutate,\n            get data () {\n                return swr.data;\n            },\n            get error () {\n                return swr.error;\n            },\n            get isValidating () {\n                return swr.isValidating;\n            },\n            get isLoading () {\n                return swr.isLoading;\n            }\n        };\n    };\nconst useSWRInfinite = withMiddleware(useSWR, infinite);\n\nexport { useSWRInfinite as default, infinite, unstable_serialize };\n"], "mappings": "AAAA,SAASA,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAC3C,OAAOC,MAAM,MAAM,KAAK;AACxB,SAASC,SAAS,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,WAAW,EAAEC,yBAAyB,EAAEC,SAAS,EAAEC,UAAU,QAAQ,eAAe;AAC3I,SAASC,oBAAoB,QAAQ,uCAAuC;AAE5E,MAAMC,eAAe,GAAG,OAAO;AAC/B,MAAMC,eAAe,GAAIC,MAAM,IAAG;EAC9B,OAAOV,SAAS,CAACU,MAAM,GAAGA,MAAM,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;AACxD,CAAC;AACD,MAAMC,kBAAkB,GAAID,MAAM,IAAG;EACjC,OAAOF,eAAe,GAAGC,eAAe,CAACC,MAAM,CAAC;AACpD,CAAC;;AAED;AACA;AACA,MAAME,aAAa,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC;AACvC;AACA;AACA;AACA,MAAMC,QAAQ,GAAIC,UAAU,IAAG,CAACN,MAAM,EAAEO,EAAE,EAAEC,MAAM,KAAG;EAC7C,MAAMC,WAAW,GAAGtB,MAAM,CAAC,KAAK,CAAC;EACjC,MAAM;IAAEuB,KAAK;IAAGC,WAAW,GAAE,CAAC;IAAGC,aAAa,GAAE,KAAK;IAAGC,WAAW,GAAE,KAAK;IAAGC,mBAAmB,GAAE,IAAI;IAAGC,iBAAiB,GAAE,KAAK;IAAGC,QAAQ,GAAE;EAAO,CAAC,GAAGR,MAAM;EAC/J;EACA;EACA,IAAIS,WAAW;EACf,IAAI;IACAA,WAAW,GAAGlB,eAAe,CAACC,MAAM,CAAC;IACrC,IAAIiB,WAAW,EAAEA,WAAW,GAAGnB,eAAe,GAAGmB,WAAW;EAChE,CAAC,CAAC,OAAOC,GAAG,EAAE;IACd;EAAA;EAEA,MAAM,CAACC,GAAG,EAAEC,GAAG,EAAEC,cAAc,CAAC,GAAG7B,iBAAiB,CAACkB,KAAK,EAAEO,WAAW,CAAC;EACxE,MAAMK,WAAW,GAAGlC,WAAW,CAAC,MAAI;IAChC,MAAMmC,IAAI,GAAG9B,WAAW,CAAC0B,GAAG,CAAC,CAAC,CAACK,EAAE,CAAC,GAAGb,WAAW,GAAGQ,GAAG,CAAC,CAAC,CAACK,EAAE;IAC3D,OAAOD,IAAI;IACf;EACA,CAAC,EAAE,CACCb,KAAK,EACLO,WAAW,EACXN,WAAW,CACd,CAAC;EACFd,oBAAoB,CAACT,WAAW,CAAEqC,QAAQ,IAAG;IACzC,IAAIR,WAAW,EAAE,OAAOI,cAAc,CAACJ,WAAW,EAAE,MAAI;MACpDQ,QAAQ,CAAC,CAAC;IACd,CAAC,CAAC;IACF,OAAO,MAAI,CAAC,CAAC;EACjB,CAAC;EAAE;EACH,CACIf,KAAK,EACLO,WAAW,CACd,CAAC,EAAEK,WAAW,EAAEA,WAAW,CAAC;EAC7B,MAAMI,eAAe,GAAGtC,WAAW,CAAC,MAAI;IACpC,MAAMuC,cAAc,GAAGR,GAAG,CAAC,CAAC,CAACK,EAAE;IAC/B,OAAO/B,WAAW,CAACkC,cAAc,CAAC,GAAGhB,WAAW,GAAGgB,cAAc;IACrE;IACA;EACA,CAAC,EAAE,CACCV,WAAW,EACXN,WAAW,CACd,CAAC;EACF;EACA,MAAMiB,eAAe,GAAGzC,MAAM,CAACuC,eAAe,CAAC,CAAC,CAAC;EACjD;EACAhC,yBAAyB,CAAC,MAAI;IAC1B,IAAI,CAACe,WAAW,CAACoB,OAAO,EAAE;MACtBpB,WAAW,CAACoB,OAAO,GAAG,IAAI;MAC1B;IACJ;IACA,IAAIZ,WAAW,EAAE;MACb;MACA;MACAG,GAAG,CAAC;QACAI,EAAE,EAAEX,WAAW,GAAGe,eAAe,CAACC,OAAO,GAAGH,eAAe,CAAC;MAChE,CAAC,CAAC;IACN;IACJ;IACA;EACA,CAAC,EAAE,CACCT,WAAW,EACXP,KAAK,CACR,CAAC;EACF;EACA,MAAMoB,uBAAuB,GAAGf,iBAAiB,IAAI,CAACN,WAAW,CAACoB,OAAO;EACzE;EACA,MAAME,GAAG,GAAGzB,UAAU,CAACW,WAAW,EAAE,MAAOe,GAAG,IAAG;IAC7C;IACA,MAAMC,kBAAkB,GAAGd,GAAG,CAAC,CAAC,CAACe,EAAE;IACnC;IACA,MAAMC,IAAI,GAAG,EAAE;IACf,MAAMC,QAAQ,GAAGV,eAAe,CAAC,CAAC;IAClC,MAAM,CAACW,QAAQ,CAAC,GAAG7C,iBAAiB,CAACkB,KAAK,EAAEsB,GAAG,CAAC;IAChD,MAAMM,SAAS,GAAGD,QAAQ,CAAC,CAAC,CAACF,IAAI;IACjC,MAAMI,YAAY,GAAG,EAAE;IACvB,IAAIC,gBAAgB,GAAG,IAAI;IAC3B,KAAI,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,QAAQ,EAAE,EAAEK,CAAC,EAAC;MAC7B,MAAM,CAACC,OAAO,EAAEC,OAAO,CAAC,GAAGrD,SAAS,CAACU,MAAM,CAACyC,CAAC,EAAEzB,QAAQ,GAAG,IAAI,GAAGwB,gBAAgB,CAAC,CAAC;MACnF,IAAI,CAACE,OAAO,EAAE;QACV;MACJ;MACA,MAAM,CAACE,WAAW,EAAEC,WAAW,CAAC,GAAGrD,iBAAiB,CAACkB,KAAK,EAAEgC,OAAO,CAAC;MACpE;MACA,IAAII,QAAQ,GAAGF,WAAW,CAAC,CAAC,CAACT,IAAI;MACjC;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAMY,eAAe,GAAGnC,aAAa,IAAIqB,kBAAkB,IAAIxC,WAAW,CAACqD,QAAQ,CAAC,IAAIhC,mBAAmB,IAAI,CAAC2B,CAAC,IAAI,CAAChD,WAAW,CAAC6C,SAAS,CAAC,IAAIR,uBAAuB,IAAIQ,SAAS,IAAI,CAAC7C,WAAW,CAAC6C,SAAS,CAACG,CAAC,CAAC,CAAC,IAAI,CAACjC,MAAM,CAACwC,OAAO,CAACV,SAAS,CAACG,CAAC,CAAC,EAAEK,QAAQ,CAAC;MAC7P,IAAIvC,EAAE,IAAIwC,eAAe,EAAE;QACvB,MAAME,UAAU,GAAG,MAAAA,CAAA,KAAU;UACzBH,QAAQ,GAAG,MAAMvC,EAAE,CAACoC,OAAO,CAAC;UAC5BE,WAAW,CAAC;YACRV,IAAI,EAAEW,QAAQ;YACdI,EAAE,EAAEP;UACR,CAAC,CAAC;UACFR,IAAI,CAACM,CAAC,CAAC,GAAGK,QAAQ;QACtB,CAAC;QACD,IAAI9B,QAAQ,EAAE;UACVuB,YAAY,CAACY,IAAI,CAACF,UAAU,CAAC;QACjC,CAAC,MAAM;UACH,MAAMA,UAAU,CAAC,CAAC;QACtB;MACJ,CAAC,MAAM;QACHd,IAAI,CAACM,CAAC,CAAC,GAAGK,QAAQ;MACtB;MACA,IAAI,CAAC9B,QAAQ,EAAE;QACXwB,gBAAgB,GAAGM,QAAQ;MAC/B;IACJ;IACA;IACA,IAAI9B,QAAQ,EAAE;MACV,MAAMb,OAAO,CAACiD,GAAG,CAACb,YAAY,CAACc,GAAG,CAAEC,CAAC,IAAGA,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD;IACA;IACAlC,GAAG,CAAC;MACAc,EAAE,EAAEvC;IACR,CAAC,CAAC;IACF;IACA,OAAOwC,IAAI;EACf,CAAC,EAAE3B,MAAM,CAAC;EACV,MAAM+C,MAAM,GAAGnE,WAAW;EAAC;EAC3B,UAAS+C,IAAI,EAAEqB,IAAI,EAAE;IACjB;IACA;IACA,MAAMC,OAAO,GAAG,OAAOD,IAAI,KAAK,SAAS,GAAG;MACxCP,UAAU,EAAEO;IAChB,CAAC,GAAGA,IAAI,IAAI,CAAC,CAAC;IACd;IACA,MAAME,gBAAgB,GAAGD,OAAO,CAACR,UAAU,KAAK,KAAK;IACrD;IACA,IAAI,CAAChC,WAAW,EAAE,OAAOf,aAAa;IACtC,IAAIwD,gBAAgB,EAAE;MAClB,IAAI,CAACjE,WAAW,CAAC0C,IAAI,CAAC,EAAE;QACpB;QACAf,GAAG,CAAC;UACAc,EAAE,EAAE;QACR,CAAC,CAAC;MACN,CAAC,MAAM;QACH;QACAd,GAAG,CAAC;UACAc,EAAE,EAAE;QACR,CAAC,CAAC;MACN;IACJ;IACA,OAAOyB,SAAS,CAACC,MAAM,GAAG7B,GAAG,CAACwB,MAAM,CAACpB,IAAI,EAAE;MACvC,GAAGsB,OAAO;MACVR,UAAU,EAAES;IAChB,CAAC,CAAC,GAAG3B,GAAG,CAACwB,MAAM,CAAC,CAAC;EACrB,CAAC;EAAE;EACH;EACA,CACItC,WAAW,EACXP,KAAK,CACR,CAAC;EACF;EACA,MAAMmD,OAAO,GAAGzE,WAAW,CAAE0E,GAAG,IAAG;IAC/B;IACA,IAAI,CAAC7C,WAAW,EAAE,OAAOf,aAAa;IACtC,MAAM,GAAG6D,UAAU,CAAC,GAAGvE,iBAAiB,CAACkB,KAAK,EAAEO,WAAW,CAAC;IAC5D,IAAIM,IAAI;IACR,IAAI3B,UAAU,CAACkE,GAAG,CAAC,EAAE;MACjBvC,IAAI,GAAGuC,GAAG,CAACpC,eAAe,CAAC,CAAC,CAAC;IACjC,CAAC,MAAM,IAAI,OAAOoC,GAAG,IAAI,QAAQ,EAAE;MAC/BvC,IAAI,GAAGuC,GAAG;IACd;IACA,IAAI,OAAOvC,IAAI,IAAI,QAAQ,EAAE,OAAOrB,aAAa;IACjD6D,UAAU,CAAC;MACPvC,EAAE,EAAED;IACR,CAAC,CAAC;IACFK,eAAe,CAACC,OAAO,GAAGN,IAAI;IAC9B;IACA,MAAMY,IAAI,GAAG,EAAE;IACf,MAAM,CAAC6B,gBAAgB,CAAC,GAAGxE,iBAAiB,CAACkB,KAAK,EAAEO,WAAW,CAAC;IAChE,IAAIuB,gBAAgB,GAAG,IAAI;IAC3B,KAAI,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGlB,IAAI,EAAE,EAAEkB,CAAC,EAAC;MACzB,MAAM,CAACC,OAAO,CAAC,GAAGpD,SAAS,CAACU,MAAM,CAACyC,CAAC,EAAED,gBAAgB,CAAC,CAAC;MACxD,MAAM,CAACH,QAAQ,CAAC,GAAG7C,iBAAiB,CAACkB,KAAK,EAAEgC,OAAO,CAAC;MACpD;MACA,MAAMI,QAAQ,GAAGJ,OAAO,GAAGL,QAAQ,CAAC,CAAC,CAACF,IAAI,GAAGxC,SAAS;MACtD;MACA,IAAIF,WAAW,CAACqD,QAAQ,CAAC,EAAE;QACvB,OAAOS,MAAM,CAACS,gBAAgB,CAAC,CAAC,CAAC7B,IAAI,CAAC;MAC1C;MACAA,IAAI,CAACgB,IAAI,CAACL,QAAQ,CAAC;MACnBN,gBAAgB,GAAGM,QAAQ;IAC/B;IACA,OAAOS,MAAM,CAACpB,IAAI,CAAC;EACvB,CAAC;EAAE;EACH;EACA,CACIlB,WAAW,EACXP,KAAK,EACL6C,MAAM,EACN7B,eAAe,CAClB,CAAC;EACF;EACA;EACA,OAAO;IACHH,IAAI,EAAEG,eAAe,CAAC,CAAC;IACvBmC,OAAO;IACPN,MAAM;IACN,IAAIpB,IAAIA,CAAA,EAAI;MACR,OAAOJ,GAAG,CAACI,IAAI;IACnB,CAAC;IACD,IAAI8B,KAAKA,CAAA,EAAI;MACT,OAAOlC,GAAG,CAACkC,KAAK;IACpB,CAAC;IACD,IAAIC,YAAYA,CAAA,EAAI;MAChB,OAAOnC,GAAG,CAACmC,YAAY;IAC3B,CAAC;IACD,IAAIC,SAASA,CAAA,EAAI;MACb,OAAOpC,GAAG,CAACoC,SAAS;IACxB;EACJ,CAAC;AACL,CAAC;AACL,MAAMC,cAAc,GAAG7E,cAAc,CAACF,MAAM,EAAEgB,QAAQ,CAAC;AAEvD,SAAS+D,cAAc,IAAIC,OAAO,EAAEhE,QAAQ,EAAEJ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}