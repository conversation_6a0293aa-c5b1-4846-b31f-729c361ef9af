{"ast": null, "code": "import { Poller, createWorkerTimers, noop } from \"./chunk-GBGFSJA3.mjs\";\nimport { getRequestUrl, isHttpOrHttps, isProxyUrlRelative, isValidProxyUrl, proxyUrlToAbsoluteURL } from \"./chunk-MHVPBPEZ.mjs\";\nimport { camelToSnake, deepCamelToSnake, deepSnakeToCamel, isIPV4Address, snakeToCamel, titleize, toSentence } from \"./chunk-5QXIOV6T.mjs\";\nimport { addClerkPrefix, getClerkJsMajorVersionOrTag, getScriptUrl, isStaging, parseSearchParams, stripScheme } from \"./chunk-BX6URPWV.mjs\";\nimport { extension, readJSONFile } from \"./chunk-5JU2E5TY.mjs\";\nimport { handleValueOrFn } from \"./chunk-TRWMHODU.mjs\";\nimport { buildPublishableKey, createDevOrStagingUrl<PERSON>ache, isDevelopment<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, isLegacy<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, isProduction<PERSON><PERSON><PERSON><PERSON><PERSON>ey, isPublishable<PERSON>ey, parsePublishable<PERSON>ey } from \"./chunk-IAZRYRAH.mjs\";\nimport { isomorphicAtob } from \"./chunk-TETGTEI2.mjs\";\nimport { loadScript } from \"./chunk-AOO6TJNL.mjs\";\nimport { LocalStorageBroadcastChannel } from \"./chunk-RSOCGYTF.mjs\";\nimport { inBrowser, isBrowserOnline, isValidBrowser, isValidBrowserOnline, userAgentIsRobot } from \"./chunk-LJ4R7M7R.mjs\";\nimport { callWithRetry } from \"./chunk-4PW5MDZA.mjs\";\nimport { colorToSameTypeString, hasAlpha, hexStringToRgbaColor, isHSLColor, isRGBColor, isTransparent, isValidHexString, isValidHslaString, isValidRgbaString, stringToHslaColor, stringToSameTypeColor } from \"./chunk-WUG2DC4W.mjs\";\nimport { addYears, dateTo12HourTime, differenceInCalendarDays, formatRelative, normalizeDate } from \"./chunk-FSKKI4LG.mjs\";\nimport { DEV_BROWSER_JWT_MARKER, DEV_BROWSER_SSO_JWT_PARAMETER, extractDevBrowserJWTFromURLHash, extractDevBrowserJWTFromURLSearchParams, setDevBrowserJWTInURL } from \"./chunk-ECUSKG7K.mjs\";\nimport { ClerkAPIResponseError, ClerkRuntimeError, EmailLinkError, EmailLinkErrorCode, MagicLinkError, MagicLinkErrorCode, buildErrorThrower, is4xxError, isCaptchaError, isClerkAPIResponseError, isClerkRuntimeError, isEmailLinkError, isKnownError, isMagicLinkError, isMetamaskError, isNetworkError, isPasswordPwnedError, isUnauthorizedError, isUserLockedError, parseError, parseErrors } from \"./chunk-VN4YMSVR.mjs\";\nimport { deprecated, deprecatedObjectProperty, deprecatedProperty, isDevelopmentEnvironment, isProductionEnvironment, isTestEnvironment } from \"./chunk-IC4FGZI3.mjs\";\nimport \"./chunk-NDCDZYN6.mjs\";\n\n// src/utils/createDeferredPromise.ts\nvar createDeferredPromise = () => {\n  let resolve = noop;\n  let reject = noop;\n  const promise = new Promise((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return {\n    promise,\n    resolve,\n    reject\n  };\n};\n\n// src/utils/runWithExponentialBackOff.ts\nvar defaultOptions = {\n  firstDelay: 125,\n  maxDelay: 0,\n  timeMultiple: 2,\n  shouldRetry: () => true\n};\nvar sleep = async ms => new Promise(s => setTimeout(s, ms));\nvar createExponentialDelayAsyncFn = opts => {\n  let timesCalled = 0;\n  const calculateDelayInMs = () => {\n    const constant = opts.firstDelay;\n    const base = opts.timeMultiple;\n    const delay = constant * Math.pow(base, timesCalled);\n    return Math.min(opts.maxDelay || delay, delay);\n  };\n  return async () => {\n    await sleep(calculateDelayInMs());\n    timesCalled++;\n  };\n};\nvar runWithExponentialBackOff = async (callback, options = {}) => {\n  let iterationsCount = 0;\n  const {\n    shouldRetry,\n    firstDelay,\n    maxDelay,\n    timeMultiple\n  } = {\n    ...defaultOptions,\n    ...options\n  };\n  const delay = createExponentialDelayAsyncFn({\n    firstDelay,\n    maxDelay,\n    timeMultiple\n  });\n  while (true) {\n    try {\n      return await callback();\n    } catch (e) {\n      iterationsCount++;\n      if (!shouldRetry(e, iterationsCount)) {\n        throw e;\n      }\n      await delay();\n    }\n  }\n};\n\n// src/utils/logErrorInDevMode.ts\nvar logErrorInDevMode = message => {\n  if (isDevelopmentEnvironment()) {\n    console.error(message);\n  }\n};\nexport { ClerkAPIResponseError, ClerkRuntimeError, DEV_BROWSER_JWT_MARKER, DEV_BROWSER_SSO_JWT_PARAMETER, EmailLinkError, EmailLinkErrorCode, LocalStorageBroadcastChannel, MagicLinkError, MagicLinkErrorCode, Poller, addClerkPrefix, addYears, buildErrorThrower, buildPublishableKey, callWithRetry, camelToSnake, colorToSameTypeString, createDeferredPromise, createDevOrStagingUrlCache, createWorkerTimers, dateTo12HourTime, deepCamelToSnake, deepSnakeToCamel, deprecated, deprecatedObjectProperty, deprecatedProperty, differenceInCalendarDays, extension, extractDevBrowserJWTFromURLHash, extractDevBrowserJWTFromURLSearchParams, formatRelative, getClerkJsMajorVersionOrTag, getRequestUrl, getScriptUrl, handleValueOrFn, hasAlpha, hexStringToRgbaColor, inBrowser, is4xxError, isBrowserOnline, isCaptchaError, isClerkAPIResponseError, isClerkRuntimeError, isDevelopmentEnvironment, isDevelopmentFromApiKey, isEmailLinkError, isHSLColor, isHttpOrHttps, isIPV4Address, isKnownError, isLegacyFrontendApiKey, isMagicLinkError, isMetamaskError, isNetworkError, isPasswordPwnedError, isProductionEnvironment, isProductionFromApiKey, isProxyUrlRelative, isPublishableKey, isRGBColor, isStaging, isTestEnvironment, isTransparent, isUnauthorizedError, isUserLockedError, isValidBrowser, isValidBrowserOnline, isValidHexString, isValidHslaString, isValidProxyUrl, isValidRgbaString, isomorphicAtob, loadScript, logErrorInDevMode, noop, normalizeDate, parseError, parseErrors, parsePublishableKey, parseSearchParams, proxyUrlToAbsoluteURL, readJSONFile, runWithExponentialBackOff, setDevBrowserJWTInURL, snakeToCamel, stringToHslaColor, stringToSameTypeColor, stripScheme, titleize, toSentence, userAgentIsRobot };", "map": {"version": 3, "names": ["createDeferredPromise", "resolve", "noop", "reject", "promise", "Promise", "res", "rej", "defaultOptions", "firstDelay", "max<PERSON><PERSON><PERSON>", "timeMultiple", "shouldRetry", "sleep", "ms", "s", "setTimeout", "createExponentialDelayAsyncFn", "opts", "timesCalled", "calculateDelayInMs", "constant", "base", "delay", "Math", "pow", "min", "runWithExponentialBackOff", "callback", "options", "iterationsCount", "e", "logErrorInDevMode", "message", "isDevelopmentEnvironment", "console", "error"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\utils\\createDeferredPromise.ts", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\utils\\runWithExponentialBackOff.ts", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\utils\\logErrorInDevMode.ts"], "sourcesContent": ["import { noop } from './noop';\n\ntype Callback = (val?: any) => void;\n\n/**\n * Create a promise that can be resolved or rejected from\n * outside the Promise constructor callback\n */\nexport const createDeferredPromise = () => {\n  let resolve: Callback = noop;\n  let reject: Callback = noop;\n  const promise = new Promise((res, rej) => {\n    resolve = res;\n    reject = rej;\n  });\n  return { promise, resolve, reject };\n};\n", "type Milliseconds = number;\n\ntype BackoffOptions = Partial<{\n  firstDelay: Milliseconds;\n  maxDelay: Milliseconds;\n  timeMultiple: number;\n  shouldRetry: (error: unknown, iterationsCount: number) => boolean;\n}>;\n\nconst defaultOptions: Required<BackoffOptions> = {\n  firstDelay: 125,\n  maxDelay: 0,\n  timeMultiple: 2,\n  shouldRetry: () => true,\n};\n\nconst sleep = async (ms: Milliseconds) => new Promise(s => setTimeout(s, ms));\n\nconst createExponentialDelayAsyncFn = (opts: {\n  firstDelay: Milliseconds;\n  maxDelay: Milliseconds;\n  timeMultiple: number;\n}) => {\n  let timesCalled = 0;\n\n  const calculateDelayInMs = () => {\n    const constant = opts.firstDelay;\n    const base = opts.timeMultiple;\n    const delay = constant * Math.pow(base, timesCalled);\n    return Math.min(opts.maxDelay || delay, delay);\n  };\n\n  return async (): Promise<void> => {\n    await sleep(calculateDelayInMs());\n    timesCalled++;\n  };\n};\n\nexport const runWithExponentialBackOff = async <T>(\n  callback: () => T | Promise<T>,\n  options: BackoffOptions = {},\n): Promise<T> => {\n  let iterationsCount = 0;\n  const { shouldRetry, firstDelay, maxDelay, timeMultiple } = {\n    ...defaultOptions,\n    ...options,\n  };\n  const delay = createExponentialDelayAsyncFn({ firstDelay, maxDelay, timeMultiple });\n\n  // eslint-disable-next-line no-constant-condition\n  while (true) {\n    try {\n      return await callback();\n    } catch (e) {\n      iterationsCount++;\n      if (!shouldRetry(e, iterationsCount)) {\n        throw e;\n      }\n      await delay();\n    }\n  }\n};\n", "import { isDevelopmentEnvironment } from './runtimeEnvironment';\n\nexport const logErrorInDevMode = (message: string) => {\n  if (isDevelopmentEnvironment()) {\n    console.error(message);\n  }\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAQO,IAAMA,qBAAA,GAAwBA,CAAA,KAAM;EACzC,IAAIC,OAAA,GAAoBC,IAAA;EACxB,IAAIC,MAAA,GAAmBD,IAAA;EACvB,MAAME,OAAA,GAAU,IAAIC,OAAA,CAAQ,CAACC,GAAA,EAAKC,GAAA,KAAQ;IACxCN,OAAA,GAAUK,GAAA;IACVH,MAAA,GAASI,GAAA;EACX,CAAC;EACD,OAAO;IAAEH,OAAA;IAASH,OAAA;IAASE;EAAO;AACpC;;;ACPA,IAAMK,cAAA,GAA2C;EAC/CC,UAAA,EAAY;EACZC,QAAA,EAAU;EACVC,YAAA,EAAc;EACdC,WAAA,EAAaA,CAAA,KAAM;AACrB;AAEA,IAAMC,KAAA,GAAQ,MAAOC,EAAA,IAAqB,IAAIT,OAAA,CAAQU,CAAA,IAAKC,UAAA,CAAWD,CAAA,EAAGD,EAAE,CAAC;AAE5E,IAAMG,6BAAA,GAAiCC,IAAA,IAIjC;EACJ,IAAIC,WAAA,GAAc;EAElB,MAAMC,kBAAA,GAAqBA,CAAA,KAAM;IAC/B,MAAMC,QAAA,GAAWH,IAAA,CAAKT,UAAA;IACtB,MAAMa,IAAA,GAAOJ,IAAA,CAAKP,YAAA;IAClB,MAAMY,KAAA,GAAQF,QAAA,GAAWG,IAAA,CAAKC,GAAA,CAAIH,IAAA,EAAMH,WAAW;IACnD,OAAOK,IAAA,CAAKE,GAAA,CAAIR,IAAA,CAAKR,QAAA,IAAYa,KAAA,EAAOA,KAAK;EAC/C;EAEA,OAAO,YAA2B;IAChC,MAAMV,KAAA,CAAMO,kBAAA,CAAmB,CAAC;IAChCD,WAAA;EACF;AACF;AAEO,IAAMQ,yBAAA,GAA4B,MAAAA,CACvCC,QAAA,EACAC,OAAA,GAA0B,CAAC,MACZ;EACf,IAAIC,eAAA,GAAkB;EACtB,MAAM;IAAElB,WAAA;IAAaH,UAAA;IAAYC,QAAA;IAAUC;EAAa,IAAI;IAC1D,GAAGH,cAAA;IACH,GAAGqB;EACL;EACA,MAAMN,KAAA,GAAQN,6BAAA,CAA8B;IAAER,UAAA;IAAYC,QAAA;IAAUC;EAAa,CAAC;EAGlF,OAAO,MAAM;IACX,IAAI;MACF,OAAO,MAAMiB,QAAA,CAAS;IACxB,SAASG,CAAA,EAAG;MACVD,eAAA;MACA,IAAI,CAAClB,WAAA,CAAYmB,CAAA,EAAGD,eAAe,GAAG;QACpC,MAAMC,CAAA;MACR;MACA,MAAMR,KAAA,CAAM;IACd;EACF;AACF;;;AC3DO,IAAMS,iBAAA,GAAqBC,OAAA,IAAoB;EACpD,IAAIC,wBAAA,CAAyB,GAAG;IAC9BC,OAAA,CAAQC,KAAA,CAAMH,OAAO;EACvB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}