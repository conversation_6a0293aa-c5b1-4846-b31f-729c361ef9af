{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport React from \"react\";\nimport { multipleChildrenInButtonComponent } from \"../errors\";\nconst assertSingleChild = children => name => {\n  try {\n    return React.Children.only(children);\n  } catch (e) {\n    throw new Error(multipleChildrenInButtonComponent(name));\n  }\n};\nconst normalizeWithDefaultValue = (children, defaultText) => {\n  if (!children) {\n    children = defaultText;\n  }\n  if (typeof children === \"string\") {\n    children = /* @__PURE__ */React.createElement(\"button\", null, children);\n  }\n  return children;\n};\nconst safeExecute = cb => (...args) => {\n  if (cb && typeof cb === \"function\") {\n    return cb(...args);\n  }\n};\nexport { assertSingleChild, normalizeWithDefaultValue, safeExecute };", "map": {"version": 3, "names": ["React", "multipleChildrenInButtonComponent", "assertSingleChild", "children", "name", "Children", "only", "e", "Error", "normalizeWithDefaultValue", "defaultText", "createElement", "safeExecute", "cb", "args"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\utils\\childrenUtils.tsx"], "sourcesContent": ["import React from 'react';\n\nimport { multipleChildrenInButtonComponent } from '../errors';\n\nexport const assertSingleChild =\n  (children: React.ReactNode) =>\n  (name: 'SignInButton' | 'SignUpButton' | 'SignOutButton' | 'SignInWithMetamaskButton') => {\n    try {\n      return React.Children.only(children);\n    } catch (e) {\n      throw new Error(multipleChildrenInButtonComponent(name));\n    }\n  };\n\nexport const normalizeWithDefaultValue = (children: React.ReactNode | undefined, defaultText: string) => {\n  if (!children) {\n    children = defaultText;\n  }\n  if (typeof children === 'string') {\n    children = <button>{children}</button>;\n  }\n  return children;\n};\n\nexport const safeExecute =\n  (cb: unknown) =>\n  (...args: any) => {\n    if (cb && typeof cb === 'function') {\n      return cb(...args);\n    }\n  };\n"], "mappings": ";AAAA,OAAOA,KAAA,MAAW;AAElB,SAASC,iCAAA,QAAyC;AAE3C,MAAMC,iBAAA,GACVC,QAAA,IACAC,IAAA,IAAyF;EACxF,IAAI;IACF,OAAOJ,KAAA,CAAMK,QAAA,CAASC,IAAA,CAAKH,QAAQ;EACrC,SAASI,CAAA,EAAG;IACV,MAAM,IAAIC,KAAA,CAAMP,iCAAA,CAAkCG,IAAI,CAAC;EACzD;AACF;AAEK,MAAMK,yBAAA,GAA4BA,CAACN,QAAA,EAAuCO,WAAA,KAAwB;EACvG,IAAI,CAACP,QAAA,EAAU;IACbA,QAAA,GAAWO,WAAA;EACb;EACA,IAAI,OAAOP,QAAA,KAAa,UAAU;IAChCA,QAAA,GAAW,eAAAH,KAAA,CAAAW,aAAA,CAAC,gBAAQR,QAAS;EAC/B;EACA,OAAOA,QAAA;AACT;AAEO,MAAMS,WAAA,GACVC,EAAA,IACD,IAAIC,IAAA,KAAc;EAChB,IAAID,EAAA,IAAM,OAAOA,EAAA,KAAO,YAAY;IAClC,OAAOA,EAAA,CAAG,GAAGC,IAAI;EACnB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}