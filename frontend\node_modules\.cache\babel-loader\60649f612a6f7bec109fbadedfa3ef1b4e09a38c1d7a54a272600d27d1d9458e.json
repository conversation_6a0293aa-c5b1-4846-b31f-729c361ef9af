{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport React from \"react\";\nimport { useIsomorphicClerkContext } from \"../contexts/IsomorphicClerkContext\";\nimport { LoadedGuarantee } from \"../contexts/StructureContext\";\nimport { hocChildrenNotAFunctionError } from \"../errors\";\nconst withClerk = (Component, displayName) => {\n  displayName = displayName || Component.displayName || Component.name || \"Component\";\n  Component.displayName = displayName;\n  const HOC = props => {\n    const clerk = useIsomorphicClerkContext();\n    if (!clerk.loaded) {\n      return null;\n    }\n    return /* @__PURE__ */React.createElement(LoadedGuarantee, null, /* @__PURE__ */React.createElement(Component, {\n      ...props,\n      clerk\n    }));\n  };\n  HOC.displayName = `withClerk(${displayName})`;\n  return HOC;\n};\nconst WithClerk = ({\n  children\n}) => {\n  const clerk = useIsomorphicClerkContext();\n  if (typeof children !== \"function\") {\n    throw new Error(hocChildrenNotAFunctionError);\n  }\n  if (!clerk.loaded) {\n    return null;\n  }\n  return /* @__PURE__ */React.createElement(LoadedGuarantee, null, children(clerk));\n};\nexport { WithClerk, withClerk };", "map": {"version": 3, "names": ["React", "useIsomorphicClerkContext", "LoadedGuarantee", "hocChildrenNotAFunctionError", "with<PERSON><PERSON><PERSON>", "Component", "displayName", "name", "HOC", "props", "clerk", "loaded", "createElement", "WithClerk", "children", "Error"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\components\\withClerk.tsx"], "sourcesContent": ["import type { LoadedClerk } from '@clerk/types';\nimport React from 'react';\n\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\nimport { LoadedGuarantee } from '../contexts/StructureContext';\nimport { hocChildrenNotAFunctionError } from '../errors';\n\nexport const withClerk = <P extends { clerk: LoadedClerk }>(\n  Component: React.ComponentType<P>,\n  displayName?: string,\n) => {\n  displayName = displayName || Component.displayName || Component.name || 'Component';\n  Component.displayName = displayName;\n  const HOC = (props: Omit<P, 'clerk'>) => {\n    const clerk = useIsomorphicClerkContext();\n\n    if (!clerk.loaded) {\n      return null;\n    }\n\n    return (\n      <LoadedGuarantee>\n        <Component\n          {...(props as P)}\n          clerk={clerk}\n        />\n      </LoadedGuarantee>\n    );\n  };\n  HOC.displayName = `withClerk(${displayName})`;\n  return HOC;\n};\n\nexport const WithClerk: React.FC<{\n  children: (clerk: LoadedClerk) => React.ReactNode;\n}> = ({ children }) => {\n  const clerk = useIsomorphicClerkContext();\n\n  if (typeof children !== 'function') {\n    throw new Error(hocChildrenNotAFunctionError);\n  }\n\n  if (!clerk.loaded) {\n    return null;\n  }\n\n  return <LoadedGuarantee>{children(clerk as unknown as LoadedClerk)}</LoadedGuarantee>;\n};\n"], "mappings": ";AACA,OAAOA,KAAA,MAAW;AAElB,SAASC,yBAAA,QAAiC;AAC1C,SAASC,eAAA,QAAuB;AAChC,SAASC,4BAAA,QAAoC;AAEtC,MAAMC,SAAA,GAAYA,CACvBC,SAAA,EACAC,WAAA,KACG;EACHA,WAAA,GAAcA,WAAA,IAAeD,SAAA,CAAUC,WAAA,IAAeD,SAAA,CAAUE,IAAA,IAAQ;EACxEF,SAAA,CAAUC,WAAA,GAAcA,WAAA;EACxB,MAAME,GAAA,GAAOC,KAAA,IAA4B;IACvC,MAAMC,KAAA,GAAQT,yBAAA,CAA0B;IAExC,IAAI,CAACS,KAAA,CAAMC,MAAA,EAAQ;MACjB,OAAO;IACT;IAEA,OACE,eAAAX,KAAA,CAAAY,aAAA,CAACV,eAAA,QACC,eAAAF,KAAA,CAAAY,aAAA,CAACP,SAAA;MACE,GAAII,KAAA;MACLC;IAAA,CACF,CACF;EAEJ;EACAF,GAAA,CAAIF,WAAA,GAAc,aAAaA,WAAW;EAC1C,OAAOE,GAAA;AACT;AAEO,MAAMK,SAAA,GAERA,CAAC;EAAEC;AAAS,MAAM;EACrB,MAAMJ,KAAA,GAAQT,yBAAA,CAA0B;EAExC,IAAI,OAAOa,QAAA,KAAa,YAAY;IAClC,MAAM,IAAIC,KAAA,CAAMZ,4BAA4B;EAC9C;EAEA,IAAI,CAACO,KAAA,CAAMC,MAAA,EAAQ;IACjB,OAAO;EACT;EAEA,OAAO,eAAAX,KAAA,CAAAY,aAAA,CAACV,eAAA,QAAiBY,QAAA,CAASJ,KAA+B,CAAE;AACrE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}