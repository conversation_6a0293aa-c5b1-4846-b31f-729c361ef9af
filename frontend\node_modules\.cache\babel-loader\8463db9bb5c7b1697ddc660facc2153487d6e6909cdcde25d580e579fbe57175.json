{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Squirrel = createLucideIcon(\"Squirrel\", [[\"path\", {\n  d: \"M18 6a4 4 0 0 0-4 4 7 7 0 0 0-7 7c0-5 4-5 4-10.5a4.5 4.5 0 1 0-9 0 2.5 2.5 0 0 0 5 0C7 10 3 11 3 17c0 2.8 2.2 5 5 5h10\",\n  key: \"980v8a\"\n}], [\"path\", {\n  d: \"M16 20c0-1.7 1.3-3 3-3h1a2 2 0 0 0 2-2v-2a4 4 0 0 0-4-4V4\",\n  key: \"19wibc\"\n}], [\"path\", {\n  d: \"M15.2 22a3 3 0 0 0-2.2-5\",\n  key: \"13bpac\"\n}], [\"path\", {\n  d: \"M18 13h.01\",\n  key: \"9veqaj\"\n}]]);\nexport { Squirrel as default };", "map": {"version": 3, "names": ["Squirrel", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\lucide-react\\src\\icons\\squirrel.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Squirrel\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTggNmE0IDQgMCAwIDAtNCA0IDcgNyAwIDAgMC03IDdjMC01IDQtNSA0LTEwLjVhNC41IDQuNSAwIDEgMC05IDAgMi41IDIuNSAwIDAgMCA1IDBDNyAxMCAzIDExIDMgMTdjMCAyLjggMi4yIDUgNSA1aDEwIiAvPgogIDxwYXRoIGQ9Ik0xNiAyMGMwLTEuNyAxLjMtMyAzLTNoMWEyIDIgMCAwIDAgMi0ydi0yYTQgNCAwIDAgMC00LTRWNCIgLz4KICA8cGF0aCBkPSJNMTUuMiAyMmEzIDMgMCAwIDAtMi4yLTUiIC8+CiAgPHBhdGggZD0iTTE4IDEzaC4wMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/squirrel\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Squirrel = createLucideIcon('Squirrel', [\n  [\n    'path',\n    {\n      d: 'M18 6a4 4 0 0 0-4 4 7 7 0 0 0-7 7c0-5 4-5 4-10.5a4.5 4.5 0 1 0-9 0 2.5 2.5 0 0 0 5 0C7 10 3 11 3 17c0 2.8 2.2 5 5 5h10',\n      key: '980v8a',\n    },\n  ],\n  ['path', { d: 'M16 20c0-1.7 1.3-3 3-3h1a2 2 0 0 0 2-2v-2a4 4 0 0 0-4-4V4', key: '19wibc' }],\n  ['path', { d: 'M15.2 22a3 3 0 0 0-2.2-5', key: '13bpac' }],\n  ['path', { d: 'M18 13h.01', key: '9veqaj' }],\n]);\n\nexport default Squirrel;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,QAAA,GAAWC,gBAAA,CAAiB,UAAY,GAC5C,CACE,QACA;EACEC,CAAG;EACHC,GAAK;AACP,EACF,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,2DAA6D;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1F,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC5C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}