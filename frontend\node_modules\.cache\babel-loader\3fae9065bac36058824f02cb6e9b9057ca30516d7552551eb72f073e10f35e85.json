{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport React from \"react\";\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from \"../utils\";\nimport { withClerk } from \"./withClerk\";\nconst SignOutButton = withClerk(({\n  clerk,\n  children,\n  ...props\n}) => {\n  const {\n    signOutCallback,\n    signOutOptions,\n    ...rest\n  } = props;\n  children = normalizeWithDefaultValue(children, \"Sign out\");\n  const child = assertSingleChild(children)(\"SignOutButton\");\n  const clickHandler = () => {\n    return clerk.signOut(signOutCallback, signOutOptions);\n  };\n  const wrappedChildClickHandler = async e => {\n    await safeExecute(child.props.onClick)(e);\n    return clickHandler();\n  };\n  const childProps = {\n    ...rest,\n    onClick: wrappedChildClickHandler\n  };\n  return React.cloneElement(child, childProps);\n}, \"SignOutButton\");\nexport { SignOutButton };", "map": {"version": 3, "names": ["React", "assertSingleChild", "normalizeWithDefaultValue", "safeExecute", "with<PERSON><PERSON><PERSON>", "SignOutButton", "clerk", "children", "props", "signOutCallback", "signOutOptions", "rest", "child", "clickHandler", "signOut", "wrappedChildClickHandler", "e", "onClick", "childProps", "cloneElement"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\components\\SignOutButton.tsx"], "sourcesContent": ["import type { SignOutCallback, SignOutOptions } from '@clerk/types';\nimport React from 'react';\n\nimport type { WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport type SignOutButtonProps = {\n  signOutCallback?: SignOutCallback;\n  signOutOptions?: SignOutOptions;\n  children?: React.ReactNode;\n};\n\nexport const SignOutButton = withClerk(\n  ({ clerk, children, ...props }: React.PropsWithChildren<WithClerkProp<SignOutButtonProps>>) => {\n    const { signOutCallback, signOutOptions, ...rest } = props;\n\n    children = normalizeWithDefaultValue(children, 'Sign out');\n    const child = assertSingleChild(children)('SignOutButton');\n\n    const clickHandler = () => {\n      return clerk.signOut(signOutCallback, signOutOptions);\n    };\n\n    const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n      await safeExecute((child as any).props.onClick)(e);\n      return clickHandler();\n    };\n\n    const childProps = { ...rest, onClick: wrappedChildClickHandler };\n    return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n  },\n  'SignOutButton',\n);\n"], "mappings": ";AACA,OAAOA,KAAA,MAAW;AAGlB,SAASC,iBAAA,EAAmBC,yBAAA,EAA2BC,WAAA,QAAmB;AAC1E,SAASC,SAAA,QAAiB;AAQnB,MAAMC,aAAA,GAAgBD,SAAA,CAC3B,CAAC;EAAEE,KAAA;EAAOC,QAAA;EAAU,GAAGC;AAAM,MAAkE;EAC7F,MAAM;IAAEC,eAAA;IAAiBC,cAAA;IAAgB,GAAGC;EAAK,IAAIH,KAAA;EAErDD,QAAA,GAAWL,yBAAA,CAA0BK,QAAA,EAAU,UAAU;EACzD,MAAMK,KAAA,GAAQX,iBAAA,CAAkBM,QAAQ,EAAE,eAAe;EAEzD,MAAMM,YAAA,GAAeA,CAAA,KAAM;IACzB,OAAOP,KAAA,CAAMQ,OAAA,CAAQL,eAAA,EAAiBC,cAAc;EACtD;EAEA,MAAMK,wBAAA,GAAoD,MAAMC,CAAA,IAAK;IACnE,MAAMb,WAAA,CAAaS,KAAA,CAAcJ,KAAA,CAAMS,OAAO,EAAED,CAAC;IACjD,OAAOH,YAAA,CAAa;EACtB;EAEA,MAAMK,UAAA,GAAa;IAAE,GAAGP,IAAA;IAAMM,OAAA,EAASF;EAAyB;EAChE,OAAOf,KAAA,CAAMmB,YAAA,CAAaP,KAAA,EAAsCM,UAAU;AAC5E,GACA,eACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}