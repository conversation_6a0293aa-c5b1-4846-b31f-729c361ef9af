{"ast": null, "code": "'use strict';\n\nimport utils from './../utils.js';\n\n/**\n * Determines whether the payload is an error thrown by <PERSON>xios\n *\n * @param {*} payload The value to test\n *\n * @returns {boolean} True if the payload is an error thrown by Axios, otherwise false\n */\nexport default function isAxiosError(payload) {\n  return utils.isObject(payload) && payload.isAxiosError === true;\n}", "map": {"version": 3, "names": ["utils", "isAxiosError", "payload", "isObject"], "sources": ["C:/Users/<USER>/Desktop/file/u3summer/artech/artech/frontend/node_modules/axios/lib/helpers/isAxiosError.js"], "sourcesContent": ["'use strict';\n\nimport utils from './../utils.js';\n\n/**\n * Determines whether the payload is an error thrown by <PERSON>xios\n *\n * @param {*} payload The value to test\n *\n * @returns {boolean} True if the payload is an error thrown by Axios, otherwise false\n */\nexport default function isAxiosError(payload) {\n  return utils.isObject(payload) && (payload.isAxiosError === true);\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,KAAK,MAAM,eAAe;;AAEjC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,YAAYA,CAACC,OAAO,EAAE;EAC5C,OAAOF,KAAK,CAACG,QAAQ,CAACD,OAAO,CAAC,IAAKA,OAAO,CAACD,YAAY,KAAK,IAAK;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}