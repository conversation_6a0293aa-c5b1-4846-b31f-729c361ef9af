{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst Scaling = createLucideIcon(\"Scaling\", [[\"path\", {\n  d: \"M21 3 9 15\",\n  key: \"15kdhq\"\n}], [\"path\", {\n  d: \"M12 3H3v18h18v-9\",\n  key: \"8suug0\"\n}], [\"path\", {\n  d: \"M16 3h5v5\",\n  key: \"1806ms\"\n}], [\"path\", {\n  d: \"M14 15H9v-5\",\n  key: \"pi4jk9\"\n}]]);\nexport { Scaling as default };", "map": {"version": 3, "names": ["Sc<PERSON>", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\lucide-react\\src\\icons\\scaling.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name Scaling\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMyA5IDE1IiAvPgogIDxwYXRoIGQ9Ik0xMiAzSDN2MThoMTh2LTkiIC8+CiAgPHBhdGggZD0iTTE2IDNoNXY1IiAvPgogIDxwYXRoIGQ9Ik0xNCAxNUg5di01IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/scaling\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Scaling = createLucideIcon('Scaling', [\n  ['path', { d: 'M21 3 9 15', key: '15kdhq' }],\n  ['path', { d: 'M12 3H3v18h18v-9', key: '8suug0' }],\n  ['path', { d: 'M16 3h5v5', key: '1806ms' }],\n  ['path', { d: 'M14 15H9v-5', key: 'pi4jk9' }],\n]);\n\nexport default Scaling;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,OAAA,GAAUC,gBAAA,CAAiB,SAAW,GAC1C,CAAC,MAAQ;EAAEC,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CAAC,MAAQ;EAAED,CAAA,EAAG,kBAAoB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACjD,CAAC,MAAQ;EAAED,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CAAC,MAAQ;EAAED,CAAA,EAAG,aAAe;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}