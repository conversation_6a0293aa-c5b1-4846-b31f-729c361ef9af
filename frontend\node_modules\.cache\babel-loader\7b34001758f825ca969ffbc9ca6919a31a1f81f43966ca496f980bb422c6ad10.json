{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport { UserContext, useUserContext } from \"@clerk/shared/react\";\nexport { UserContext, useUserContext };", "map": {"version": 3, "names": ["UserContext", "useUserContext"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\contexts\\UserContext.tsx"], "sourcesContent": ["export { UserContext, useUserContext } from '@clerk/shared/react';\n"], "mappings": ";AAAA,SAASA,WAAA,EAAaC,cAAA,QAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}