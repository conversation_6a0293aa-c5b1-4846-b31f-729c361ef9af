{"ast": null, "code": "import { deprecated } from \"./chunk-IC4FGZI3.mjs\";\n\n// src/proxy.ts\nfunction isValidProxyUrl(key) {\n  if (!key) {\n    return true;\n  }\n  return isHttpOrHttps(key) || isProxyUrlRelative(key);\n}\nfunction isHttpOrHttps(key) {\n  return /^http(s)?:\\/\\//.test(key || \"\");\n}\nfunction isProxyUrlRelative(key) {\n  return key.startsWith(\"/\");\n}\nfunction proxyUrlToAbsoluteURL(url) {\n  if (!url) {\n    return \"\";\n  }\n  return isProxyUrlRelative(url) ? new URL(url, window.location.origin).toString() : url;\n}\nfunction getRequestUrl({\n  request,\n  relativePath\n}) {\n  var _a, _b, _c, _d, _e, _f;\n  deprecated(\"getRequestUrl\", \"Use `buildRequestUrl` from @clerk/backend instead.\");\n  const {\n    headers,\n    url: initialUrl\n  } = request;\n  const url = new URL(initialUrl);\n  const host = (_c = (_b = (_a = headers.get(\"X-Forwarded-Host\")) != null ? _a : headers.get(\"host\")) != null ? _b : headers[\"host\"]) != null ? _c : url.host;\n  let protocol = (_f = (_e = (_d = headers.get(\"X-Forwarded-Proto\")) != null ? _d : headers[\"X-Forwarded-Proto\"]) == null ? void 0 : _e.split(\",\")[0]) != null ? _f : url.protocol;\n  protocol = protocol.replace(/[:/]/, \"\");\n  return new URL(relativePath || url.pathname, `${protocol}://${host}`);\n}\nexport { isValidProxyUrl, isHttpOrHttps, isProxyUrlRelative, proxyUrlToAbsoluteURL, getRequestUrl };", "map": {"version": 3, "names": ["isValidProxyUrl", "key", "isHttpOrHttps", "isProxyUrlRelative", "test", "startsWith", "proxyUrlToAbsoluteURL", "url", "URL", "window", "location", "origin", "toString", "getRequestUrl", "request", "relativePath", "_a", "_b", "_c", "_d", "_e", "_f", "deprecated", "headers", "initialUrl", "host", "get", "protocol", "split", "replace", "pathname"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\proxy.ts"], "sourcesContent": ["import { deprecated } from './deprecated';\n\nexport function isValidProxyUrl(key: string | undefined) {\n  if (!key) {\n    return true;\n  }\n\n  return isHttpOrHttps(key) || isProxyUrlRelative(key);\n}\n\nexport function isHttpOrHttps(key: string | undefined) {\n  return /^http(s)?:\\/\\//.test(key || '');\n}\n\nexport function isProxyUrlRelative(key: string) {\n  return key.startsWith('/');\n}\n\nexport function proxyUrlToAbsoluteURL(url: string | undefined): string {\n  if (!url) {\n    return '';\n  }\n  return isProxyUrlRelative(url) ? new URL(url, window.location.origin).toString() : url;\n}\n\n/**\n * @deprecated Use `buildRequestUrl` from @clerk/backend\n */\nexport function getRequestUrl({ request, relativePath }: { request: Request; relativePath?: string }): URL {\n  deprecated('getRequestUrl', 'Use `buildRequestUrl` from @clerk/backend instead.');\n  const { headers, url: initialUrl } = request;\n  const url = new URL(initialUrl);\n  const host = headers.get('X-Forwarded-Host') ?? headers.get('host') ?? (headers as any)['host'] ?? url.host;\n\n  // X-Forwarded-Proto could be 'https, http'\n  let protocol =\n    (headers.get('X-Forwarded-Proto') ?? (headers as any)['X-Forwarded-Proto'])?.split(',')[0] ?? url.protocol;\n  protocol = protocol.replace(/[:/]/, '');\n\n  return new URL(relativePath || url.pathname, `${protocol}://${host}`);\n}\n"], "mappings": ";;;AAEO,SAASA,gBAAgBC,GAAA,EAAyB;EACvD,IAAI,CAACA,GAAA,EAAK;IACR,OAAO;EACT;EAEA,OAAOC,aAAA,CAAcD,GAAG,KAAKE,kBAAA,CAAmBF,GAAG;AACrD;AAEO,SAASC,cAAcD,GAAA,EAAyB;EACrD,OAAO,iBAAiBG,IAAA,CAAKH,GAAA,IAAO,EAAE;AACxC;AAEO,SAASE,mBAAmBF,GAAA,EAAa;EAC9C,OAAOA,GAAA,CAAII,UAAA,CAAW,GAAG;AAC3B;AAEO,SAASC,sBAAsBC,GAAA,EAAiC;EACrE,IAAI,CAACA,GAAA,EAAK;IACR,OAAO;EACT;EACA,OAAOJ,kBAAA,CAAmBI,GAAG,IAAI,IAAIC,GAAA,CAAID,GAAA,EAAKE,MAAA,CAAOC,QAAA,CAASC,MAAM,EAAEC,QAAA,CAAS,IAAIL,GAAA;AACrF;AAKO,SAASM,cAAc;EAAEC,OAAA;EAASC;AAAa,GAAqD;EA5B3G,IAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;EA6BEC,UAAA,CAAW,iBAAiB,oDAAoD;EAChF,MAAM;IAAEC,OAAA;IAAShB,GAAA,EAAKiB;EAAW,IAAIV,OAAA;EACrC,MAAMP,GAAA,GAAM,IAAIC,GAAA,CAAIgB,UAAU;EAC9B,MAAMC,IAAA,IAAOP,EAAA,IAAAD,EAAA,IAAAD,EAAA,GAAAO,OAAA,CAAQG,GAAA,CAAI,kBAAkB,MAA9B,OAAAV,EAAA,GAAmCO,OAAA,CAAQG,GAAA,CAAI,MAAM,MAArD,OAAAT,EAAA,GAA2DM,OAAA,CAAgB,MAAM,MAAjF,OAAAL,EAAA,GAAsFX,GAAA,CAAIkB,IAAA;EAGvG,IAAIE,QAAA,IACDN,EAAA,IAAAD,EAAA,IAAAD,EAAA,GAAAI,OAAA,CAAQG,GAAA,CAAI,mBAAmB,MAA/B,OAAAP,EAAA,GAAqCI,OAAA,CAAgB,mBAAmB,MAAxE,gBAAAH,EAAA,CAA4EQ,KAAA,CAAM,KAAK,OAAvF,OAAAP,EAAA,GAA6Fd,GAAA,CAAIoB,QAAA;EACpGA,QAAA,GAAWA,QAAA,CAASE,OAAA,CAAQ,QAAQ,EAAE;EAEtC,OAAO,IAAIrB,GAAA,CAAIO,YAAA,IAAgBR,GAAA,CAAIuB,QAAA,EAAU,GAAGH,QAAQ,MAAMF,IAAI,EAAE;AACtE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}