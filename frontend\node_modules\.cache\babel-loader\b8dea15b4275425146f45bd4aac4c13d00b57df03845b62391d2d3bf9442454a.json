{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport React from \"react\";\nimport { useSessionContext } from \"../contexts/SessionContext\";\nimport { hocChildrenNotAFunctionError } from \"../errors\";\nconst withSession = (Component, displayName) => {\n  displayName = displayName || Component.displayName || Component.name || \"Component\";\n  Component.displayName = displayName;\n  const HOC = props => {\n    const session = useSessionContext();\n    if (!session) {\n      return null;\n    }\n    return /* @__PURE__ */React.createElement(Component, {\n      ...props,\n      session\n    });\n  };\n  HOC.displayName = `withSession(${displayName})`;\n  return HOC;\n};\nconst WithSession = ({\n  children\n}) => {\n  const session = useSessionContext();\n  if (typeof children !== \"function\") {\n    throw new Error(hocChildrenNotAFunctionError);\n  }\n  if (!session) {\n    return null;\n  }\n  return /* @__PURE__ */React.createElement(React.Fragment, null, children(session));\n};\nexport { WithSession, withSession };", "map": {"version": 3, "names": ["React", "useSessionContext", "hocChildrenNotAFunctionError", "withSession", "Component", "displayName", "name", "HOC", "props", "session", "createElement", "WithSession", "children", "Error", "Fragment"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\components\\withSession.tsx"], "sourcesContent": ["import type { SessionResource } from '@clerk/types';\nimport React from 'react';\n\nimport { useSessionContext } from '../contexts/SessionContext';\nimport { hocChildrenNotAFunctionError } from '../errors';\n\nexport const withSession = <P extends { session: SessionResource }>(\n  Component: React.ComponentType<P>,\n  displayName?: string,\n) => {\n  displayName = displayName || Component.displayName || Component.name || 'Component';\n  Component.displayName = displayName;\n  const HOC: React.FC<Omit<P, 'session'>> = (props: Omit<P, 'session'>) => {\n    const session = useSessionContext();\n\n    if (!session) {\n      return null;\n    }\n\n    return (\n      <Component\n        {...(props as P)}\n        session={session}\n      />\n    );\n  };\n\n  HOC.displayName = `withSession(${displayName})`;\n  return HOC;\n};\n\nexport const WithSession: React.FC<{\n  children: (session: SessionResource) => React.ReactNode;\n}> = ({ children }) => {\n  const session = useSessionContext();\n\n  if (typeof children !== 'function') {\n    throw new Error(hocChildrenNotAFunctionError);\n  }\n\n  if (!session) {\n    return null;\n  }\n\n  return <>{children(session)}</>;\n};\n"], "mappings": ";AACA,OAAOA,KAAA,MAAW;AAElB,SAASC,iBAAA,QAAyB;AAClC,SAASC,4BAAA,QAAoC;AAEtC,MAAMC,WAAA,GAAcA,CACzBC,SAAA,EACAC,WAAA,KACG;EACHA,WAAA,GAAcA,WAAA,IAAeD,SAAA,CAAUC,WAAA,IAAeD,SAAA,CAAUE,IAAA,IAAQ;EACxEF,SAAA,CAAUC,WAAA,GAAcA,WAAA;EACxB,MAAME,GAAA,GAAqCC,KAAA,IAA8B;IACvE,MAAMC,OAAA,GAAUR,iBAAA,CAAkB;IAElC,IAAI,CAACQ,OAAA,EAAS;MACZ,OAAO;IACT;IAEA,OACE,eAAAT,KAAA,CAAAU,aAAA,CAACN,SAAA;MACE,GAAII,KAAA;MACLC;IAAA,CACF;EAEJ;EAEAF,GAAA,CAAIF,WAAA,GAAc,eAAeA,WAAW;EAC5C,OAAOE,GAAA;AACT;AAEO,MAAMI,WAAA,GAERA,CAAC;EAAEC;AAAS,MAAM;EACrB,MAAMH,OAAA,GAAUR,iBAAA,CAAkB;EAElC,IAAI,OAAOW,QAAA,KAAa,YAAY;IAClC,MAAM,IAAIC,KAAA,CAAMX,4BAA4B;EAC9C;EAEA,IAAI,CAACO,OAAA,EAAS;IACZ,OAAO;EACT;EAEA,OAAO,eAAAT,KAAA,CAAAU,aAAA,CAAAV,KAAA,CAAAc,QAAA,QAAGF,QAAA,CAASH,OAAO,CAAE;AAC9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}