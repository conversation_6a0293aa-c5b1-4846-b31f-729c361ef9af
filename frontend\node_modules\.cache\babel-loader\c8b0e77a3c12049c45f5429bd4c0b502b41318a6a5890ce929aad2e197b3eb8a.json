{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport React from \"react\";\nimport { useUserContext } from \"../contexts/UserContext\";\nimport { hocChildrenNotAFunctionError } from \"../errors\";\nconst withUser = (Component, displayName) => {\n  displayName = displayName || Component.displayName || Component.name || \"Component\";\n  Component.displayName = displayName;\n  const HOC = props => {\n    const user = useUserContext();\n    if (!user) {\n      return null;\n    }\n    return /* @__PURE__ */React.createElement(Component, {\n      ...props,\n      user\n    });\n  };\n  HOC.displayName = `withUser(${displayName})`;\n  return HOC;\n};\nconst WithUser = ({\n  children\n}) => {\n  const user = useUserContext();\n  if (typeof children !== \"function\") {\n    throw new Error(hocChildrenNotAFunctionError);\n  }\n  if (!user) {\n    return null;\n  }\n  return /* @__PURE__ */React.createElement(React.Fragment, null, children(user));\n};\nexport { WithUser, withUser };", "map": {"version": 3, "names": ["React", "useUserContext", "hocChildrenNotAFunctionError", "with<PERSON><PERSON>", "Component", "displayName", "name", "HOC", "props", "user", "createElement", "With<PERSON>ser", "children", "Error", "Fragment"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\components\\withUser.tsx"], "sourcesContent": ["import type { UserResource } from '@clerk/types';\nimport React from 'react';\n\nimport { useUserContext } from '../contexts/UserContext';\nimport { hocChildrenNotAFunctionError } from '../errors';\n\nexport const withUser = <P extends { user: UserResource }>(Component: React.ComponentType<P>, displayName?: string) => {\n  displayName = displayName || Component.displayName || Component.name || 'Component';\n  Component.displayName = displayName;\n  const HOC: React.FC<Omit<P, 'user'>> = (props: Omit<P, 'user'>) => {\n    const user = useUserContext();\n\n    if (!user) {\n      return null;\n    }\n\n    return (\n      <Component\n        {...(props as P)}\n        user={user}\n      />\n    );\n  };\n\n  HOC.displayName = `withUser(${displayName})`;\n  return HOC;\n};\n\nexport const WithUser: React.FC<{\n  children: (user: UserResource) => React.ReactNode;\n}> = ({ children }) => {\n  const user = useUserContext();\n\n  if (typeof children !== 'function') {\n    throw new Error(hocChildrenNotAFunctionError);\n  }\n\n  if (!user) {\n    return null;\n  }\n\n  return <>{children(user)}</>;\n};\n"], "mappings": ";AACA,OAAOA,KAAA,MAAW;AAElB,SAASC,cAAA,QAAsB;AAC/B,SAASC,4BAAA,QAAoC;AAEtC,MAAMC,QAAA,GAAWA,CAAmCC,SAAA,EAAmCC,WAAA,KAAyB;EACrHA,WAAA,GAAcA,WAAA,IAAeD,SAAA,CAAUC,WAAA,IAAeD,SAAA,CAAUE,IAAA,IAAQ;EACxEF,SAAA,CAAUC,WAAA,GAAcA,WAAA;EACxB,MAAME,GAAA,GAAkCC,KAAA,IAA2B;IACjE,MAAMC,IAAA,GAAOR,cAAA,CAAe;IAE5B,IAAI,CAACQ,IAAA,EAAM;MACT,OAAO;IACT;IAEA,OACE,eAAAT,KAAA,CAAAU,aAAA,CAACN,SAAA;MACE,GAAII,KAAA;MACLC;IAAA,CACF;EAEJ;EAEAF,GAAA,CAAIF,WAAA,GAAc,YAAYA,WAAW;EACzC,OAAOE,GAAA;AACT;AAEO,MAAMI,QAAA,GAERA,CAAC;EAAEC;AAAS,MAAM;EACrB,MAAMH,IAAA,GAAOR,cAAA,CAAe;EAE5B,IAAI,OAAOW,QAAA,KAAa,YAAY;IAClC,MAAM,IAAIC,KAAA,CAAMX,4BAA4B;EAC9C;EAEA,IAAI,CAACO,IAAA,EAAM;IACT,OAAO;EACT;EAEA,OAAO,eAAAT,KAAA,CAAAU,aAAA,CAAAV,KAAA,CAAAc,QAAA,QAAGF,QAAA,CAASH,IAAI,CAAE;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}