{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nconst deriveState = (clerkLoaded, state, initialState) => {\n  if (!clerkLoaded && initialState) {\n    return deriveFromSsrInitialState(initialState);\n  }\n  return deriveFromClientSideState(state);\n};\nconst deriveFromSsrInitialState = initialState => {\n  const userId = initialState.userId;\n  const user = initialState.user;\n  const sessionId = initialState.sessionId;\n  const session = initialState.session;\n  const organization = initialState.organization;\n  const orgId = initialState.orgId;\n  const orgRole = initialState.orgRole;\n  const orgPermissions = initialState.orgPermissions;\n  const orgSlug = initialState.orgSlug;\n  const actor = initialState.actor;\n  return {\n    userId,\n    user,\n    sessionId,\n    session,\n    organization,\n    orgId,\n    orgRole,\n    orgPermissions,\n    orgSlug,\n    actor,\n    lastOrganizationInvitation: null,\n    lastOrganizationMember: null\n  };\n};\nconst deriveFromClientSideState = state => {\n  var _a;\n  const userId = state.user ? state.user.id : state.user;\n  const user = state.user;\n  const sessionId = state.session ? state.session.id : state.session;\n  const session = state.session;\n  const actor = session == null ? void 0 : session.actor;\n  const organization = state.organization;\n  const orgId = state.organization ? state.organization.id : state.organization;\n  const orgSlug = organization == null ? void 0 : organization.slug;\n  const membership = organization ? (_a = user == null ? void 0 : user.organizationMemberships) == null ? void 0 : _a.find(om => om.organization.id === orgId) : organization;\n  const orgPermissions = membership ? membership.permissions : membership;\n  const orgRole = membership ? membership.role : membership;\n  const lastOrganizationInvitation = state.lastOrganizationInvitation;\n  const lastOrganizationMember = state.lastOrganizationMember;\n  return {\n    userId,\n    user,\n    sessionId,\n    session,\n    organization,\n    orgId,\n    orgRole,\n    orgSlug,\n    orgPermissions,\n    actor,\n    lastOrganizationInvitation,\n    lastOrganizationMember\n  };\n};\nexport { deriveState };", "map": {"version": 3, "names": ["deriveState", "clerkLoaded", "state", "initialState", "deriveFromSsrInitialState", "deriveFromClientSideState", "userId", "user", "sessionId", "session", "organization", "orgId", "orgRole", "orgPermissions", "orgSlug", "actor", "lastOrganizationInvitation", "lastOrganizationMember", "_a", "id", "slug", "membership", "organizationMemberships", "find", "om", "permissions", "role"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\utils\\deriveState.ts"], "sourcesContent": ["import type {\n  ActiveSessionResource,\n  InitialState,\n  MembershipRole,\n  OrganizationCustomPermissionKey,\n  OrganizationResource,\n  Resources,\n  UserResource,\n} from '@clerk/types';\n\nexport const deriveState = (clerkLoaded: boolean, state: Resources, initialState: InitialState | undefined) => {\n  if (!clerkLoaded && initialState) {\n    return deriveFromSsrInitialState(initialState);\n  }\n  return deriveFromClientSideState(state);\n};\n\nconst deriveFromSsrInitialState = (initialState: InitialState) => {\n  const userId = initialState.userId;\n  const user = initialState.user as UserResource;\n  const sessionId = initialState.sessionId;\n  const session = initialState.session as ActiveSessionResource;\n  const organization = initialState.organization as OrganizationResource;\n  const orgId = initialState.orgId;\n  const orgRole = initialState.orgRole as MembershipRole;\n  const orgPermissions = initialState.orgPermissions as OrganizationCustomPermissionKey[];\n  const orgSlug = initialState.orgSlug;\n  const actor = initialState.actor;\n\n  return {\n    userId,\n    user,\n    sessionId,\n    session,\n    organization,\n    orgId,\n    orgRole,\n    orgPermissions,\n    orgSlug,\n    actor,\n    lastOrganizationInvitation: null,\n    lastOrganizationMember: null,\n  };\n};\n\nconst deriveFromClientSideState = (state: Resources) => {\n  const userId: string | null | undefined = state.user ? state.user.id : state.user;\n  const user = state.user;\n  const sessionId: string | null | undefined = state.session ? state.session.id : state.session;\n  const session = state.session;\n  const actor = session?.actor;\n  const organization = state.organization;\n  const orgId: string | null | undefined = state.organization ? state.organization.id : state.organization;\n  const orgSlug = organization?.slug;\n  const membership = organization\n    ? user?.organizationMemberships?.find(om => om.organization.id === orgId)\n    : organization;\n  const orgPermissions = membership ? membership.permissions : membership;\n  const orgRole = membership ? membership.role : membership;\n\n  const lastOrganizationInvitation = state.lastOrganizationInvitation;\n  const lastOrganizationMember = state.lastOrganizationMember;\n\n  return {\n    userId,\n    user,\n    sessionId,\n    session,\n    organization,\n    orgId,\n    orgRole,\n    orgSlug,\n    orgPermissions,\n    actor,\n    lastOrganizationInvitation,\n    lastOrganizationMember,\n  };\n};\n"], "mappings": ";AAUO,MAAMA,WAAA,GAAcA,CAACC,WAAA,EAAsBC,KAAA,EAAkBC,YAAA,KAA2C;EAC7G,IAAI,CAACF,WAAA,IAAeE,YAAA,EAAc;IAChC,OAAOC,yBAAA,CAA0BD,YAAY;EAC/C;EACA,OAAOE,yBAAA,CAA0BH,KAAK;AACxC;AAEA,MAAME,yBAAA,GAA6BD,YAAA,IAA+B;EAChE,MAAMG,MAAA,GAASH,YAAA,CAAaG,MAAA;EAC5B,MAAMC,IAAA,GAAOJ,YAAA,CAAaI,IAAA;EAC1B,MAAMC,SAAA,GAAYL,YAAA,CAAaK,SAAA;EAC/B,MAAMC,OAAA,GAAUN,YAAA,CAAaM,OAAA;EAC7B,MAAMC,YAAA,GAAeP,YAAA,CAAaO,YAAA;EAClC,MAAMC,KAAA,GAAQR,YAAA,CAAaQ,KAAA;EAC3B,MAAMC,OAAA,GAAUT,YAAA,CAAaS,OAAA;EAC7B,MAAMC,cAAA,GAAiBV,YAAA,CAAaU,cAAA;EACpC,MAAMC,OAAA,GAAUX,YAAA,CAAaW,OAAA;EAC7B,MAAMC,KAAA,GAAQZ,YAAA,CAAaY,KAAA;EAE3B,OAAO;IACLT,MAAA;IACAC,IAAA;IACAC,SAAA;IACAC,OAAA;IACAC,YAAA;IACAC,KAAA;IACAC,OAAA;IACAC,cAAA;IACAC,OAAA;IACAC,KAAA;IACAC,0BAAA,EAA4B;IAC5BC,sBAAA,EAAwB;EAC1B;AACF;AAEA,MAAMZ,yBAAA,GAA6BH,KAAA,IAAqB;EA7CxD,IAAAgB,EAAA;EA8CE,MAAMZ,MAAA,GAAoCJ,KAAA,CAAMK,IAAA,GAAOL,KAAA,CAAMK,IAAA,CAAKY,EAAA,GAAKjB,KAAA,CAAMK,IAAA;EAC7E,MAAMA,IAAA,GAAOL,KAAA,CAAMK,IAAA;EACnB,MAAMC,SAAA,GAAuCN,KAAA,CAAMO,OAAA,GAAUP,KAAA,CAAMO,OAAA,CAAQU,EAAA,GAAKjB,KAAA,CAAMO,OAAA;EACtF,MAAMA,OAAA,GAAUP,KAAA,CAAMO,OAAA;EACtB,MAAMM,KAAA,GAAQN,OAAA,oBAAAA,OAAA,CAASM,KAAA;EACvB,MAAML,YAAA,GAAeR,KAAA,CAAMQ,YAAA;EAC3B,MAAMC,KAAA,GAAmCT,KAAA,CAAMQ,YAAA,GAAeR,KAAA,CAAMQ,YAAA,CAAaS,EAAA,GAAKjB,KAAA,CAAMQ,YAAA;EAC5F,MAAMI,OAAA,GAAUJ,YAAA,oBAAAA,YAAA,CAAcU,IAAA;EAC9B,MAAMC,UAAA,GAAaX,YAAA,IACfQ,EAAA,GAAAX,IAAA,oBAAAA,IAAA,CAAMe,uBAAA,KAAN,gBAAAJ,EAAA,CAA+BK,IAAA,CAAKC,EAAA,IAAMA,EAAA,CAAGd,YAAA,CAAaS,EAAA,KAAOR,KAAA,IACjED,YAAA;EACJ,MAAMG,cAAA,GAAiBQ,UAAA,GAAaA,UAAA,CAAWI,WAAA,GAAcJ,UAAA;EAC7D,MAAMT,OAAA,GAAUS,UAAA,GAAaA,UAAA,CAAWK,IAAA,GAAOL,UAAA;EAE/C,MAAML,0BAAA,GAA6Bd,KAAA,CAAMc,0BAAA;EACzC,MAAMC,sBAAA,GAAyBf,KAAA,CAAMe,sBAAA;EAErC,OAAO;IACLX,MAAA;IACAC,IAAA;IACAC,SAAA;IACAC,OAAA;IACAC,YAAA;IACAC,KAAA;IACAC,OAAA;IACAE,OAAA;IACAD,cAAA;IACAE,KAAA;IACAC,0BAAA;IACAC;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}