{"ast": null, "code": "// src/file.ts\nfunction readJSONFile(file) {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.addEventListener(\"load\", function () {\n      const result = JSON.parse(reader.result);\n      resolve(result);\n    });\n    reader.addEventListener(\"error\", reject);\n    reader.readAsText(file);\n  });\n}\nvar MimeTypeToExtensionMap = Object.freeze({\n  \"image/png\": \"png\",\n  \"image/jpeg\": \"jpg\",\n  \"image/gif\": \"gif\",\n  \"image/webp\": \"webp\",\n  \"image/x-icon\": \"ico\",\n  \"image/vnd.microsoft.icon\": \"ico\"\n});\nvar extension = mimeType => {\n  return MimeTypeToExtensionMap[mimeType];\n};\nexport { readJSONFile, extension };", "map": {"version": 3, "names": ["readJSONFile", "file", "Promise", "resolve", "reject", "reader", "FileReader", "addEventListener", "result", "JSON", "parse", "readAsText", "MimeTypeToExtensionMap", "Object", "freeze", "extension", "mimeType"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\file.ts"], "sourcesContent": ["/**\n * Read an expected JSON type File.\n *\n * Probably paired with:\n *  <input type='file' accept='application/JSON' ... />\n */\nexport function readJSONFile(file: File): Promise<unknown> {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.addEventListener('load', function () {\n      const result = JSON.parse(reader.result as string);\n      resolve(result);\n    });\n\n    reader.addEventListener('error', reject);\n    reader.readAsText(file);\n  });\n}\n\nconst MimeTypeToExtensionMap = Object.freeze({\n  'image/png': 'png',\n  'image/jpeg': 'jpg',\n  'image/gif': 'gif',\n  'image/webp': 'webp',\n  'image/x-icon': 'ico',\n  'image/vnd.microsoft.icon': 'ico',\n} as const);\n\nexport type SupportedMimeType = keyof typeof MimeTypeToExtensionMap;\n\nexport const extension = (mimeType: SupportedMimeType): string => {\n  return MimeTypeToExtensionMap[mimeType];\n};\n"], "mappings": ";AAMO,SAASA,aAAaC,IAAA,EAA8B;EACzD,OAAO,IAAIC,OAAA,CAAQ,CAACC,OAAA,EAASC,MAAA,KAAW;IACtC,MAAMC,MAAA,GAAS,IAAIC,UAAA,CAAW;IAC9BD,MAAA,CAAOE,gBAAA,CAAiB,QAAQ,YAAY;MAC1C,MAAMC,MAAA,GAASC,IAAA,CAAKC,KAAA,CAAML,MAAA,CAAOG,MAAgB;MACjDL,OAAA,CAAQK,MAAM;IAChB,CAAC;IAEDH,MAAA,CAAOE,gBAAA,CAAiB,SAASH,MAAM;IACvCC,MAAA,CAAOM,UAAA,CAAWV,IAAI;EACxB,CAAC;AACH;AAEA,IAAMW,sBAAA,GAAyBC,MAAA,CAAOC,MAAA,CAAO;EAC3C,aAAa;EACb,cAAc;EACd,aAAa;EACb,cAAc;EACd,gBAAgB;EAChB,4BAA4B;AAC9B,CAAU;AAIH,IAAMC,SAAA,GAAaC,QAAA,IAAwC;EAChE,OAAOJ,sBAAA,CAAuBI,QAAQ;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}