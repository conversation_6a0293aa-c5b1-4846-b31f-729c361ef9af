{"ast": null, "code": "// src/handleValueOrFn.ts\nfunction handleValueOrFn(value, url, defaultValue) {\n  if (typeof value === \"function\") {\n    return value(url);\n  }\n  if (typeof value !== \"undefined\") {\n    return value;\n  }\n  if (typeof defaultValue !== \"undefined\") {\n    return defaultValue;\n  }\n  return void 0;\n}\nexport { handleValueOrFn };", "map": {"version": 3, "names": ["handleValueOrFn", "value", "url", "defaultValue"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\handleValueOrFn.ts"], "sourcesContent": ["type VOrFnReturnsV<T> = T | undefined | ((v: URL) => T);\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL): T | undefined;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue: T): T;\nexport function handleValueOrFn<T>(value: VOrFnReturnsV<T>, url: URL, defaultValue?: unknown): unknown {\n  if (typeof value === 'function') {\n    return (value as (v: URL) => T)(url);\n  }\n\n  if (typeof value !== 'undefined') {\n    return value;\n  }\n\n  if (typeof defaultValue !== 'undefined') {\n    return defaultValue;\n  }\n\n  return undefined;\n}\n"], "mappings": ";AAGO,SAASA,gBAAmBC,KAAA,EAAyBC,GAAA,EAAUC,YAAA,EAAiC;EACrG,IAAI,OAAOF,KAAA,KAAU,YAAY;IAC/B,OAAQA,KAAA,CAAwBC,GAAG;EACrC;EAEA,IAAI,OAAOD,KAAA,KAAU,aAAa;IAChC,OAAOA,KAAA;EACT;EAEA,IAAI,OAAOE,YAAA,KAAiB,aAAa;IACvC,OAAOA,YAAA;EACT;EAEA,OAAO;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}