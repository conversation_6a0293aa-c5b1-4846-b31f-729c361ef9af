{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport { createDevOrStagingUrlCache } from \"@clerk/shared/keys\";\nconst {\n  isDevOrStagingUrl\n} = createDevOrStagingUrlCache();\nexport { isDevOrStagingUrl };", "map": {"version": 3, "names": ["createDevOrStagingUrlCache", "isDevOrStagingUrl"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\utils\\isDevOrStageUrl.tsx"], "sourcesContent": ["import { createDevOrStagingUrlCache } from '@clerk/shared/keys';\nconst { isDevOrStagingUrl } = createDevOrStagingUrlCache();\nexport { isDevOrStagingUrl };\n"], "mappings": ";AAAA,SAASA,0BAAA,QAAkC;AAC3C,MAAM;EAAEC;AAAkB,IAAID,0BAAA,CAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}