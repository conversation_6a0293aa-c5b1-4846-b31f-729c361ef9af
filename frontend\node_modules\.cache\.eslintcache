[{"C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\components\\LandingPage.js": "3", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\components\\Header.js": "4", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\components\\Recommendations.js": "5", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\components\\ChatInterface.js": "6", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\components\\Profile.js": "7", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\components\\Dashboard.js": "8", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\contexts\\UserContext.js": "9", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\components\\LoadingSpinner.js": "10", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\services\\apiService.js": "11"}, {"size": 254, "mtime": 1753239809513, "results": "12", "hashOfConfig": "13"}, {"size": 2144, "mtime": 1753239465927, "results": "14", "hashOfConfig": "13"}, {"size": 4624, "mtime": 1753239561343, "results": "15", "hashOfConfig": "13"}, {"size": 4191, "mtime": 1753239536497, "results": "16", "hashOfConfig": "13"}, {"size": 13095, "mtime": 1753239784490, "results": "17", "hashOfConfig": "13"}, {"size": 12364, "mtime": 1753239722582, "results": "18", "hashOfConfig": "13"}, {"size": 13762, "mtime": 1753239663108, "results": "19", "hashOfConfig": "13"}, {"size": 7945, "mtime": 1753239599167, "results": "20", "hashOfConfig": "13"}, {"size": 6034, "mtime": 1753239493070, "results": "21", "hashOfConfig": "13"}, {"size": 1479, "mtime": 1753239799999, "results": "22", "hashOfConfig": "13"}, {"size": 3133, "mtime": 1753239510845, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "czot25", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\components\\LandingPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\components\\Header.js", [], [], "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\components\\Recommendations.js", ["57", "58", "59"], [], "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\components\\ChatInterface.js", [], [], "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\components\\Profile.js", ["60"], [], "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\components\\Dashboard.js", ["61", "62", "63"], [], "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\contexts\\UserContext.js", ["64"], [], "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\components\\LoadingSpinner.js", [], [], "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\src\\services\\apiService.js", [], [], {"ruleId": "65", "severity": 1, "message": "66", "line": 8, "column": 3, "nodeType": "67", "messageId": "68", "endLine": 8, "endColumn": 10}, {"ruleId": "65", "severity": 1, "message": "69", "line": 12, "column": 3, "nodeType": "67", "messageId": "68", "endLine": 12, "endColumn": 9}, {"ruleId": "65", "severity": 1, "message": "70", "line": 280, "column": 11, "nodeType": "67", "messageId": "68", "endLine": 280, "endColumn": 15}, {"ruleId": "65", "severity": 1, "message": "71", "line": 155, "column": 7, "nodeType": "67", "messageId": "68", "endLine": 155, "endColumn": 24}, {"ruleId": "65", "severity": 1, "message": "72", "line": 12, "column": 3, "nodeType": "67", "messageId": "68", "endLine": 12, "endColumn": 12}, {"ruleId": "65", "severity": 1, "message": "73", "line": 205, "column": 10, "nodeType": "67", "messageId": "68", "endLine": 205, "endColumn": 15}, {"ruleId": "65", "severity": 1, "message": "74", "line": 205, "column": 17, "nodeType": "67", "messageId": "68", "endLine": 205, "endColumn": 25}, {"ruleId": "75", "severity": 1, "message": "76", "line": 30, "column": 6, "nodeType": "77", "endLine": 30, "endColumn": 24, "suggestions": "78"}, "no-unused-vars", "'Palette' is defined but never used.", "Identifier", "unusedVar", "'Search' is defined but never used.", "'user' is assigned a value but never used.", "'PreferenceSection' is assigned a value but never used.", "'BarChart3' is defined but never used.", "'stats' is assigned a value but never used.", "'setStats' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'initializeUser'. Either include it or remove the dependency array.", "ArrayExpression", ["79"], {"desc": "80", "fix": "81"}, "Update the dependencies array to be: [initializeUser, isLoaded, userId]", {"range": "82", "text": "83"}, [954, 972], "[initializeUser, isLoaded, userId]"]