{"ast": null, "code": "import CanceledError from \"../cancel/CanceledError.js\";\nimport AxiosError from \"../core/AxiosError.js\";\nimport utils from '../utils.js';\nconst composeSignals = (signals, timeout) => {\n  const {\n    length\n  } = signals = signals ? signals.filter(Boolean) : [];\n  if (timeout || length) {\n    let controller = new AbortController();\n    let aborted;\n    const onabort = function (reason) {\n      if (!aborted) {\n        aborted = true;\n        unsubscribe();\n        const err = reason instanceof Error ? reason : this.reason;\n        controller.abort(err instanceof AxiosError ? err : new CanceledError(err instanceof Error ? err.message : err));\n      }\n    };\n    let timer = timeout && setTimeout(() => {\n      timer = null;\n      onabort(new AxiosError(`timeout ${timeout} of ms exceeded`, AxiosError.ETIMEDOUT));\n    }, timeout);\n    const unsubscribe = () => {\n      if (signals) {\n        timer && clearTimeout(timer);\n        timer = null;\n        signals.forEach(signal => {\n          signal.unsubscribe ? signal.unsubscribe(onabort) : signal.removeEventListener('abort', onabort);\n        });\n        signals = null;\n      }\n    };\n    signals.forEach(signal => signal.addEventListener('abort', onabort));\n    const {\n      signal\n    } = controller;\n    signal.unsubscribe = () => utils.asap(unsubscribe);\n    return signal;\n  }\n};\nexport default composeSignals;", "map": {"version": 3, "names": ["CanceledError", "AxiosError", "utils", "composeSignals", "signals", "timeout", "length", "filter", "Boolean", "controller", "AbortController", "aborted", "<PERSON>ab<PERSON>", "reason", "unsubscribe", "err", "Error", "abort", "message", "timer", "setTimeout", "ETIMEDOUT", "clearTimeout", "for<PERSON>ach", "signal", "removeEventListener", "addEventListener", "asap"], "sources": ["C:/Users/<USER>/Desktop/file/u3summer/artech/artech/frontend/node_modules/axios/lib/helpers/composeSignals.js"], "sourcesContent": ["import CanceledError from \"../cancel/CanceledError.js\";\nimport AxiosError from \"../core/AxiosError.js\";\nimport utils from '../utils.js';\n\nconst composeSignals = (signals, timeout) => {\n  const {length} = (signals = signals ? signals.filter(Boolean) : []);\n\n  if (timeout || length) {\n    let controller = new AbortController();\n\n    let aborted;\n\n    const onabort = function (reason) {\n      if (!aborted) {\n        aborted = true;\n        unsubscribe();\n        const err = reason instanceof Error ? reason : this.reason;\n        controller.abort(err instanceof AxiosError ? err : new CanceledError(err instanceof Error ? err.message : err));\n      }\n    }\n\n    let timer = timeout && setTimeout(() => {\n      timer = null;\n      onabort(new AxiosError(`timeout ${timeout} of ms exceeded`, AxiosError.ETIMEDOUT))\n    }, timeout)\n\n    const unsubscribe = () => {\n      if (signals) {\n        timer && clearTimeout(timer);\n        timer = null;\n        signals.forEach(signal => {\n          signal.unsubscribe ? signal.unsubscribe(onabort) : signal.removeEventListener('abort', onabort);\n        });\n        signals = null;\n      }\n    }\n\n    signals.forEach((signal) => signal.addEventListener('abort', onabort));\n\n    const {signal} = controller;\n\n    signal.unsubscribe = () => utils.asap(unsubscribe);\n\n    return signal;\n  }\n}\n\nexport default composeSignals;\n"], "mappings": "AAAA,OAAOA,aAAa,MAAM,4BAA4B;AACtD,OAAOC,UAAU,MAAM,uBAAuB;AAC9C,OAAOC,KAAK,MAAM,aAAa;AAE/B,MAAMC,cAAc,GAAGA,CAACC,OAAO,EAAEC,OAAO,KAAK;EAC3C,MAAM;IAACC;EAAM,CAAC,GAAIF,OAAO,GAAGA,OAAO,GAAGA,OAAO,CAACG,MAAM,CAACC,OAAO,CAAC,GAAG,EAAG;EAEnE,IAAIH,OAAO,IAAIC,MAAM,EAAE;IACrB,IAAIG,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;IAEtC,IAAIC,OAAO;IAEX,MAAMC,OAAO,GAAG,SAAAA,CAAUC,MAAM,EAAE;MAChC,IAAI,CAACF,OAAO,EAAE;QACZA,OAAO,GAAG,IAAI;QACdG,WAAW,CAAC,CAAC;QACb,MAAMC,GAAG,GAAGF,MAAM,YAAYG,KAAK,GAAGH,MAAM,GAAG,IAAI,CAACA,MAAM;QAC1DJ,UAAU,CAACQ,KAAK,CAACF,GAAG,YAAYd,UAAU,GAAGc,GAAG,GAAG,IAAIf,aAAa,CAACe,GAAG,YAAYC,KAAK,GAAGD,GAAG,CAACG,OAAO,GAAGH,GAAG,CAAC,CAAC;MACjH;IACF,CAAC;IAED,IAAII,KAAK,GAAGd,OAAO,IAAIe,UAAU,CAAC,MAAM;MACtCD,KAAK,GAAG,IAAI;MACZP,OAAO,CAAC,IAAIX,UAAU,CAAC,WAAWI,OAAO,iBAAiB,EAAEJ,UAAU,CAACoB,SAAS,CAAC,CAAC;IACpF,CAAC,EAAEhB,OAAO,CAAC;IAEX,MAAMS,WAAW,GAAGA,CAAA,KAAM;MACxB,IAAIV,OAAO,EAAE;QACXe,KAAK,IAAIG,YAAY,CAACH,KAAK,CAAC;QAC5BA,KAAK,GAAG,IAAI;QACZf,OAAO,CAACmB,OAAO,CAACC,MAAM,IAAI;UACxBA,MAAM,CAACV,WAAW,GAAGU,MAAM,CAACV,WAAW,CAACF,OAAO,CAAC,GAAGY,MAAM,CAACC,mBAAmB,CAAC,OAAO,EAAEb,OAAO,CAAC;QACjG,CAAC,CAAC;QACFR,OAAO,GAAG,IAAI;MAChB;IACF,CAAC;IAEDA,OAAO,CAACmB,OAAO,CAAEC,MAAM,IAAKA,MAAM,CAACE,gBAAgB,CAAC,OAAO,EAAEd,OAAO,CAAC,CAAC;IAEtE,MAAM;MAACY;IAAM,CAAC,GAAGf,UAAU;IAE3Be,MAAM,CAACV,WAAW,GAAG,MAAMZ,KAAK,CAACyB,IAAI,CAACb,WAAW,CAAC;IAElD,OAAOU,MAAM;EACf;AACF,CAAC;AAED,eAAerB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}