{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nexport * from \"./childrenUtils\";\nexport * from \"./errorThrower\";\nexport * from \"./isConstructor\";\nimport { loadClerkJsScript } from \"./loadClerkJsScript\";\nexport * from \"./useMaxAllowedInstancesGuard\";\nexport * from \"./useCustomElementPortal\";\nexport * from \"./useCustomPages\";\nexport { loadClerkJsScript };", "map": {"version": 3, "names": ["loadClerkJsScript"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\utils\\index.ts"], "sourcesContent": ["export * from './childrenUtils';\nexport * from './errorThrower';\nexport * from './isConstructor';\nexport { loadClerkJsScript } from './loadClerkJsScript';\nexport * from './useMaxAllowedInstancesGuard';\nexport * from './useCustomElementPortal';\nexport * from './useCustomPages';\n"], "mappings": ";AAAA,cAAc;AACd,cAAc;AACd,cAAc;AACd,SAASA,iBAAA,QAAyB;AAClC,cAAc;AACd,cAAc;AACd,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}