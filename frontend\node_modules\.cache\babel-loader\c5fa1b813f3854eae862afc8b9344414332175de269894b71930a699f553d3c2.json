{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport { buildErrorThrower } from \"@clerk/shared/error\";\nconst errorThrower = buildErrorThrower({\n  packageName: \"@clerk/react\"\n});\nfunction __internal__setErrorThrowerOptions(options) {\n  errorThrower.setMessages(options).setPackageName(options);\n}\nexport { __internal__setErrorThrowerOptions, errorThrower };", "map": {"version": 3, "names": ["buildErrorThrower", "errorThrower", "packageName", "__internal__setErrorThrowerOptions", "options", "setMessages", "setPackageName"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\utils\\errorThrower.ts"], "sourcesContent": ["import type { ErrorThrowerOptions } from '@clerk/shared/error';\nimport { buildErrorThrower } from '@clerk/shared/error';\n\nconst errorThrower = buildErrorThrower({ packageName: '@clerk/react' });\n\nfunction __internal__setErrorThrowerOptions(options: ErrorThrowerOptions) {\n  errorThrower.setMessages(options).setPackageName(options);\n}\n\nexport { errorThrower, __internal__setErrorThrowerOptions };\n"], "mappings": ";AACA,SAASA,iBAAA,QAAyB;AAElC,MAAMC,YAAA,GAAeD,iBAAA,CAAkB;EAAEE,WAAA,EAAa;AAAe,CAAC;AAEtE,SAASC,mCAAmCC,OAAA,EAA8B;EACxEH,YAAA,CAAaI,WAAA,CAAYD,OAAO,EAAEE,cAAA,CAAeF,OAAO;AAC1D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}