{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport React, { useState } from \"react\";\nimport { createPortal } from \"react-dom\";\nconst useCustomElementPortal = elements => {\n  const initialState = Array(elements.length).fill(null);\n  const [nodes, setNodes] = useState(initialState);\n  return elements.map((el, index) => ({\n    id: el.id,\n    mount: node => setNodes(prevState => prevState.map((n, i) => i === index ? node : n)),\n    unmount: () => setNodes(prevState => prevState.map((n, i) => i === index ? null : n)),\n    portal: () => /* @__PURE__ */React.createElement(React.Fragment, null, nodes[index] ? createPortal(el.component, nodes[index]) : null)\n  }));\n};\nexport { useCustomElementPortal };", "map": {"version": 3, "names": ["React", "useState", "createPortal", "useCustomElementPortal", "elements", "initialState", "Array", "length", "fill", "nodes", "setNodes", "map", "el", "index", "id", "mount", "node", "prevState", "n", "i", "unmount", "portal", "createElement", "Fragment", "component"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\utils\\useCustomElementPortal.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { createPortal } from 'react-dom';\n\nexport type UseCustomElementPortalParams = {\n  component: React.ReactNode;\n  id: number;\n};\n\nexport type UseCustomElementPortalReturn = {\n  portal: () => JSX.Element;\n  mount: (node: Element) => void;\n  unmount: () => void;\n  id: number;\n};\n\n// This function takes a component as prop, and returns functions that mount and unmount\n// the given component into a given node\nexport const useCustomElementPortal = (elements: UseCustomElementPortalParams[]) => {\n  const initialState = Array(elements.length).fill(null);\n  const [nodes, setNodes] = useState<(Element | null)[]>(initialState);\n\n  return elements.map((el, index) => ({\n    id: el.id,\n    mount: (node: Element) => setNodes(prevState => prevState.map((n, i) => (i === index ? node : n))),\n    unmount: () => setNodes(prevState => prevState.map((n, i) => (i === index ? null : n))),\n    portal: () => <>{nodes[index] ? createPortal(el.component, nodes[index] as Element) : null}</>,\n  }));\n};\n"], "mappings": ";AAAA,OAAOA,KAAA,IAASC,QAAA,QAAgB;AAChC,SAASC,YAAA,QAAoB;AAgBtB,MAAMC,sBAAA,GAA0BC,QAAA,IAA6C;EAClF,MAAMC,YAAA,GAAeC,KAAA,CAAMF,QAAA,CAASG,MAAM,EAAEC,IAAA,CAAK,IAAI;EACrD,MAAM,CAACC,KAAA,EAAOC,QAAQ,IAAIT,QAAA,CAA6BI,YAAY;EAEnE,OAAOD,QAAA,CAASO,GAAA,CAAI,CAACC,EAAA,EAAIC,KAAA,MAAW;IAClCC,EAAA,EAAIF,EAAA,CAAGE,EAAA;IACPC,KAAA,EAAQC,IAAA,IAAkBN,QAAA,CAASO,SAAA,IAAaA,SAAA,CAAUN,GAAA,CAAI,CAACO,CAAA,EAAGC,CAAA,KAAOA,CAAA,KAAMN,KAAA,GAAQG,IAAA,GAAOE,CAAE,CAAC;IACjGE,OAAA,EAASA,CAAA,KAAMV,QAAA,CAASO,SAAA,IAAaA,SAAA,CAAUN,GAAA,CAAI,CAACO,CAAA,EAAGC,CAAA,KAAOA,CAAA,KAAMN,KAAA,GAAQ,OAAOK,CAAE,CAAC;IACtFG,MAAA,EAAQA,CAAA,KAAM,eAAArB,KAAA,CAAAsB,aAAA,CAAAtB,KAAA,CAAAuB,QAAA,QAAGd,KAAA,CAAMI,KAAK,IAAIX,YAAA,CAAaU,EAAA,CAAGY,SAAA,EAAWf,KAAA,CAAMI,KAAK,CAAY,IAAI,IAAK;EAC7F,EAAE;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}