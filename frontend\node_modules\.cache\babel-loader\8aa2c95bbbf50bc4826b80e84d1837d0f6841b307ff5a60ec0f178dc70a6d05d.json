{"ast": null, "code": "import { deprecated } from \"./chunk-IC4FGZI3.mjs\";\n\n// src/error.ts\nfunction isUnauthorizedError(e) {\n  var _a, _b;\n  const status = e == null ? void 0 : e.status;\n  const code = (_b = (_a = e == null ? void 0 : e.errors) == null ? void 0 : _a[0]) == null ? void 0 : _b.code;\n  return code === \"authentication_invalid\" && status === 401;\n}\nfunction isCaptchaError(e) {\n  return [\"captcha_invalid\", \"captcha_not_enabled\", \"captcha_missing_token\"].includes(e.errors[0].code);\n}\nfunction is4xxError(e) {\n  const status = e == null ? void 0 : e.status;\n  return !!status && status >= 400 && status < 500;\n}\nfunction isNetworkError(e) {\n  const message = (`${e.message}${e.name}` || \"\").toLowerCase().replace(/\\s+/g, \"\");\n  return message.includes(\"networkerror\");\n}\nfunction isKnownError(error) {\n  return isClerkAPIResponseError(error) || isMetamaskError(error) || isClerkRuntimeError(error);\n}\nfunction isClerkAPIResponseError(err) {\n  return \"clerkError\" in err;\n}\nfunction isClerkRuntimeError(err) {\n  return \"clerkRuntimeError\" in err;\n}\nfunction isMetamaskError(err) {\n  return \"code\" in err && [4001, 32602, 32603].includes(err.code) && \"message\" in err;\n}\nfunction isUserLockedError(err) {\n  var _a, _b;\n  return isClerkAPIResponseError(err) && ((_b = (_a = err.errors) == null ? void 0 : _a[0]) == null ? void 0 : _b.code) === \"user_locked\";\n}\nfunction parseErrors(data = []) {\n  return data.length > 0 ? data.map(parseError) : [];\n}\nfunction isPasswordPwnedError(err) {\n  var _a, _b;\n  return isClerkAPIResponseError(err) && ((_b = (_a = err.errors) == null ? void 0 : _a[0]) == null ? void 0 : _b.code) === \"form_password_pwned\";\n}\nfunction parseError(error) {\n  var _a, _b, _c, _d, _e;\n  return {\n    code: error.code,\n    message: error.message,\n    longMessage: error.long_message,\n    meta: {\n      paramName: (_a = error == null ? void 0 : error.meta) == null ? void 0 : _a.param_name,\n      sessionId: (_b = error == null ? void 0 : error.meta) == null ? void 0 : _b.session_id,\n      emailAddresses: (_c = error == null ? void 0 : error.meta) == null ? void 0 : _c.email_addresses,\n      identifiers: (_d = error == null ? void 0 : error.meta) == null ? void 0 : _d.identifiers,\n      zxcvbn: (_e = error == null ? void 0 : error.meta) == null ? void 0 : _e.zxcvbn\n    }\n  };\n}\nvar ClerkAPIResponseError = class _ClerkAPIResponseError extends Error {\n  constructor(message, {\n    data,\n    status,\n    clerkTraceId\n  }) {\n    super(message);\n    this.toString = () => {\n      let message = `[${this.name}]\nMessage:${this.message}\nStatus:${this.status}\nSerialized errors: ${this.errors.map(e => JSON.stringify(e))}`;\n      if (this.clerkTraceId) {\n        message += `\nClerk Trace ID: ${this.clerkTraceId}`;\n      }\n      return message;\n    };\n    Object.setPrototypeOf(this, _ClerkAPIResponseError.prototype);\n    this.status = status;\n    this.message = message;\n    this.clerkTraceId = clerkTraceId;\n    this.clerkError = true;\n    this.errors = parseErrors(data);\n  }\n};\nvar ClerkRuntimeError = class _ClerkRuntimeError extends Error {\n  constructor(message, {\n    code\n  }) {\n    super(message);\n    /**\n     * Returns a string representation of the error.\n     *\n     * @returns {string} A formatted string with the error name and message.\n     * @memberof ClerkRuntimeError\n     */\n    this.toString = () => {\n      return `[${this.name}]\nMessage:${this.message}`;\n    };\n    Object.setPrototypeOf(this, _ClerkRuntimeError.prototype);\n    this.code = code;\n    this.message = message;\n    this.clerkRuntimeError = true;\n  }\n};\nvar MagicLinkError = class _MagicLinkError extends Error {\n  constructor(code) {\n    super(code);\n    this.code = code;\n    Object.setPrototypeOf(this, _MagicLinkError.prototype);\n    deprecated(\"MagicLinkError\", \"Use `EmailLinkError` instead.\");\n  }\n};\nvar EmailLinkError = class _EmailLinkError extends Error {\n  constructor(code) {\n    super(code);\n    this.code = code;\n    Object.setPrototypeOf(this, _EmailLinkError.prototype);\n  }\n};\nfunction isMagicLinkError(err) {\n  deprecated(\"isMagicLinkError\", \"Use `isEmailLinkError` instead.\");\n  return err instanceof MagicLinkError;\n}\nfunction isEmailLinkError(err) {\n  return err instanceof EmailLinkError;\n}\nvar _MagicLinkErrorCode = {\n  Expired: \"expired\",\n  Failed: \"failed\"\n};\nvar MagicLinkErrorCode = new Proxy(_MagicLinkErrorCode, {\n  get(target, prop, receiver) {\n    deprecated(\"MagicLinkErrorCode\", \"Use `EmailLinkErrorCode` instead.\");\n    return Reflect.get(target, prop, receiver);\n  }\n});\nvar EmailLinkErrorCode = {\n  Expired: \"expired\",\n  Failed: \"failed\"\n};\nvar DefaultMessages = Object.freeze({\n  InvalidFrontendApiErrorMessage: `The frontendApi passed to Clerk is invalid. You can get your Frontend API key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})`,\n  InvalidProxyUrlErrorMessage: `The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})`,\n  InvalidPublishableKeyErrorMessage: `The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})`,\n  MissingPublishableKeyErrorMessage: `Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`\n});\nfunction buildErrorThrower({\n  packageName,\n  customMessages\n}) {\n  let pkg = packageName;\n  const messages = {\n    ...DefaultMessages,\n    ...customMessages\n  };\n  function buildMessage(rawMessage, replacements) {\n    if (!replacements) {\n      return `${pkg}: ${rawMessage}`;\n    }\n    let msg = rawMessage;\n    const matches = rawMessage.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);\n    for (const match of matches) {\n      const replacement = (replacements[match[1]] || \"\").toString();\n      msg = msg.replace(`{{${match[1]}}}`, replacement);\n    }\n    return `${pkg}: ${msg}`;\n  }\n  return {\n    setPackageName({\n      packageName: packageName2\n    }) {\n      if (typeof packageName2 === \"string\") {\n        pkg = packageName2;\n      }\n      return this;\n    },\n    setMessages({\n      customMessages: customMessages2\n    }) {\n      Object.assign(messages, customMessages2 || {});\n      return this;\n    },\n    throwInvalidPublishableKeyError(params) {\n      throw new Error(buildMessage(messages.InvalidPublishableKeyErrorMessage, params));\n    },\n    throwInvalidFrontendApiError(params) {\n      throw new Error(buildMessage(messages.InvalidFrontendApiErrorMessage, params));\n    },\n    throwInvalidProxyUrl(params) {\n      throw new Error(buildMessage(messages.InvalidProxyUrlErrorMessage, params));\n    },\n    throwMissingPublishableKeyError() {\n      throw new Error(buildMessage(messages.MissingPublishableKeyErrorMessage));\n    }\n  };\n}\nexport { isUnauthorizedError, isCaptchaError, is4xxError, isNetworkError, isKnownError, isClerkAPIResponseError, isClerkRuntimeError, isMetamaskError, isUserLockedError, parseErrors, isPasswordPwnedError, parseError, ClerkAPIResponseError, ClerkRuntimeError, MagicLinkError, EmailLinkError, isMagicLinkError, isEmailLinkError, MagicLinkErrorCode, EmailLinkErrorCode, buildErrorThrower };", "map": {"version": 3, "names": ["isUnauthorizedError", "e", "_a", "_b", "status", "code", "errors", "isCaptchaError", "includes", "is4xxError", "isNetworkError", "message", "name", "toLowerCase", "replace", "isKnownError", "error", "isClerkAPIResponseError", "isMetamaskError", "isClerkRuntimeError", "err", "isUserLockedError", "parseErrors", "data", "length", "map", "parseError", "isPasswordPwnedError", "_c", "_d", "_e", "longMessage", "long_message", "meta", "paramName", "param_name", "sessionId", "session_id", "emailAddresses", "email_addresses", "identifiers", "zxcvbn", "ClerkAPIResponseError", "_ClerkAPIResponseError", "Error", "constructor", "clerk<PERSON>raceId", "toString", "JSON", "stringify", "Object", "setPrototypeOf", "prototype", "clerk<PERSON><PERSON>r", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_Clerk<PERSON><PERSON><PERSON><PERSON><PERSON>r", "clerk<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MagicLinkError", "_MagicLinkError", "deprecated", "EmailLinkError", "_EmailLinkError", "isMagicLinkError", "isEmailLinkError", "_MagicLinkErrorCode", "Expired", "Failed", "MagicLinkErrorCode", "Proxy", "get", "target", "prop", "receiver", "Reflect", "EmailLinkErrorCode", "DefaultMessages", "freeze", "InvalidFrontendApiErrorMessage", "InvalidProxyUrlErrorMessage", "InvalidPublishableKeyErrorMessage", "MissingPublishableKeyErrorMessage", "buildErrorThrower", "packageName", "customMessages", "pkg", "messages", "buildMessage", "rawMessage", "replacements", "msg", "matches", "matchAll", "match", "replacement", "setPackageName", "packageName2", "setMessages", "customMessages2", "assign", "throwInvalidPublishableKeyError", "params", "throwInvalidFrontendApiError", "throwInvalidProxyUrl", "throwMissingPublishableKeyError"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\error.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@clerk/types';\n\nimport { deprecated } from './deprecated';\n\nexport function isUnauthorizedError(e: any): boolean {\n  const status = e?.status;\n  const code = e?.errors?.[0]?.code;\n  return code === 'authentication_invalid' && status === 401;\n}\n\nexport function isCaptchaError(e: ClerkAPIResponseError): boolean {\n  return ['captcha_invalid', 'captcha_not_enabled', 'captcha_missing_token'].includes(e.errors[0].code);\n}\n\nexport function is4xxError(e: any): boolean {\n  const status = e?.status;\n  return !!status && status >= 400 && status < 500;\n}\n\nexport function isNetworkError(e: any): boolean {\n  // TODO: revise during error handling epic\n  const message = (`${e.message}${e.name}` || '').toLowerCase().replace(/\\s+/g, '');\n  return message.includes('networkerror');\n}\n\ninterface ClerkAPIResponseOptions {\n  data: ClerkAPIErrorJSON[];\n  status: number;\n  clerkTraceId?: string;\n}\n\n// For a comprehensive Metamask error list, please see\n// https://docs.metamask.io/guide/ethereum-provider.html#errors\nexport interface MetamaskError extends Error {\n  code: 4001 | 32602 | 32603;\n  message: string;\n  data?: unknown;\n}\n\nexport function isKnownError(error: any) {\n  return isClerkAPIResponseError(error) || isMetamaskError(error) || isClerkRuntimeError(error);\n}\n\nexport function isClerkAPIResponseError(err: any): err is ClerkAPIResponseError {\n  return 'clerkError' in err;\n}\n\n/**\n * Checks if the provided error object is an instance of ClerkRuntimeError.\n *\n * @param {any} err - The error object to check.\n * @returns {boolean} True if the error is a ClerkRuntimeError, false otherwise.\n *\n * @example\n * const error = new ClerkRuntimeError('An error occurred');\n * if (isClerkRuntimeError(error)) {\n *   // Handle ClerkRuntimeError\n *   console.error('ClerkRuntimeError:', error.message);\n * } else {\n *   // Handle other errors\n *   console.error('Other error:', error.message);\n * }\n */\nexport function isClerkRuntimeError(err: any): err is ClerkRuntimeError {\n  return 'clerkRuntimeError' in err;\n}\n\nexport function isMetamaskError(err: any): err is MetamaskError {\n  return 'code' in err && [4001, 32602, 32603].includes(err.code) && 'message' in err;\n}\n\nexport function isUserLockedError(err: any) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === 'user_locked';\n}\n\nexport function parseErrors(data: ClerkAPIErrorJSON[] = []): ClerkAPIError[] {\n  return data.length > 0 ? data.map(parseError) : [];\n}\n\nexport function isPasswordPwnedError(err: any) {\n  return isClerkAPIResponseError(err) && err.errors?.[0]?.code === 'form_password_pwned';\n}\n\nexport function parseError(error: ClerkAPIErrorJSON): ClerkAPIError {\n  return {\n    code: error.code,\n    message: error.message,\n    longMessage: error.long_message,\n    meta: {\n      paramName: error?.meta?.param_name,\n      sessionId: error?.meta?.session_id,\n      emailAddresses: error?.meta?.email_addresses,\n      identifiers: error?.meta?.identifiers,\n      zxcvbn: error?.meta?.zxcvbn,\n    },\n  };\n}\n\nexport class ClerkAPIResponseError extends Error {\n  clerkError: true;\n\n  status: number;\n  message: string;\n  clerkTraceId?: string;\n\n  errors: ClerkAPIError[];\n\n  constructor(message: string, { data, status, clerkTraceId }: ClerkAPIResponseOptions) {\n    super(message);\n\n    Object.setPrototypeOf(this, ClerkAPIResponseError.prototype);\n\n    this.status = status;\n    this.message = message;\n    this.clerkTraceId = clerkTraceId;\n    this.clerkError = true;\n    this.errors = parseErrors(data);\n  }\n\n  public toString = () => {\n    let message = `[${this.name}]\\nMessage:${this.message}\\nStatus:${this.status}\\nSerialized errors: ${this.errors.map(\n      e => JSON.stringify(e),\n    )}`;\n\n    if (this.clerkTraceId) {\n      message += `\\nClerk Trace ID: ${this.clerkTraceId}`;\n    }\n\n    return message;\n  };\n}\n\n/**\n * Custom error class for representing Clerk runtime errors.\n *\n * @class ClerkRuntimeError\n * @example\n *   throw new ClerkRuntimeError('An error occurred', { code: 'password_invalid' });\n */\nexport class ClerkRuntimeError extends Error {\n  clerkRuntimeError: true;\n\n  /**\n   * The error message.\n   *\n   * @type {string}\n   * @memberof ClerkRuntimeError\n   */\n  message: string;\n\n  /**\n   * A unique code identifying the error, used for localization\n   *\n   * @type {string}\n   * @memberof ClerkRuntimeError\n   */\n  code: string;\n\n  constructor(message: string, { code }: { code: string }) {\n    super(message);\n\n    Object.setPrototypeOf(this, ClerkRuntimeError.prototype);\n\n    this.code = code;\n    this.message = message;\n    this.clerkRuntimeError = true;\n  }\n\n  /**\n   * Returns a string representation of the error.\n   *\n   * @returns {string} A formatted string with the error name and message.\n   * @memberof ClerkRuntimeError\n   */\n  public toString = () => {\n    return `[${this.name}]\\nMessage:${this.message}`;\n  };\n}\n\n/**\n * @deprecated Use `EmailLinkError` instead.\n */\nexport class MagicLinkError extends Error {\n  code: string;\n\n  constructor(code: string) {\n    super(code);\n    this.code = code;\n    Object.setPrototypeOf(this, MagicLinkError.prototype);\n    deprecated('MagicLinkError', 'Use `EmailLinkError` instead.');\n  }\n}\n\nexport class EmailLinkError extends Error {\n  code: string;\n\n  constructor(code: string) {\n    super(code);\n    this.code = code;\n    Object.setPrototypeOf(this, EmailLinkError.prototype);\n  }\n}\n\n/**\n * Check if the error is a MagicLinkError.\n * @deprecated Use `isEmailLinkError` instead.\n */\nexport function isMagicLinkError(err: Error): err is MagicLinkError {\n  deprecated('isMagicLinkError', 'Use `isEmailLinkError` instead.');\n  return err instanceof MagicLinkError;\n}\n\nexport function isEmailLinkError(err: Error): err is EmailLinkError {\n  return err instanceof EmailLinkError;\n}\n\nconst _MagicLinkErrorCode = {\n  Expired: 'expired',\n  Failed: 'failed',\n};\n\n/**\n * @deprecated Use `EmailLinkErrorCode` instead.\n */\nexport const MagicLinkErrorCode = new Proxy(_MagicLinkErrorCode, {\n  get(target, prop, receiver) {\n    deprecated('MagicLinkErrorCode', 'Use `EmailLinkErrorCode` instead.');\n    return Reflect.get(target, prop, receiver);\n  },\n});\n\nexport const EmailLinkErrorCode = {\n  Expired: 'expired',\n  Failed: 'failed',\n};\n\nconst DefaultMessages = Object.freeze({\n  InvalidFrontendApiErrorMessage: `The frontendApi passed to Clerk is invalid. You can get your Frontend API key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})`,\n  InvalidProxyUrlErrorMessage: `The proxyUrl passed to Clerk is invalid. The expected value for proxyUrl is an absolute URL or a relative path with a leading '/'. (key={{url}})`,\n  InvalidPublishableKeyErrorMessage: `The publishableKey passed to Clerk is invalid. You can get your Publishable key at https://dashboard.clerk.com/last-active?path=api-keys. (key={{key}})`,\n  MissingPublishableKeyErrorMessage: `Missing publishableKey. You can get your key at https://dashboard.clerk.com/last-active?path=api-keys.`,\n});\n\ntype MessageKeys = keyof typeof DefaultMessages;\n\ntype Messages = Record<MessageKeys, string>;\n\ntype CustomMessages = Partial<Messages>;\n\nexport type ErrorThrowerOptions = {\n  packageName: string;\n  customMessages?: CustomMessages;\n};\n\nexport interface ErrorThrower {\n  setPackageName(options: ErrorThrowerOptions): ErrorThrower;\n\n  setMessages(options: ErrorThrowerOptions): ErrorThrower;\n\n  throwInvalidPublishableKeyError(params: { key?: string }): never;\n\n  throwInvalidFrontendApiError(params: { key?: string }): never;\n\n  throwInvalidProxyUrl(params: { url?: string }): never;\n\n  throwMissingPublishableKeyError(): never;\n}\n\nexport function buildErrorThrower({ packageName, customMessages }: ErrorThrowerOptions): ErrorThrower {\n  let pkg = packageName;\n\n  const messages = {\n    ...DefaultMessages,\n    ...customMessages,\n  };\n\n  function buildMessage(rawMessage: string, replacements?: Record<string, string | number>) {\n    if (!replacements) {\n      return `${pkg}: ${rawMessage}`;\n    }\n\n    let msg = rawMessage;\n    const matches = rawMessage.matchAll(/{{([a-zA-Z0-9-_]+)}}/g);\n\n    for (const match of matches) {\n      const replacement = (replacements[match[1]] || '').toString();\n      msg = msg.replace(`{{${match[1]}}}`, replacement);\n    }\n\n    return `${pkg}: ${msg}`;\n  }\n\n  return {\n    setPackageName({ packageName }: ErrorThrowerOptions): ErrorThrower {\n      if (typeof packageName === 'string') {\n        pkg = packageName;\n      }\n      return this;\n    },\n\n    setMessages({ customMessages }: ErrorThrowerOptions): ErrorThrower {\n      Object.assign(messages, customMessages || {});\n      return this;\n    },\n\n    throwInvalidPublishableKeyError(params: { key?: string }): never {\n      throw new Error(buildMessage(messages.InvalidPublishableKeyErrorMessage, params));\n    },\n\n    throwInvalidFrontendApiError(params: { key?: string }): never {\n      throw new Error(buildMessage(messages.InvalidFrontendApiErrorMessage, params));\n    },\n\n    throwInvalidProxyUrl(params: { url?: string }): never {\n      throw new Error(buildMessage(messages.InvalidProxyUrlErrorMessage, params));\n    },\n\n    throwMissingPublishableKeyError(): never {\n      throw new Error(buildMessage(messages.MissingPublishableKeyErrorMessage));\n    },\n  };\n}\n"], "mappings": ";;;AAIO,SAASA,oBAAoBC,CAAA,EAAiB;EAJrD,IAAAC,EAAA,EAAAC,EAAA;EAKE,MAAMC,MAAA,GAASH,CAAA,oBAAAA,CAAA,CAAGG,MAAA;EAClB,MAAMC,IAAA,IAAOF,EAAA,IAAAD,EAAA,GAAAD,CAAA,oBAAAA,CAAA,CAAGK,MAAA,KAAH,gBAAAJ,EAAA,CAAY,OAAZ,gBAAAC,EAAA,CAAgBE,IAAA;EAC7B,OAAOA,IAAA,KAAS,4BAA4BD,MAAA,KAAW;AACzD;AAEO,SAASG,eAAeN,CAAA,EAAmC;EAChE,OAAO,CAAC,mBAAmB,uBAAuB,uBAAuB,EAAEO,QAAA,CAASP,CAAA,CAAEK,MAAA,CAAO,CAAC,EAAED,IAAI;AACtG;AAEO,SAASI,WAAWR,CAAA,EAAiB;EAC1C,MAAMG,MAAA,GAASH,CAAA,oBAAAA,CAAA,CAAGG,MAAA;EAClB,OAAO,CAAC,CAACA,MAAA,IAAUA,MAAA,IAAU,OAAOA,MAAA,GAAS;AAC/C;AAEO,SAASM,eAAeT,CAAA,EAAiB;EAE9C,MAAMU,OAAA,IAAW,GAAGV,CAAA,CAAEU,OAAO,GAAGV,CAAA,CAAEW,IAAI,MAAM,IAAIC,WAAA,CAAY,EAAEC,OAAA,CAAQ,QAAQ,EAAE;EAChF,OAAOH,OAAA,CAAQH,QAAA,CAAS,cAAc;AACxC;AAgBO,SAASO,aAAaC,KAAA,EAAY;EACvC,OAAOC,uBAAA,CAAwBD,KAAK,KAAKE,eAAA,CAAgBF,KAAK,KAAKG,mBAAA,CAAoBH,KAAK;AAC9F;AAEO,SAASC,wBAAwBG,GAAA,EAAwC;EAC9E,OAAO,gBAAgBA,GAAA;AACzB;AAkBO,SAASD,oBAAoBC,GAAA,EAAoC;EACtE,OAAO,uBAAuBA,GAAA;AAChC;AAEO,SAASF,gBAAgBE,GAAA,EAAgC;EAC9D,OAAO,UAAUA,GAAA,IAAO,CAAC,MAAM,OAAO,KAAK,EAAEZ,QAAA,CAASY,GAAA,CAAIf,IAAI,KAAK,aAAae,GAAA;AAClF;AAEO,SAASC,kBAAkBD,GAAA,EAAU;EAvE5C,IAAAlB,EAAA,EAAAC,EAAA;EAwEE,OAAOc,uBAAA,CAAwBG,GAAG,OAAKjB,EAAA,IAAAD,EAAA,GAAAkB,GAAA,CAAId,MAAA,KAAJ,gBAAAJ,EAAA,CAAa,OAAb,gBAAAC,EAAA,CAAiBE,IAAA,MAAS;AACnE;AAEO,SAASiB,YAAYC,IAAA,GAA4B,EAAC,EAAoB;EAC3E,OAAOA,IAAA,CAAKC,MAAA,GAAS,IAAID,IAAA,CAAKE,GAAA,CAAIC,UAAU,IAAI,EAAC;AACnD;AAEO,SAASC,qBAAqBP,GAAA,EAAU;EA/E/C,IAAAlB,EAAA,EAAAC,EAAA;EAgFE,OAAOc,uBAAA,CAAwBG,GAAG,OAAKjB,EAAA,IAAAD,EAAA,GAAAkB,GAAA,CAAId,MAAA,KAAJ,gBAAAJ,EAAA,CAAa,OAAb,gBAAAC,EAAA,CAAiBE,IAAA,MAAS;AACnE;AAEO,SAASqB,WAAWV,KAAA,EAAyC;EAnFpE,IAAAd,EAAA,EAAAC,EAAA,EAAAyB,EAAA,EAAAC,EAAA,EAAAC,EAAA;EAoFE,OAAO;IACLzB,IAAA,EAAMW,KAAA,CAAMX,IAAA;IACZM,OAAA,EAASK,KAAA,CAAML,OAAA;IACfoB,WAAA,EAAaf,KAAA,CAAMgB,YAAA;IACnBC,IAAA,EAAM;MACJC,SAAA,GAAWhC,EAAA,GAAAc,KAAA,oBAAAA,KAAA,CAAOiB,IAAA,KAAP,gBAAA/B,EAAA,CAAaiC,UAAA;MACxBC,SAAA,GAAWjC,EAAA,GAAAa,KAAA,oBAAAA,KAAA,CAAOiB,IAAA,KAAP,gBAAA9B,EAAA,CAAakC,UAAA;MACxBC,cAAA,GAAgBV,EAAA,GAAAZ,KAAA,oBAAAA,KAAA,CAAOiB,IAAA,KAAP,gBAAAL,EAAA,CAAaW,eAAA;MAC7BC,WAAA,GAAaX,EAAA,GAAAb,KAAA,oBAAAA,KAAA,CAAOiB,IAAA,KAAP,gBAAAJ,EAAA,CAAaW,WAAA;MAC1BC,MAAA,GAAQX,EAAA,GAAAd,KAAA,oBAAAA,KAAA,CAAOiB,IAAA,KAAP,gBAAAH,EAAA,CAAaW;IACvB;EACF;AACF;AAEO,IAAMC,qBAAA,GAAN,MAAMC,sBAAA,SAA8BC,KAAA,CAAM;EAS/CC,YAAYlC,OAAA,EAAiB;IAAEY,IAAA;IAAMnB,MAAA;IAAQ0C;EAAa,GAA4B;IACpF,MAAMnC,OAAO;IAWf,KAAOoC,QAAA,GAAW,MAAM;MACtB,IAAIpC,OAAA,GAAU,IAAI,KAAKC,IAAI;AAAA,UAAc,KAAKD,OAAO;AAAA,SAAY,KAAKP,MAAM;AAAA,qBAAwB,KAAKE,MAAA,CAAOmB,GAAA,CAC9GxB,CAAA,IAAK+C,IAAA,CAAKC,SAAA,CAAUhD,CAAC,CACvB,CAAC;MAED,IAAI,KAAK6C,YAAA,EAAc;QACrBnC,OAAA,IAAW;AAAA,kBAAqB,KAAKmC,YAAY;MACnD;MAEA,OAAOnC,OAAA;IACT;IAnBEuC,MAAA,CAAOC,cAAA,CAAe,MAAMR,sBAAA,CAAsBS,SAAS;IAE3D,KAAKhD,MAAA,GAASA,MAAA;IACd,KAAKO,OAAA,GAAUA,OAAA;IACf,KAAKmC,YAAA,GAAeA,YAAA;IACpB,KAAKO,UAAA,GAAa;IAClB,KAAK/C,MAAA,GAASgB,WAAA,CAAYC,IAAI;EAChC;AAaF;AASO,IAAM+B,iBAAA,GAAN,MAAMC,kBAAA,SAA0BX,KAAA,CAAM;EAmB3CC,YAAYlC,OAAA,EAAiB;IAAEN;EAAK,GAAqB;IACvD,MAAMM,OAAO;IAef;AAAA;AAAA;AAAA;AAAA;AAAA;IAAA,KAAOoC,QAAA,GAAW,MAAM;MACtB,OAAO,IAAI,KAAKnC,IAAI;AAAA,UAAc,KAAKD,OAAO;IAChD;IAfEuC,MAAA,CAAOC,cAAA,CAAe,MAAMI,kBAAA,CAAkBH,SAAS;IAEvD,KAAK/C,IAAA,GAAOA,IAAA;IACZ,KAAKM,OAAA,GAAUA,OAAA;IACf,KAAK6C,iBAAA,GAAoB;EAC3B;AAWF;AAKO,IAAMC,cAAA,GAAN,MAAMC,eAAA,SAAuBd,KAAA,CAAM;EAGxCC,YAAYxC,IAAA,EAAc;IACxB,MAAMA,IAAI;IACV,KAAKA,IAAA,GAAOA,IAAA;IACZ6C,MAAA,CAAOC,cAAA,CAAe,MAAMO,eAAA,CAAeN,SAAS;IACpDO,UAAA,CAAW,kBAAkB,+BAA+B;EAC9D;AACF;AAEO,IAAMC,cAAA,GAAN,MAAMC,eAAA,SAAuBjB,KAAA,CAAM;EAGxCC,YAAYxC,IAAA,EAAc;IACxB,MAAMA,IAAI;IACV,KAAKA,IAAA,GAAOA,IAAA;IACZ6C,MAAA,CAAOC,cAAA,CAAe,MAAMU,eAAA,CAAeT,SAAS;EACtD;AACF;AAMO,SAASU,iBAAiB1C,GAAA,EAAmC;EAClEuC,UAAA,CAAW,oBAAoB,iCAAiC;EAChE,OAAOvC,GAAA,YAAeqC,cAAA;AACxB;AAEO,SAASM,iBAAiB3C,GAAA,EAAmC;EAClE,OAAOA,GAAA,YAAewC,cAAA;AACxB;AAEA,IAAMI,mBAAA,GAAsB;EAC1BC,OAAA,EAAS;EACTC,MAAA,EAAQ;AACV;AAKO,IAAMC,kBAAA,GAAqB,IAAIC,KAAA,CAAMJ,mBAAA,EAAqB;EAC/DK,IAAIC,MAAA,EAAQC,IAAA,EAAMC,QAAA,EAAU;IAC1Bb,UAAA,CAAW,sBAAsB,mCAAmC;IACpE,OAAOc,OAAA,CAAQJ,GAAA,CAAIC,MAAA,EAAQC,IAAA,EAAMC,QAAQ;EAC3C;AACF,CAAC;AAEM,IAAME,kBAAA,GAAqB;EAChCT,OAAA,EAAS;EACTC,MAAA,EAAQ;AACV;AAEA,IAAMS,eAAA,GAAkBzB,MAAA,CAAO0B,MAAA,CAAO;EACpCC,8BAAA,EAAgC;EAChCC,2BAAA,EAA6B;EAC7BC,iCAAA,EAAmC;EACnCC,iCAAA,EAAmC;AACrC,CAAC;AA2BM,SAASC,kBAAkB;EAAEC,WAAA;EAAaC;AAAe,GAAsC;EACpG,IAAIC,GAAA,GAAMF,WAAA;EAEV,MAAMG,QAAA,GAAW;IACf,GAAGV,eAAA;IACH,GAAGQ;EACL;EAEA,SAASG,aAAaC,UAAA,EAAoBC,YAAA,EAAgD;IACxF,IAAI,CAACA,YAAA,EAAc;MACjB,OAAO,GAAGJ,GAAG,KAAKG,UAAU;IAC9B;IAEA,IAAIE,GAAA,GAAMF,UAAA;IACV,MAAMG,OAAA,GAAUH,UAAA,CAAWI,QAAA,CAAS,uBAAuB;IAE3D,WAAWC,KAAA,IAASF,OAAA,EAAS;MAC3B,MAAMG,WAAA,IAAeL,YAAA,CAAaI,KAAA,CAAM,CAAC,CAAC,KAAK,IAAI7C,QAAA,CAAS;MAC5D0C,GAAA,GAAMA,GAAA,CAAI3E,OAAA,CAAQ,KAAK8E,KAAA,CAAM,CAAC,CAAC,MAAMC,WAAW;IAClD;IAEA,OAAO,GAAGT,GAAG,KAAKK,GAAG;EACvB;EAEA,OAAO;IACLK,eAAe;MAAEZ,WAAA,EAAAa;IAAY,GAAsC;MACjE,IAAI,OAAOA,YAAA,KAAgB,UAAU;QACnCX,GAAA,GAAMW,YAAA;MACR;MACA,OAAO;IACT;IAEAC,YAAY;MAAEb,cAAA,EAAAc;IAAe,GAAsC;MACjE/C,MAAA,CAAOgD,MAAA,CAAOb,QAAA,EAAUY,eAAA,IAAkB,CAAC,CAAC;MAC5C,OAAO;IACT;IAEAE,gCAAgCC,MAAA,EAAiC;MAC/D,MAAM,IAAIxD,KAAA,CAAM0C,YAAA,CAAaD,QAAA,CAASN,iCAAA,EAAmCqB,MAAM,CAAC;IAClF;IAEAC,6BAA6BD,MAAA,EAAiC;MAC5D,MAAM,IAAIxD,KAAA,CAAM0C,YAAA,CAAaD,QAAA,CAASR,8BAAA,EAAgCuB,MAAM,CAAC;IAC/E;IAEAE,qBAAqBF,MAAA,EAAiC;MACpD,MAAM,IAAIxD,KAAA,CAAM0C,YAAA,CAAaD,QAAA,CAASP,2BAAA,EAA6BsB,MAAM,CAAC;IAC5E;IAEAG,gCAAA,EAAyC;MACvC,MAAM,IAAI3D,KAAA,CAAM0C,YAAA,CAAaD,QAAA,CAASL,iCAAiC,CAAC;IAC1E;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}