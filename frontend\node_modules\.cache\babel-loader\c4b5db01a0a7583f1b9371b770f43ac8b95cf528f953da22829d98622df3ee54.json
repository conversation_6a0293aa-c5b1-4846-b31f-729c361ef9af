{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport { ClientContext, useClientContext } from \"@clerk/shared/react\";\nexport { ClientContext, useClientContext };", "map": {"version": 3, "names": ["ClientContext", "useClientContext"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\contexts\\ClientContext.tsx"], "sourcesContent": ["export { ClientContext, useClientContext } from '@clerk/shared/react';\n"], "mappings": ";AAAA,SAASA,aAAA,EAAeC,gBAAA,QAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}