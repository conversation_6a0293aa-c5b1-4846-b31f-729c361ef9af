{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nconst versionSelector = clerkJSVersion => {\n  if (clerkJSVersion) {\n    return clerkJSVersion;\n  }\n  const prereleaseTag = getPrereleaseTag(\"4.32.5\");\n  if (prereleaseTag) {\n    if (prereleaseTag === \"snapshot\") {\n      return \"4.73.14\";\n    }\n    return prereleaseTag;\n  }\n  return getMajorVersion(\"4.32.5\");\n};\nconst getPrereleaseTag = packageVersion => {\n  var _a;\n  return (_a = packageVersion.match(/-(.*)\\./)) == null ? void 0 : _a[1];\n};\nconst getMajorVersion = packageVersion => packageVersion.split(\".\")[0];\nexport { versionSelector };", "map": {"version": 3, "names": ["versionSelector", "clerkJ<PERSON><PERSON><PERSON>", "prereleaseTag", "getPrereleaseTag", "getMajorVersion", "packageVersion", "_a", "match", "split"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\utils\\versionSelector.ts"], "sourcesContent": ["/**\n * This version selector is a bit complicated, so here is the flow:\n * 1. Use the clerkJSVersion prop on the provider\n * 2. Use the exact `@clerk/clerk-js` version if it is a `@snapshot` prerelease for `@clerk/clerk-react`\n * 3. Use the prerelease tag of `@clerk/clerk-react`\n * 4. Fallback to the major version of `@clerk/clerk-react`\n * @param clerkJSVersion - The optional clerkJSVersion prop on the provider\n * @returns The npm tag, version or major version to use\n */\nexport const versionSelector = (clerkJSVersion: string | undefined) => {\n  if (clerkJSVersion) {\n    return clerkJSVersion;\n  }\n\n  const prereleaseTag = getPrereleaseTag(PACKAGE_VERSION);\n  if (prereleaseTag) {\n    if (prereleaseTag === 'snapshot') {\n      return JS_PACKAGE_VERSION;\n    }\n\n    return prereleaseTag;\n  }\n\n  return getMajorVersion(PACKAGE_VERSION);\n};\n\n// TODO: Replace these with \"semver\" package\nconst getPrereleaseTag = (packageVersion: string) => packageVersion.match(/-(.*)\\./)?.[1];\nconst getMajorVersion = (packageVersion: string) => packageVersion.split('.')[0];\n"], "mappings": ";AASO,MAAMA,eAAA,GAAmBC,cAAA,IAAuC;EACrE,IAAIA,cAAA,EAAgB;IAClB,OAAOA,cAAA;EACT;EAEA,MAAMC,aAAA,GAAgBC,gBAAA,CAAiB,QAAe;EACtD,IAAID,aAAA,EAAe;IACjB,IAAIA,aAAA,KAAkB,YAAY;MAChC,OAAO;IACT;IAEA,OAAOA,aAAA;EACT;EAEA,OAAOE,eAAA,CAAgB,QAAe;AACxC;AAGA,MAAMD,gBAAA,GAAoBE,cAAA,IAAwB;EA3BlD,IAAAC,EAAA;EA2BqD,QAAAA,EAAA,GAAAD,cAAA,CAAeE,KAAA,CAAM,SAAS,MAA9B,gBAAAD,EAAA,CAAkC;AAAA;AACvF,MAAMF,eAAA,GAAmBC,cAAA,IAA2BA,cAAA,CAAeG,KAAA,CAAM,GAAG,EAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}