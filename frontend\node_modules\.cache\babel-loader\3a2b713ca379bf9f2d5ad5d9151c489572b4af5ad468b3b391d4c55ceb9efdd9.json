{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport { useIsomorphicClerkContext } from \"../contexts/IsomorphicClerkContext\";\nconst useClerk = () => {\n  const isomorphicClerk = useIsomorphicClerkContext();\n  return isomorphicClerk;\n};\nexport { useClerk };", "map": {"version": 3, "names": ["useIsomorphicClerkContext", "useClerk", "isomorphicClerk"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\hooks\\useClerk.ts"], "sourcesContent": ["import type { LoadedClerk } from '@clerk/types';\n\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\n\nexport const useClerk = (): LoadedClerk => {\n  const isomorphicClerk = useIsomorphicClerkContext();\n  // The actual value is an instance of IsomorphicClerk, not Clerk\n  // we expose is as a Clerk instance\n  return isomorphicClerk as unknown as LoadedClerk;\n};\n"], "mappings": ";AAEA,SAASA,yBAAA,QAAiC;AAEnC,MAAMC,QAAA,GAAWA,CAAA,KAAmB;EACzC,MAAMC,eAAA,GAAkBF,yBAAA,CAA0B;EAGlD,OAAOE,eAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}