{"ast": null, "code": "// src/devBrowser.ts\nvar DEV_BROWSER_SSO_JWT_PARAMETER = \"__dev_session\";\nvar DEV_BROWSER_JWT_MARKER = \"__clerk_db_jwt\";\nvar DEV_BROWSER_JWT_MARKER_REGEXP = /__clerk_db_jwt\\[(.*)\\]/;\nfunction setDevBrowserJWTInURL(url, jwt, opts = {\n  hash: true\n}) {\n  const resultURL = new URL(url);\n  const jwtFromHash = extractDevBrowserJWTFromURLHash(resultURL);\n  const jwtFromSearch = extractDevBrowserJWTFromURLSearchParams(resultURL);\n  const jwtToSet = jwtFromHash || jwtFromSearch || jwt;\n  if (jwtToSet) {\n    resultURL.searchParams.append(DEV_BROWSER_SSO_JWT_PARAMETER, jwtToSet);\n    resultURL.searchParams.append(DEV_BROWSER_JWT_MARKER, jwtToSet);\n    if (opts.hash) {\n      resultURL.hash = resultURL.hash + `${DEV_BROWSER_JWT_MARKER}[${jwtToSet}]`;\n    }\n  }\n  return resultURL;\n}\nfunction extractDevBrowserJWTFromHash(hash) {\n  const matches = hash.match(DEV_BROWSER_JWT_MARKER_REGEXP);\n  return matches ? matches[1] : \"\";\n}\nfunction extractDevBrowserJWTFromURLHash(url) {\n  const jwt = extractDevBrowserJWTFromHash(url.hash);\n  url.hash = url.hash.replace(DEV_BROWSER_JWT_MARKER_REGEXP, \"\");\n  if (url.href.endsWith(\"#\")) {\n    url.hash = \"\";\n  }\n  return jwt;\n}\nfunction extractDevBrowserJWTFromURLSearchParams(url) {\n  const jwtFromDevSession = url.searchParams.get(DEV_BROWSER_SSO_JWT_PARAMETER);\n  url.searchParams.delete(DEV_BROWSER_SSO_JWT_PARAMETER);\n  const jwtFromClerkDbJwt = url.searchParams.get(DEV_BROWSER_JWT_MARKER);\n  url.searchParams.delete(DEV_BROWSER_JWT_MARKER);\n  return jwtFromDevSession || jwtFromClerkDbJwt || \"\";\n}\nexport { DEV_BROWSER_SSO_JWT_PARAMETER, DEV_BROWSER_JWT_MARKER, setDevBrowserJWTInURL, extractDevBrowserJWTFromURLHash, extractDevBrowserJWTFromURLSearchParams };", "map": {"version": 3, "names": ["DEV_BROWSER_SSO_JWT_PARAMETER", "DEV_BROWSER_JWT_MARKER", "DEV_BROWSER_JWT_MARKER_REGEXP", "setDevBrowserJWTInURL", "url", "jwt", "opts", "hash", "resultURL", "URL", "jwtFromHash", "extractDevBrowserJWTFromURLHash", "jwtFromSearch", "extractDevBrowserJWTFromURLSearchParams", "jwtToSet", "searchParams", "append", "extractDevBrowserJWTFromHash", "matches", "match", "replace", "href", "endsWith", "jwtFromDevSession", "get", "delete", "jwtFromClerkDbJwt"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\devBrowser.ts"], "sourcesContent": ["export const DEV_BROWSER_SSO_JWT_PARAMETER = '__dev_session';\nexport const DEV_BROWSER_JWT_MARKER = '__clerk_db_jwt';\nconst DEV_BROWSER_JWT_MARKER_REGEXP = /__clerk_db_jwt\\[(.*)\\]/;\n\n// Sets the dev_browser JWT in the hash or the search\nexport function setDevBrowserJWTInURL(url: URL, jwt: string, opts = { hash: true }): URL {\n  const resultURL = new URL(url);\n\n  const jwtFromHash = extractDevBrowserJWTFromURLHash(resultURL);\n  const jwtFromSearch = extractDevBrowserJWTFromURLSearchParams(resultURL);\n  // Existing jwt takes precedence\n  const jwtToSet = jwtFromHash || jwtFromSearch || jwt;\n\n  if (jwtToSet) {\n    resultURL.searchParams.append(DEV_BROWSER_SSO_JWT_PARAMETER, jwtToSet);\n    resultURL.searchParams.append(DEV_BROWSER_JWT_MARKER, jwtToSet);\n    if (opts.hash) {\n      resultURL.hash = resultURL.hash + `${DEV_BROWSER_JWT_MARKER}[${jwtToSet}]`;\n    }\n  }\n\n  return resultURL;\n}\n\nfunction extractDevBrowserJWTFromHash(hash: string): string {\n  const matches = hash.match(DEV_BROWSER_JWT_MARKER_REGEXP);\n  return matches ? matches[1] : '';\n}\n\n/**\n * Extract & strip existing jwt from hash\n * Side effect: Removes dev browser from the url hash\n **/\nexport function extractDevBrowserJWTFromURLHash(url: URL) {\n  const jwt = extractDevBrowserJWTFromHash(url.hash);\n  url.hash = url.hash.replace(DEV_BROWSER_JWT_MARKER_REGEXP, '');\n  if (url.href.endsWith('#')) {\n    url.hash = '';\n  }\n\n  return jwt;\n}\n\n/**\n * Extract & strip existing jwt from search params\n * Side effect: Removes dev browser from the search params\n **/\nexport function extractDevBrowserJWTFromURLSearchParams(url: URL) {\n  const jwtFromDevSession = url.searchParams.get(DEV_BROWSER_SSO_JWT_PARAMETER);\n  url.searchParams.delete(DEV_BROWSER_SSO_JWT_PARAMETER);\n\n  const jwtFromClerkDbJwt = url.searchParams.get(DEV_BROWSER_JWT_MARKER);\n  url.searchParams.delete(DEV_BROWSER_JWT_MARKER);\n\n  return jwtFromDevSession || jwtFromClerkDbJwt || '';\n}\n"], "mappings": ";AAAO,IAAMA,6BAAA,GAAgC;AACtC,IAAMC,sBAAA,GAAyB;AACtC,IAAMC,6BAAA,GAAgC;AAG/B,SAASC,sBAAsBC,GAAA,EAAUC,GAAA,EAAaC,IAAA,GAAO;EAAEC,IAAA,EAAM;AAAK,GAAQ;EACvF,MAAMC,SAAA,GAAY,IAAIC,GAAA,CAAIL,GAAG;EAE7B,MAAMM,WAAA,GAAcC,+BAAA,CAAgCH,SAAS;EAC7D,MAAMI,aAAA,GAAgBC,uCAAA,CAAwCL,SAAS;EAEvE,MAAMM,QAAA,GAAWJ,WAAA,IAAeE,aAAA,IAAiBP,GAAA;EAEjD,IAAIS,QAAA,EAAU;IACZN,SAAA,CAAUO,YAAA,CAAaC,MAAA,CAAOhB,6BAAA,EAA+Bc,QAAQ;IACrEN,SAAA,CAAUO,YAAA,CAAaC,MAAA,CAAOf,sBAAA,EAAwBa,QAAQ;IAC9D,IAAIR,IAAA,CAAKC,IAAA,EAAM;MACbC,SAAA,CAAUD,IAAA,GAAOC,SAAA,CAAUD,IAAA,GAAO,GAAGN,sBAAsB,IAAIa,QAAQ;IACzE;EACF;EAEA,OAAON,SAAA;AACT;AAEA,SAASS,6BAA6BV,IAAA,EAAsB;EAC1D,MAAMW,OAAA,GAAUX,IAAA,CAAKY,KAAA,CAAMjB,6BAA6B;EACxD,OAAOgB,OAAA,GAAUA,OAAA,CAAQ,CAAC,IAAI;AAChC;AAMO,SAASP,gCAAgCP,GAAA,EAAU;EACxD,MAAMC,GAAA,GAAMY,4BAAA,CAA6Bb,GAAA,CAAIG,IAAI;EACjDH,GAAA,CAAIG,IAAA,GAAOH,GAAA,CAAIG,IAAA,CAAKa,OAAA,CAAQlB,6BAAA,EAA+B,EAAE;EAC7D,IAAIE,GAAA,CAAIiB,IAAA,CAAKC,QAAA,CAAS,GAAG,GAAG;IAC1BlB,GAAA,CAAIG,IAAA,GAAO;EACb;EAEA,OAAOF,GAAA;AACT;AAMO,SAASQ,wCAAwCT,GAAA,EAAU;EAChE,MAAMmB,iBAAA,GAAoBnB,GAAA,CAAIW,YAAA,CAAaS,GAAA,CAAIxB,6BAA6B;EAC5EI,GAAA,CAAIW,YAAA,CAAaU,MAAA,CAAOzB,6BAA6B;EAErD,MAAM0B,iBAAA,GAAoBtB,GAAA,CAAIW,YAAA,CAAaS,GAAA,CAAIvB,sBAAsB;EACrEG,GAAA,CAAIW,YAAA,CAAaU,MAAA,CAAOxB,sBAAsB;EAE9C,OAAOsB,iBAAA,IAAqBG,iBAAA,IAAqB;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}