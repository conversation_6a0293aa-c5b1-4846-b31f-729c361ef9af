{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst PencilRuler = createLucideIcon(\"PencilRuler\", [[\"path\", {\n  d: \"m15 5 4 4\",\n  key: \"1mk7zo\"\n}], [\"path\", {\n  d: \"M13 7 8.7 2.7a2.41 2.41 0 0 0-3.4 0L2.7 5.3a2.41 2.41 0 0 0 0 3.4L7 13\",\n  key: \"orapub\"\n}], [\"path\", {\n  d: \"m8 6 2-2\",\n  key: \"115y1s\"\n}], [\"path\", {\n  d: \"m2 22 5.5-1.5L21.17 6.83a2.82 2.82 0 0 0-4-4L3.5 16.5Z\",\n  key: \"hes763\"\n}], [\"path\", {\n  d: \"m18 16 2-2\",\n  key: \"ee94s4\"\n}], [\"path\", {\n  d: \"m17 11 4.3 4.3c.94.94.94 2.46 0 3.4l-2.6 2.6c-.94.94-2.46.94-3.4 0L11 17\",\n  key: \"cfq27r\"\n}]]);\nexport { PencilRuler as default };", "map": {"version": 3, "names": ["PencilRuler", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\lucide-react\\src\\icons\\pencil-ruler.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name PencilRuler\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTUgNSA0IDQiIC8+CiAgPHBhdGggZD0iTTEzIDcgOC43IDIuN2EyLjQxIDIuNDEgMCAwIDAtMy40IDBMMi43IDUuM2EyLjQxIDIuNDEgMCAwIDAgMCAzLjRMNyAxMyIgLz4KICA8cGF0aCBkPSJtOCA2IDItMiIgLz4KICA8cGF0aCBkPSJtMiAyMiA1LjUtMS41TDIxLjE3IDYuODNhMi44MiAyLjgyIDAgMCAwLTQtNEwzLjUgMTYuNVoiIC8+CiAgPHBhdGggZD0ibTE4IDE2IDItMiIgLz4KICA8cGF0aCBkPSJtMTcgMTEgNC4zIDQuM2MuOTQuOTQuOTQgMi40NiAwIDMuNGwtMi42IDIuNmMtLjk0Ljk0LTIuNDYuOTQtMy40IDBMMTEgMTciIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/pencil-ruler\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst PencilRuler = createLucideIcon('PencilRuler', [\n  ['path', { d: 'm15 5 4 4', key: '1mk7zo' }],\n  [\n    'path',\n    { d: 'M13 7 8.7 2.7a2.41 2.41 0 0 0-3.4 0L2.7 5.3a2.41 2.41 0 0 0 0 3.4L7 13', key: 'orapub' },\n  ],\n  ['path', { d: 'm8 6 2-2', key: '115y1s' }],\n  ['path', { d: 'm2 22 5.5-1.5L21.17 6.83a2.82 2.82 0 0 0-4-4L3.5 16.5Z', key: 'hes763' }],\n  ['path', { d: 'm18 16 2-2', key: 'ee94s4' }],\n  [\n    'path',\n    {\n      d: 'm17 11 4.3 4.3c.94.94.94 2.46 0 3.4l-2.6 2.6c-.94.94-2.46.94-3.4 0L11 17',\n      key: 'cfq27r',\n    },\n  ],\n]);\n\nexport default PencilRuler;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,WAAA,GAAcC,gBAAA,CAAiB,aAAe,GAClD,CAAC,MAAQ;EAAEC,CAAA,EAAG,WAAa;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC1C,CACE,QACA;EAAED,CAAA,EAAG,wEAA0E;EAAAC,GAAA,EAAK;AAAS,EAC/F,EACA,CAAC,MAAQ;EAAED,CAAA,EAAG,UAAY;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzC,CAAC,MAAQ;EAAED,CAAA,EAAG,wDAA0D;EAAAC,GAAA,EAAK;AAAA,CAAU,GACvF,CAAC,MAAQ;EAAED,CAAA,EAAG,YAAc;EAAAC,GAAA,EAAK;AAAA,CAAU,GAC3C,CACE,QACA;EACED,CAAG;EACHC,GAAK;AACP,EACF,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}