{"ast": null, "code": "import { inBrowser, isBrowserOnline, isValidBrowser, isValidBrowserOnline, userAgentIsRobot } from \"./chunk-LJ4R7M7R.mjs\";\nimport \"./chunk-NDCDZYN6.mjs\";\nexport { inBrowser, isBrowserOnline, isValidBrowser, isValidBrowserOnline, userAgentIsRobot };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["import {\n  inBrowser,\n  isBrowserOnline,\n  isValidBrowser,\n  isValidBrowserOnline,\n  userAgentIsRobot\n} from \"./chunk-LJ4R7M7R.mjs\";\nimport \"./chunk-NDCDZYN6.mjs\";\nexport {\n  inBrowser,\n  isBrowserOnline,\n  isValidBrowser,\n  isValidBrowserOnline,\n  userAgentIsRobot\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}