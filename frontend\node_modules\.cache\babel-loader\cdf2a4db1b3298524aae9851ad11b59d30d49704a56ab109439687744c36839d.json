{"ast": null, "code": "import { isomorphicAtob } from \"./chunk-TETGTEI2.mjs\";\n\n// src/keys.ts\nvar PUBLISHABLE_KEY_LIVE_PREFIX = \"pk_live_\";\nvar PUBLISHABLE_KEY_TEST_PREFIX = \"pk_test_\";\nvar PUBLISHABLE_FRONTEND_API_DEV_REGEX = /^(([a-z]+)-){2}([0-9]{1,2})\\.clerk\\.accounts([a-z.]*)(dev|com)$/i;\nfunction buildPublishableKey(frontendApi) {\n  const keyPrefix = PUBLISHABLE_FRONTEND_API_DEV_REGEX.test(frontendApi) ? PUBLISHABLE_KEY_TEST_PREFIX : PUBLISHABLE_KEY_LIVE_PREFIX;\n  return `${keyPrefix}${btoa(`${frontendApi}$`)}`;\n}\nfunction parsePublishableKey(key) {\n  key = key || \"\";\n  if (!isPublishableKey(key)) {\n    return null;\n  }\n  const instanceType = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) ? \"production\" : \"development\";\n  let frontendApi = isomorphicAtob(key.split(\"_\")[2]);\n  if (!frontendApi.endsWith(\"$\")) {\n    return null;\n  }\n  frontendApi = frontendApi.slice(0, -1);\n  return {\n    instanceType,\n    frontendApi\n  };\n}\nfunction isPublishableKey(key) {\n  key = key || \"\";\n  const hasValidPrefix = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) || key.startsWith(PUBLISHABLE_KEY_TEST_PREFIX);\n  const hasValidFrontendApiPostfix = isomorphicAtob(key.split(\"_\")[2] || \"\").endsWith(\"$\");\n  return hasValidPrefix && hasValidFrontendApiPostfix;\n}\nfunction isLegacyFrontendApiKey(key) {\n  key = key || \"\";\n  return key.startsWith(\"clerk.\");\n}\nfunction createDevOrStagingUrlCache() {\n  const DEV_OR_STAGING_SUFFIXES = [\".lcl.dev\", \".stg.dev\", \".lclstage.dev\", \".stgstage.dev\", \".dev.lclclerk.com\", \".stg.lclclerk.com\", \".accounts.lclclerk.com\", \"accountsstage.dev\", \"accounts.dev\"];\n  const devOrStagingUrlCache = /* @__PURE__ */new Map();\n  return {\n    isDevOrStagingUrl: url => {\n      if (!url) {\n        return false;\n      }\n      const hostname = typeof url === \"string\" ? url : url.hostname;\n      let res = devOrStagingUrlCache.get(hostname);\n      if (res === void 0) {\n        res = DEV_OR_STAGING_SUFFIXES.some(s => hostname.endsWith(s));\n        devOrStagingUrlCache.set(hostname, res);\n      }\n      return res;\n    }\n  };\n}\nfunction isDevelopmentFromApiKey(apiKey) {\n  return apiKey.startsWith(\"test_\") || apiKey.startsWith(\"sk_test_\");\n}\nfunction isProductionFromApiKey(apiKey) {\n  return apiKey.startsWith(\"live_\") || apiKey.startsWith(\"sk_live_\");\n}\nexport { buildPublishableKey, parsePublishableKey, isPublishableKey, isLegacyFrontendApiKey, createDevOrStagingUrlCache, isDevelopmentFromApiKey, isProductionFromApiKey };", "map": {"version": 3, "names": ["PUBLISHABLE_KEY_LIVE_PREFIX", "PUBLISHABLE_KEY_TEST_PREFIX", "PUBLISHABLE_FRONTEND_API_DEV_REGEX", "buildPublishableKey", "frontendApi", "keyPrefix", "test", "btoa", "parsePublishableKey", "key", "isPublishableKey", "instanceType", "startsWith", "isomorphicAtob", "split", "endsWith", "slice", "hasValidPrefix", "hasValidFrontendApiPostfix", "isLegacyFrontendApiKey", "createDevOrStagingUrlCache", "DEV_OR_STAGING_SUFFIXES", "devOrStagingUrlCache", "Map", "isDevOrStagingUrl", "url", "hostname", "res", "get", "some", "s", "set", "isDevelopmentFromApiKey", "<PERSON><PERSON><PERSON><PERSON>", "isProductionFromApiKey"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\keys.ts"], "sourcesContent": ["import type { PublishableKey } from '@clerk/types';\n\nimport { isomorphicAtob } from './isomorphicAtob';\n\nconst PUBLISHABLE_KEY_LIVE_PREFIX = 'pk_live_';\nconst PUBLISHABLE_KEY_TEST_PREFIX = 'pk_test_';\n\n// This regex matches the publishable like frontend API keys (e.g. foo-bar-13.clerk.accounts.dev)\nconst PUBLISHABLE_FRONTEND_API_DEV_REGEX = /^(([a-z]+)-){2}([0-9]{1,2})\\.clerk\\.accounts([a-z.]*)(dev|com)$/i;\n\nexport function buildPublishableKey(frontendApi: string): string {\n  const keyPrefix = PUBLISHABLE_FRONTEND_API_DEV_REGEX.test(frontendApi)\n    ? PUBLISHABLE_KEY_TEST_PREFIX\n    : PUBLISHABLE_KEY_LIVE_PREFIX;\n  return `${keyPrefix}${btoa(`${frontendApi}$`)}`;\n}\n\nexport function parsePublishableKey(key: string | undefined): PublishableKey | null {\n  key = key || '';\n\n  if (!isPublishableKey(key)) {\n    return null;\n  }\n\n  const instanceType = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) ? 'production' : 'development';\n\n  let frontendApi = isomorphicAtob(key.split('_')[2]);\n\n  if (!frontendApi.endsWith('$')) {\n    return null;\n  }\n\n  frontendApi = frontendApi.slice(0, -1);\n\n  return {\n    instanceType,\n    frontendApi,\n  };\n}\n\nexport function isPublishableKey(key: string) {\n  key = key || '';\n\n  const hasValidPrefix = key.startsWith(PUBLISHABLE_KEY_LIVE_PREFIX) || key.startsWith(PUBLISHABLE_KEY_TEST_PREFIX);\n\n  const hasValidFrontendApiPostfix = isomorphicAtob(key.split('_')[2] || '').endsWith('$');\n\n  return hasValidPrefix && hasValidFrontendApiPostfix;\n}\n\nexport function isLegacyFrontendApiKey(key: string) {\n  key = key || '';\n\n  return key.startsWith('clerk.');\n}\n\nexport function createDevOrStagingUrlCache() {\n  // TODO: Check if we can merge it with `./instance.ts#isStaging()`\n  const DEV_OR_STAGING_SUFFIXES = [\n    '.lcl.dev',\n    '.stg.dev',\n    '.lclstage.dev',\n    '.stgstage.dev',\n    '.dev.lclclerk.com',\n    '.stg.lclclerk.com',\n    '.accounts.lclclerk.com',\n    'accountsstage.dev',\n    'accounts.dev',\n  ];\n\n  const devOrStagingUrlCache = new Map<string, boolean>();\n\n  return {\n    isDevOrStagingUrl: (url: string | URL): boolean => {\n      if (!url) {\n        return false;\n      }\n\n      const hostname = typeof url === 'string' ? url : url.hostname;\n      let res = devOrStagingUrlCache.get(hostname);\n      if (res === undefined) {\n        res = DEV_OR_STAGING_SUFFIXES.some(s => hostname.endsWith(s));\n        devOrStagingUrlCache.set(hostname, res);\n      }\n      return res;\n    },\n  };\n}\n\nexport function isDevelopmentFromApiKey(apiKey: string): boolean {\n  return apiKey.startsWith('test_') || apiKey.startsWith('sk_test_');\n}\n\nexport function isProductionFromApiKey(apiKey: string): boolean {\n  return apiKey.startsWith('live_') || apiKey.startsWith('sk_live_');\n}\n"], "mappings": ";;;AAIA,IAAMA,2BAAA,GAA8B;AACpC,IAAMC,2BAAA,GAA8B;AAGpC,IAAMC,kCAAA,GAAqC;AAEpC,SAASC,oBAAoBC,WAAA,EAA6B;EAC/D,MAAMC,SAAA,GAAYH,kCAAA,CAAmCI,IAAA,CAAKF,WAAW,IACjEH,2BAAA,GACAD,2BAAA;EACJ,OAAO,GAAGK,SAAS,GAAGE,IAAA,CAAK,GAAGH,WAAW,GAAG,CAAC;AAC/C;AAEO,SAASI,oBAAoBC,GAAA,EAAgD;EAClFA,GAAA,GAAMA,GAAA,IAAO;EAEb,IAAI,CAACC,gBAAA,CAAiBD,GAAG,GAAG;IAC1B,OAAO;EACT;EAEA,MAAME,YAAA,GAAeF,GAAA,CAAIG,UAAA,CAAWZ,2BAA2B,IAAI,eAAe;EAElF,IAAII,WAAA,GAAcS,cAAA,CAAeJ,GAAA,CAAIK,KAAA,CAAM,GAAG,EAAE,CAAC,CAAC;EAElD,IAAI,CAACV,WAAA,CAAYW,QAAA,CAAS,GAAG,GAAG;IAC9B,OAAO;EACT;EAEAX,WAAA,GAAcA,WAAA,CAAYY,KAAA,CAAM,GAAG,EAAE;EAErC,OAAO;IACLL,YAAA;IACAP;EACF;AACF;AAEO,SAASM,iBAAiBD,GAAA,EAAa;EAC5CA,GAAA,GAAMA,GAAA,IAAO;EAEb,MAAMQ,cAAA,GAAiBR,GAAA,CAAIG,UAAA,CAAWZ,2BAA2B,KAAKS,GAAA,CAAIG,UAAA,CAAWX,2BAA2B;EAEhH,MAAMiB,0BAAA,GAA6BL,cAAA,CAAeJ,GAAA,CAAIK,KAAA,CAAM,GAAG,EAAE,CAAC,KAAK,EAAE,EAAEC,QAAA,CAAS,GAAG;EAEvF,OAAOE,cAAA,IAAkBC,0BAAA;AAC3B;AAEO,SAASC,uBAAuBV,GAAA,EAAa;EAClDA,GAAA,GAAMA,GAAA,IAAO;EAEb,OAAOA,GAAA,CAAIG,UAAA,CAAW,QAAQ;AAChC;AAEO,SAASQ,2BAAA,EAA6B;EAE3C,MAAMC,uBAAA,GAA0B,CAC9B,YACA,YACA,iBACA,iBACA,qBACA,qBACA,0BACA,qBACA,eACF;EAEA,MAAMC,oBAAA,GAAuB,mBAAIC,GAAA,CAAqB;EAEtD,OAAO;IACLC,iBAAA,EAAoBC,GAAA,IAA+B;MACjD,IAAI,CAACA,GAAA,EAAK;QACR,OAAO;MACT;MAEA,MAAMC,QAAA,GAAW,OAAOD,GAAA,KAAQ,WAAWA,GAAA,GAAMA,GAAA,CAAIC,QAAA;MACrD,IAAIC,GAAA,GAAML,oBAAA,CAAqBM,GAAA,CAAIF,QAAQ;MAC3C,IAAIC,GAAA,KAAQ,QAAW;QACrBA,GAAA,GAAMN,uBAAA,CAAwBQ,IAAA,CAAKC,CAAA,IAAKJ,QAAA,CAASX,QAAA,CAASe,CAAC,CAAC;QAC5DR,oBAAA,CAAqBS,GAAA,CAAIL,QAAA,EAAUC,GAAG;MACxC;MACA,OAAOA,GAAA;IACT;EACF;AACF;AAEO,SAASK,wBAAwBC,MAAA,EAAyB;EAC/D,OAAOA,MAAA,CAAOrB,UAAA,CAAW,OAAO,KAAKqB,MAAA,CAAOrB,UAAA,CAAW,UAAU;AACnE;AAEO,SAASsB,uBAAuBD,MAAA,EAAyB;EAC9D,OAAOA,MAAA,CAAOrB,UAAA,CAAW,OAAO,KAAKqB,MAAA,CAAOrB,UAAA,CAAW,UAAU;AACnE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}