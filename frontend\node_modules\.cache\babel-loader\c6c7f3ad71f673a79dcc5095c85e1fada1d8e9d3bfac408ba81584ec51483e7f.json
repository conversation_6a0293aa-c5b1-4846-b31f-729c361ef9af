{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport React from \"react\";\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from \"../utils\";\nimport { withClerk } from \"./withClerk\";\nconst SignUpButton = withClerk(({\n  clerk,\n  children,\n  ...props\n}) => {\n  const {\n    afterSignInUrl,\n    afterSignUpUrl,\n    redirectUrl,\n    mode,\n    unsafeMetadata,\n    ...rest\n  } = props;\n  children = normalizeWithDefaultValue(children, \"Sign up\");\n  const child = assertSingleChild(children)(\"SignUpButton\");\n  const clickHandler = () => {\n    const opts = {\n      afterSignInUrl,\n      afterSignUpUrl,\n      redirectUrl,\n      unsafeMetadata\n    };\n    if (mode === \"modal\") {\n      return clerk.openSignUp(opts);\n    }\n    return clerk.redirectToSignUp(opts);\n  };\n  const wrappedChildClickHandler = async e => {\n    await safeExecute(child.props.onClick)(e);\n    return clickHandler();\n  };\n  const childProps = {\n    ...rest,\n    onClick: wrappedChildClickHandler\n  };\n  return React.cloneElement(child, childProps);\n}, \"SignUpButton\");\nexport { SignUpButton };", "map": {"version": 3, "names": ["React", "assertSingleChild", "normalizeWithDefaultValue", "safeExecute", "with<PERSON><PERSON><PERSON>", "SignUpButton", "clerk", "children", "props", "afterSignInUrl", "afterSignUpUrl", "redirectUrl", "mode", "unsafeMetadata", "rest", "child", "clickHandler", "opts", "openSignUp", "redirectToSignUp", "wrappedChildClickHandler", "e", "onClick", "childProps", "cloneElement"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\components\\SignUpButton.tsx"], "sourcesContent": ["import React from 'react';\n\nimport type { SignUpButtonProps, WithClerkProp } from '../types';\nimport { assertSingleChild, normalizeWithDefaultValue, safeExecute } from '../utils';\nimport { withClerk } from './withClerk';\n\nexport const SignUpButton = withClerk(({ clerk, children, ...props }: WithClerkProp<SignUpButtonProps>) => {\n  const { afterSignInUrl, afterSignUpUrl, redirectUrl, mode, unsafeMetadata, ...rest } = props;\n\n  children = normalizeWithDefaultValue(children, 'Sign up');\n  const child = assertSingleChild(children)('SignUpButton');\n\n  const clickHandler = () => {\n    const opts = { afterSignInUrl, afterSignUpUrl, redirectUrl, unsafeMetadata };\n\n    if (mode === 'modal') {\n      return clerk.openSignUp(opts);\n    }\n\n    return clerk.redirectToSignUp(opts);\n  };\n\n  const wrappedChildClickHandler: React.MouseEventHandler = async e => {\n    await safeExecute((child as any).props.onClick)(e);\n    return clickHandler();\n  };\n\n  const childProps = { ...rest, onClick: wrappedChildClickHandler };\n  return React.cloneElement(child as React.ReactElement<unknown>, childProps);\n}, 'SignUpButton');\n"], "mappings": ";AAAA,OAAOA,KAAA,MAAW;AAGlB,SAASC,iBAAA,EAAmBC,yBAAA,EAA2BC,WAAA,QAAmB;AAC1E,SAASC,SAAA,QAAiB;AAEnB,MAAMC,YAAA,GAAeD,SAAA,CAAU,CAAC;EAAEE,KAAA;EAAOC,QAAA;EAAU,GAAGC;AAAM,MAAwC;EACzG,MAAM;IAAEC,cAAA;IAAgBC,cAAA;IAAgBC,WAAA;IAAaC,IAAA;IAAMC,cAAA;IAAgB,GAAGC;EAAK,IAAIN,KAAA;EAEvFD,QAAA,GAAWL,yBAAA,CAA0BK,QAAA,EAAU,SAAS;EACxD,MAAMQ,KAAA,GAAQd,iBAAA,CAAkBM,QAAQ,EAAE,cAAc;EAExD,MAAMS,YAAA,GAAeA,CAAA,KAAM;IACzB,MAAMC,IAAA,GAAO;MAAER,cAAA;MAAgBC,cAAA;MAAgBC,WAAA;MAAaE;IAAe;IAE3E,IAAID,IAAA,KAAS,SAAS;MACpB,OAAON,KAAA,CAAMY,UAAA,CAAWD,IAAI;IAC9B;IAEA,OAAOX,KAAA,CAAMa,gBAAA,CAAiBF,IAAI;EACpC;EAEA,MAAMG,wBAAA,GAAoD,MAAMC,CAAA,IAAK;IACnE,MAAMlB,WAAA,CAAaY,KAAA,CAAcP,KAAA,CAAMc,OAAO,EAAED,CAAC;IACjD,OAAOL,YAAA,CAAa;EACtB;EAEA,MAAMO,UAAA,GAAa;IAAE,GAAGT,IAAA;IAAMQ,OAAA,EAASF;EAAyB;EAChE,OAAOpB,KAAA,CAAMwB,YAAA,CAAaT,KAAA,EAAsCQ,UAAU;AAC5E,GAAG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}