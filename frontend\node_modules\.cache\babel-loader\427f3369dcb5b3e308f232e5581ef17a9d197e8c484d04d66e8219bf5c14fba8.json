{"ast": null, "code": "import { addClerkPrefix, getClerkJsMajorVersionOrTag, getScriptUrl, parseSearchParams, stripScheme } from \"./chunk-BX6URPWV.mjs\";\nimport \"./chunk-NDCDZYN6.mjs\";\nexport { addClerkPrefix, getClerkJsMajorVersionOrTag, getScriptUrl, parseSearchParams, stripScheme };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["import {\n  addClerkPrefix,\n  getClerkJsMajorVersionOrTag,\n  getScriptUrl,\n  parseSearchParams,\n  stripScheme\n} from \"./chunk-BX6URPWV.mjs\";\nimport \"./chunk-NDCDZYN6.mjs\";\nexport {\n  addClerkPrefix,\n  getClerkJsMajorVersionOrTag,\n  getScriptUrl,\n  parseSearchParams,\n  stripScheme\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}