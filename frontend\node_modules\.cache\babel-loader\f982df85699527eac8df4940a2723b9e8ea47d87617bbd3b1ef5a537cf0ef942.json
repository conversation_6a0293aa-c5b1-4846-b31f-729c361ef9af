{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport { noClerkProviderError, noGuaranteedLoadedError } from \"../errors\";\nfunction assertWrappedByClerkProvider(contextVal) {\n  if (!contextVal) {\n    throw new Error(noClerkProviderError);\n  }\n}\nfunction assertClerkLoadedGuarantee(guarantee, hookName) {\n  if (!guarantee) {\n    throw new Error(noGuaranteedLoadedError(hookName));\n  }\n}\nexport { assertClerkLoadedGuarantee, assertWrappedByClerkProvider };", "map": {"version": 3, "names": ["noClerkProviderError", "noGuaranteedLoadedError", "assertWrappedByClerkProvider", "contextVal", "Error", "assertClerkLoadedGuarantee", "guarantee", "<PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\contexts\\assertHelpers.ts"], "sourcesContent": ["import { noClerkProviderError, noGuaranteedLoadedError } from '../errors';\n\nexport function assertWrappedByClerkProvider(contextVal: unknown): asserts contextVal {\n  if (!contextVal) {\n    throw new Error(noClerkProviderError);\n  }\n}\n\nexport function assertClerkLoadedGuarantee(guarantee: unknown, hookName: string): asserts guarantee {\n  if (!guarantee) {\n    throw new Error(noGuaranteedLoadedError(hookName));\n  }\n}\n"], "mappings": ";AAAA,SAASA,oBAAA,EAAsBC,uBAAA,QAA+B;AAEvD,SAASC,6BAA6BC,UAAA,EAAyC;EACpF,IAAI,CAACA,UAAA,EAAY;IACf,MAAM,IAAIC,KAAA,CAAMJ,oBAAoB;EACtC;AACF;AAEO,SAASK,2BAA2BC,SAAA,EAAoBC,QAAA,EAAqC;EAClG,IAAI,CAACD,SAAA,EAAW;IACd,MAAM,IAAIF,KAAA,CAAMH,uBAAA,CAAwBM,QAAQ,CAAC;EACnD;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}