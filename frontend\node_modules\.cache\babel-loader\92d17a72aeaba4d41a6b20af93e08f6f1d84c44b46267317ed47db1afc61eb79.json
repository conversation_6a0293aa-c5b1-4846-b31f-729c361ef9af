{"ast": null, "code": "import ReactExports, { useRef, useMemo, useCallback, useDebugValue } from 'react';\nimport { useSyncExternalStore } from 'use-sync-external-store/shim/index.js';\nimport { serialize, OBJECT, SWRConfig as SWRConfig$1, defaultConfig, withArgs, SWRGlobalState, createCacheHelper, isUndefined, getTimestamp, UNDEFINED, isFunction, revalidateEvents, internalMutate, useIsomorphicLayoutEffect, subscribeCallback, IS_SERVER, rAF, IS_REACT_LEGACY, mergeObjects } from 'swr/_internal';\nexport { mutate, preload, useSWRConfig } from 'swr/_internal';\nconst unstable_serialize = key => serialize(key)[0];\n\n/// <reference types=\"react/experimental\" />\nconst use = ReactExports.use || (promise => {\n  if (promise.status === 'pending') {\n    throw promise;\n  } else if (promise.status === 'fulfilled') {\n    return promise.value;\n  } else if (promise.status === 'rejected') {\n    throw promise.reason;\n  } else {\n    promise.status = 'pending';\n    promise.then(v => {\n      promise.status = 'fulfilled';\n      promise.value = v;\n    }, e => {\n      promise.status = 'rejected';\n      promise.reason = e;\n    });\n    throw promise;\n  }\n});\nconst WITH_DEDUPE = {\n  dedupe: true\n};\nconst useSWRHandler = (_key, fetcher, config) => {\n  const {\n    cache,\n    compare,\n    suspense,\n    fallbackData,\n    revalidateOnMount,\n    revalidateIfStale,\n    refreshInterval,\n    refreshWhenHidden,\n    refreshWhenOffline,\n    keepPreviousData\n  } = config;\n  const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n  // `key` is the identifier of the SWR internal state,\n  // `fnArg` is the argument/arguments parsed from the key, which will be passed\n  // to the fetcher.\n  // All of them are derived from `_key`.\n  const [key, fnArg] = serialize(_key);\n  // If it's the initial render of this hook.\n  const initialMountedRef = useRef(false);\n  // If the hook is unmounted already. This will be used to prevent some effects\n  // to be called after unmounting.\n  const unmountedRef = useRef(false);\n  // Refs to keep the key and config.\n  const keyRef = useRef(key);\n  const fetcherRef = useRef(fetcher);\n  const configRef = useRef(config);\n  const getConfig = () => configRef.current;\n  const isActive = () => getConfig().isVisible() && getConfig().isOnline();\n  const [getCache, setCache, subscribeCache, getInitialCache] = createCacheHelper(cache, key);\n  const stateDependencies = useRef({}).current;\n  const fallback = isUndefined(fallbackData) ? config.fallback[key] : fallbackData;\n  const isEqual = (prev, current) => {\n    for (const _ in stateDependencies) {\n      const t = _;\n      if (t === 'data') {\n        if (!compare(prev[t], current[t])) {\n          if (!isUndefined(prev[t])) {\n            return false;\n          }\n          if (!compare(returnedData, current[t])) {\n            return false;\n          }\n        }\n      } else {\n        if (current[t] !== prev[t]) {\n          return false;\n        }\n      }\n    }\n    return true;\n  };\n  const getSnapshot = useMemo(() => {\n    const shouldStartRequest = (() => {\n      if (!key) return false;\n      if (!fetcher) return false;\n      // If `revalidateOnMount` is set, we take the value directly.\n      if (!isUndefined(revalidateOnMount)) return revalidateOnMount;\n      // If it's paused, we skip revalidation.\n      if (getConfig().isPaused()) return false;\n      if (suspense) return false;\n      if (!isUndefined(revalidateIfStale)) return revalidateIfStale;\n      return true;\n    })();\n    // Get the cache and merge it with expected states.\n    const getSelectedCache = state => {\n      // We only select the needed fields from the state.\n      const snapshot = mergeObjects(state);\n      delete snapshot._k;\n      if (!shouldStartRequest) {\n        return snapshot;\n      }\n      return {\n        isValidating: true,\n        isLoading: true,\n        ...snapshot\n      };\n    };\n    const cachedData = getCache();\n    const initialData = getInitialCache();\n    const clientSnapshot = getSelectedCache(cachedData);\n    const serverSnapshot = cachedData === initialData ? clientSnapshot : getSelectedCache(initialData);\n    // To make sure that we are returning the same object reference to avoid\n    // unnecessary re-renders, we keep the previous snapshot and use deep\n    // comparison to check if we need to return a new one.\n    let memorizedSnapshot = clientSnapshot;\n    return [() => {\n      const newSnapshot = getSelectedCache(getCache());\n      const compareResult = isEqual(newSnapshot, memorizedSnapshot);\n      if (compareResult) {\n        // Mentally, we should always return the `memorizedSnapshot` here\n        // as there's no change between the new and old snapshots.\n        // However, since the `isEqual` function only compares selected fields,\n        // the values of the unselected fields might be changed. That's\n        // simply because we didn't track them.\n        // To support the case in https://github.com/vercel/swr/pull/2576,\n        // we need to update these fields in the `memorizedSnapshot` too\n        // with direct mutations to ensure the snapshot is always up-to-date\n        // even for the unselected fields, but only trigger re-renders when\n        // the selected fields are changed.\n        memorizedSnapshot.data = newSnapshot.data;\n        memorizedSnapshot.isLoading = newSnapshot.isLoading;\n        memorizedSnapshot.isValidating = newSnapshot.isValidating;\n        memorizedSnapshot.error = newSnapshot.error;\n        return memorizedSnapshot;\n      } else {\n        memorizedSnapshot = newSnapshot;\n        return newSnapshot;\n      }\n    }, () => serverSnapshot];\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [cache, key]);\n  // Get the current state that SWR should return.\n  const cached = useSyncExternalStore(useCallback(callback => subscribeCache(key, (current, prev) => {\n    if (!isEqual(prev, current)) callback();\n  }),\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [cache, key]), getSnapshot[0], getSnapshot[1]);\n  const isInitialMount = !initialMountedRef.current;\n  const hasRevalidator = EVENT_REVALIDATORS[key] && EVENT_REVALIDATORS[key].length > 0;\n  const cachedData = cached.data;\n  const data = isUndefined(cachedData) ? fallback : cachedData;\n  const error = cached.error;\n  // Use a ref to store previously returned data. Use the initial data as its initial value.\n  const laggyDataRef = useRef(data);\n  const returnedData = keepPreviousData ? isUndefined(cachedData) ? laggyDataRef.current : cachedData : data;\n  // - Suspense mode and there's stale data for the initial render.\n  // - Not suspense mode and there is no fallback data and `revalidateIfStale` is enabled.\n  // - `revalidateIfStale` is enabled but `data` is not defined.\n  const shouldDoInitialRevalidation = (() => {\n    // if a key already has revalidators and also has error, we should not trigger revalidation\n    if (hasRevalidator && !isUndefined(error)) return false;\n    // If `revalidateOnMount` is set, we take the value directly.\n    if (isInitialMount && !isUndefined(revalidateOnMount)) return revalidateOnMount;\n    // If it's paused, we skip revalidation.\n    if (getConfig().isPaused()) return false;\n    // Under suspense mode, it will always fetch on render if there is no\n    // stale data so no need to revalidate immediately mount it again.\n    // If data exists, only revalidate if `revalidateIfStale` is true.\n    if (suspense) return isUndefined(data) ? false : revalidateIfStale;\n    // If there is no stale data, we need to revalidate when mount;\n    // If `revalidateIfStale` is set to true, we will always revalidate.\n    return isUndefined(data) || revalidateIfStale;\n  })();\n  // Resolve the default validating state:\n  // If it's able to validate, and it should revalidate when mount, this will be true.\n  const defaultValidatingState = !!(key && fetcher && isInitialMount && shouldDoInitialRevalidation);\n  const isValidating = isUndefined(cached.isValidating) ? defaultValidatingState : cached.isValidating;\n  const isLoading = isUndefined(cached.isLoading) ? defaultValidatingState : cached.isLoading;\n  // The revalidation function is a carefully crafted wrapper of the original\n  // `fetcher`, to correctly handle the many edge cases.\n  const revalidate = useCallback(async revalidateOpts => {\n    const currentFetcher = fetcherRef.current;\n    if (!key || !currentFetcher || unmountedRef.current || getConfig().isPaused()) {\n      return false;\n    }\n    let newData;\n    let startAt;\n    let loading = true;\n    const opts = revalidateOpts || {};\n    // If there is no ongoing concurrent request, or `dedupe` is not set, a\n    // new request should be initiated.\n    const shouldStartNewRequest = !FETCH[key] || !opts.dedupe;\n    /*\n     For React 17\n     Do unmount check for calls:\n     If key has changed during the revalidation, or the component has been\n     unmounted, old dispatch and old event callbacks should not take any\n     effect\n     For React 18\n    only check if key has changed\n    https://github.com/reactwg/react-18/discussions/82\n    */\n    const callbackSafeguard = () => {\n      if (IS_REACT_LEGACY) {\n        return !unmountedRef.current && key === keyRef.current && initialMountedRef.current;\n      }\n      return key === keyRef.current;\n    };\n    // The final state object when the request finishes.\n    const finalState = {\n      isValidating: false,\n      isLoading: false\n    };\n    const finishRequestAndUpdateState = () => {\n      setCache(finalState);\n    };\n    const cleanupState = () => {\n      // Check if it's still the same request before deleting it.\n      const requestInfo = FETCH[key];\n      if (requestInfo && requestInfo[1] === startAt) {\n        delete FETCH[key];\n      }\n    };\n    // Start fetching. Change the `isValidating` state, update the cache.\n    const initialState = {\n      isValidating: true\n    };\n    // It is in the `isLoading` state, if and only if there is no cached data.\n    // This bypasses fallback data and laggy data.\n    if (isUndefined(getCache().data)) {\n      initialState.isLoading = true;\n    }\n    try {\n      if (shouldStartNewRequest) {\n        setCache(initialState);\n        // If no cache is being rendered currently (it shows a blank page),\n        // we trigger the loading slow event.\n        if (config.loadingTimeout && isUndefined(getCache().data)) {\n          setTimeout(() => {\n            if (loading && callbackSafeguard()) {\n              getConfig().onLoadingSlow(key, config);\n            }\n          }, config.loadingTimeout);\n        }\n        // Start the request and save the timestamp.\n        // Key must be truthy if entering here.\n        FETCH[key] = [currentFetcher(fnArg), getTimestamp()];\n      }\n      [newData, startAt] = FETCH[key];\n      newData = await newData;\n      if (shouldStartNewRequest) {\n        // If the request isn't interrupted, clean it up after the\n        // deduplication interval.\n        setTimeout(cleanupState, config.dedupingInterval);\n      }\n      // If there're other ongoing request(s), started after the current one,\n      // we need to ignore the current one to avoid possible race conditions:\n      //   req1------------------>res1        (current one)\n      //        req2---------------->res2\n      // the request that fired later will always be kept.\n      // The timestamp maybe be `undefined` or a number\n      if (!FETCH[key] || FETCH[key][1] !== startAt) {\n        if (shouldStartNewRequest) {\n          if (callbackSafeguard()) {\n            getConfig().onDiscarded(key);\n          }\n        }\n        return false;\n      }\n      // Clear error.\n      finalState.error = UNDEFINED;\n      // If there're other mutations(s), that overlapped with the current revalidation:\n      // case 1:\n      //   req------------------>res\n      //       mutate------>end\n      // case 2:\n      //         req------------>res\n      //   mutate------>end\n      // case 3:\n      //   req------------------>res\n      //       mutate-------...---------->\n      // we have to ignore the revalidation result (res) because it's no longer fresh.\n      // meanwhile, a new revalidation should be triggered when the mutation ends.\n      const mutationInfo = MUTATION[key];\n      if (!isUndefined(mutationInfo) && (\n      // case 1\n      startAt <= mutationInfo[0] ||\n      // case 2\n      startAt <= mutationInfo[1] ||\n      // case 3\n      mutationInfo[1] === 0)) {\n        finishRequestAndUpdateState();\n        if (shouldStartNewRequest) {\n          if (callbackSafeguard()) {\n            getConfig().onDiscarded(key);\n          }\n        }\n        return false;\n      }\n      // Deep compare with the latest state to avoid extra re-renders.\n      // For local state, compare and assign.\n      const cacheData = getCache().data;\n      // Since the compare fn could be custom fn\n      // cacheData might be different from newData even when compare fn returns True\n      finalState.data = compare(cacheData, newData) ? cacheData : newData;\n      // Trigger the successful callback if it's the original request.\n      if (shouldStartNewRequest) {\n        if (callbackSafeguard()) {\n          getConfig().onSuccess(newData, key, config);\n        }\n      }\n    } catch (err) {\n      cleanupState();\n      const currentConfig = getConfig();\n      const {\n        shouldRetryOnError\n      } = currentConfig;\n      // Not paused, we continue handling the error. Otherwise, discard it.\n      if (!currentConfig.isPaused()) {\n        // Get a new error, don't use deep comparison for errors.\n        finalState.error = err;\n        // Error event and retry logic. Only for the actual request, not\n        // deduped ones.\n        if (shouldStartNewRequest && callbackSafeguard()) {\n          currentConfig.onError(err, key, currentConfig);\n          if (shouldRetryOnError === true || isFunction(shouldRetryOnError) && shouldRetryOnError(err)) {\n            if (isActive()) {\n              // If it's inactive, stop. It will auto-revalidate when\n              // refocusing or reconnecting.\n              // When retrying, deduplication is always enabled.\n              currentConfig.onErrorRetry(err, key, currentConfig, _opts => {\n                const revalidators = EVENT_REVALIDATORS[key];\n                if (revalidators && revalidators[0]) {\n                  revalidators[0](revalidateEvents.ERROR_REVALIDATE_EVENT, _opts);\n                }\n              }, {\n                retryCount: (opts.retryCount || 0) + 1,\n                dedupe: true\n              });\n            }\n          }\n        }\n      }\n    }\n    // Mark loading as stopped.\n    loading = false;\n    // Update the current hook's state.\n    finishRequestAndUpdateState();\n    return true;\n  },\n  // `setState` is immutable, and `eventsCallback`, `fnArg`, and\n  // `keyValidating` are depending on `key`, so we can exclude them from\n  // the deps array.\n  //\n  // FIXME:\n  // `fn` and `config` might be changed during the lifecycle,\n  // but they might be changed every render like this.\n  // `useSWR('key', () => fetch('/api/'), { suspense: true })`\n  // So we omit the values from the deps array\n  // even though it might cause unexpected behaviors.\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  [key, cache]);\n  // Similar to the global mutate but bound to the current cache and key.\n  // `cache` isn't allowed to change during the lifecycle.\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  const boundMutate = useCallback(\n  // Use callback to make sure `keyRef.current` returns latest result every time\n  (...args) => {\n    return internalMutate(cache, keyRef.current, ...args);\n  },\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  []);\n  // The logic for updating refs.\n  useIsomorphicLayoutEffect(() => {\n    fetcherRef.current = fetcher;\n    configRef.current = config;\n    // Handle laggy data updates. If there's cached data of the current key,\n    // it'll be the correct reference.\n    if (!isUndefined(cachedData)) {\n      laggyDataRef.current = cachedData;\n    }\n  });\n  // After mounted or key changed.\n  useIsomorphicLayoutEffect(() => {\n    if (!key) return;\n    const softRevalidate = revalidate.bind(UNDEFINED, WITH_DEDUPE);\n    // Expose revalidators to global event listeners. So we can trigger\n    // revalidation from the outside.\n    let nextFocusRevalidatedAt = 0;\n    const onRevalidate = (type, opts = {}) => {\n      if (type == revalidateEvents.FOCUS_EVENT) {\n        const now = Date.now();\n        if (getConfig().revalidateOnFocus && now > nextFocusRevalidatedAt && isActive()) {\n          nextFocusRevalidatedAt = now + getConfig().focusThrottleInterval;\n          softRevalidate();\n        }\n      } else if (type == revalidateEvents.RECONNECT_EVENT) {\n        if (getConfig().revalidateOnReconnect && isActive()) {\n          softRevalidate();\n        }\n      } else if (type == revalidateEvents.MUTATE_EVENT) {\n        return revalidate();\n      } else if (type == revalidateEvents.ERROR_REVALIDATE_EVENT) {\n        return revalidate(opts);\n      }\n      return;\n    };\n    const unsubEvents = subscribeCallback(key, EVENT_REVALIDATORS, onRevalidate);\n    // Mark the component as mounted and update corresponding refs.\n    unmountedRef.current = false;\n    keyRef.current = key;\n    initialMountedRef.current = true;\n    // Keep the original key in the cache.\n    setCache({\n      _k: fnArg\n    });\n    // Trigger a revalidation\n    if (shouldDoInitialRevalidation) {\n      if (isUndefined(data) || IS_SERVER) {\n        // Revalidate immediately.\n        softRevalidate();\n      } else {\n        // Delay the revalidate if we have data to return so we won't block\n        // rendering.\n        rAF(softRevalidate);\n      }\n    }\n    return () => {\n      // Mark it as unmounted.\n      unmountedRef.current = true;\n      unsubEvents();\n    };\n  }, [key]);\n  // Polling\n  useIsomorphicLayoutEffect(() => {\n    let timer;\n    function next() {\n      // Use the passed interval\n      // ...or invoke the function with the updated data to get the interval\n      const interval = isFunction(refreshInterval) ? refreshInterval(getCache().data) : refreshInterval;\n      // We only start the next interval if `refreshInterval` is not 0, and:\n      // - `force` is true, which is the start of polling\n      // - or `timer` is not 0, which means the effect wasn't canceled\n      if (interval && timer !== -1) {\n        timer = setTimeout(execute, interval);\n      }\n    }\n    function execute() {\n      // Check if it's OK to execute:\n      // Only revalidate when the page is visible, online, and not errored.\n      if (!getCache().error && (refreshWhenHidden || getConfig().isVisible()) && (refreshWhenOffline || getConfig().isOnline())) {\n        revalidate(WITH_DEDUPE).then(next);\n      } else {\n        // Schedule the next interval to check again.\n        next();\n      }\n    }\n    next();\n    return () => {\n      if (timer) {\n        clearTimeout(timer);\n        timer = -1;\n      }\n    };\n  }, [refreshInterval, refreshWhenHidden, refreshWhenOffline, key]);\n  // Display debug info in React DevTools.\n  useDebugValue(returnedData);\n  // In Suspense mode, we can't return the empty `data` state.\n  // If there is an `error`, the `error` needs to be thrown to the error boundary.\n  // If there is no `error`, the `revalidation` promise needs to be thrown to\n  // the suspense boundary.\n  if (suspense && isUndefined(data) && key) {\n    // SWR should throw when trying to use Suspense on the server with React 18,\n    // without providing any initial data. See:\n    // https://github.com/vercel/swr/issues/1832\n    if (!IS_REACT_LEGACY && IS_SERVER) {\n      throw new Error('Fallback data is required when using suspense in SSR.');\n    }\n    // Always update fetcher and config refs even with the Suspense mode.\n    fetcherRef.current = fetcher;\n    configRef.current = config;\n    unmountedRef.current = false;\n    const req = PRELOAD[key];\n    if (!isUndefined(req)) {\n      const promise = boundMutate(req);\n      use(promise);\n    }\n    if (isUndefined(error)) {\n      const promise = revalidate(WITH_DEDUPE);\n      if (!isUndefined(returnedData)) {\n        promise.status = 'fulfilled';\n        promise.value = true;\n      }\n      use(promise);\n    } else {\n      throw error;\n    }\n  }\n  return {\n    mutate: boundMutate,\n    get data() {\n      stateDependencies.data = true;\n      return returnedData;\n    },\n    get error() {\n      stateDependencies.error = true;\n      return error;\n    },\n    get isValidating() {\n      stateDependencies.isValidating = true;\n      return isValidating;\n    },\n    get isLoading() {\n      stateDependencies.isLoading = true;\n      return isLoading;\n    }\n  };\n};\nconst SWRConfig = OBJECT.defineProperty(SWRConfig$1, 'defaultValue', {\n  value: defaultConfig\n});\n/**\n * A hook to fetch data.\n *\n * @link https://swr.vercel.app\n * @example\n * ```jsx\n * import useSWR from 'swr'\n * function Profile() {\n *   const { data, error } = useSWR('/api/user', fetcher)\n *   if (error) return <div>failed to load</div>\n *   if (!data) return <div>loading...</div>\n *   return <div>hello {data.name}!</div>\n * }\n * ```\n */\nconst useSWR = withArgs(useSWRHandler);\n\n// useSWR\n\nexport { SWRConfig, useSWR as default, unstable_serialize };", "map": {"version": 3, "names": ["ReactExports", "useRef", "useMemo", "useCallback", "useDebugValue", "useSyncExternalStore", "serialize", "OBJECT", "SWRConfig", "SWRConfig$1", "defaultConfig", "<PERSON><PERSON><PERSON><PERSON>", "SWRGlobalState", "createCacheHelper", "isUndefined", "getTimestamp", "UNDEFINED", "isFunction", "revalidateEvents", "internalMutate", "useIsomorphicLayoutEffect", "subscribeCallback", "IS_SERVER", "rAF", "IS_REACT_LEGACY", "mergeObjects", "mutate", "preload", "useSWRConfig", "unstable_serialize", "key", "use", "promise", "status", "value", "reason", "then", "v", "e", "WITH_DEDUPE", "dedupe", "useSWRHandler", "_key", "fetcher", "config", "cache", "compare", "suspense", "fallbackD<PERSON>", "revalidateOnMount", "revalidateIfStale", "refreshInterval", "refreshWhenHidden", "refreshWhenOffline", "keepPreviousData", "EVENT_REVALIDATORS", "MUTATION", "FETCH", "PRELOAD", "get", "fnArg", "initialMountedRef", "unmountedRef", "keyRef", "fetcherRef", "configRef", "getConfig", "current", "isActive", "isVisible", "isOnline", "getCache", "setCache", "subscribeCache", "getInitialCache", "stateDependencies", "fallback", "isEqual", "prev", "_", "t", "returnedData", "getSnapshot", "shouldStartRequest", "isPaused", "getSelectedCache", "state", "snapshot", "_k", "isValidating", "isLoading", "cachedData", "initialData", "clientSnapshot", "serverSnapshot", "memorizedSnapshot", "newSnapshot", "compareResult", "data", "error", "cached", "callback", "isInitialMount", "hasRevalidator", "length", "laggyDataRef", "shouldDoInitialRevalidation", "defaultValidatingState", "revalidate", "revalidateOpts", "current<PERSON><PERSON>cher", "newData", "startAt", "loading", "opts", "shouldStartNewRequest", "callback<PERSON><PERSON><PERSON><PERSON>", "finalState", "finishRequestAndUpdateState", "cleanupState", "requestInfo", "initialState", "loadingTimeout", "setTimeout", "onLoadingSlow", "dedupingInterval", "onDiscarded", "mutationInfo", "cacheData", "onSuccess", "err", "currentConfig", "shouldRetryOnError", "onError", "onErrorRetry", "_opts", "revalidators", "ERROR_REVALIDATE_EVENT", "retryCount", "boundMutate", "args", "softRevalidate", "bind", "nextFocusRevalidatedAt", "onRevalidate", "type", "FOCUS_EVENT", "now", "Date", "revalidateOnFocus", "focusThrottleInterval", "RECONNECT_EVENT", "revalidateOnReconnect", "MUTATE_EVENT", "unsubEvents", "timer", "next", "interval", "execute", "clearTimeout", "Error", "req", "defineProperty", "useSWR", "default"], "sources": ["C:/Users/<USER>/Desktop/file/u3summer/artech/artech/frontend/node_modules/swr/core/dist/index.mjs"], "sourcesContent": ["import ReactExports, { useRef, useMemo, useCallback, useDebugValue } from 'react';\nimport { useSyncExternalStore } from 'use-sync-external-store/shim/index.js';\nimport { serialize, OBJECT, SWRConfig as SWRConfig$1, defaultConfig, withArgs, SWRGlobalState, createCacheHelper, isUndefined, getTimestamp, UNDEFINED, isFunction, revalidateEvents, internalMutate, useIsomorphicLayoutEffect, subscribeCallback, IS_SERVER, rAF, IS_REACT_LEGACY, mergeObjects } from 'swr/_internal';\nexport { mutate, preload, useSWRConfig } from 'swr/_internal';\n\nconst unstable_serialize = (key)=>serialize(key)[0];\n\n/// <reference types=\"react/experimental\" />\nconst use = ReactExports.use || ((promise)=>{\n    if (promise.status === 'pending') {\n        throw promise;\n    } else if (promise.status === 'fulfilled') {\n        return promise.value;\n    } else if (promise.status === 'rejected') {\n        throw promise.reason;\n    } else {\n        promise.status = 'pending';\n        promise.then((v)=>{\n            promise.status = 'fulfilled';\n            promise.value = v;\n        }, (e)=>{\n            promise.status = 'rejected';\n            promise.reason = e;\n        });\n        throw promise;\n    }\n});\nconst WITH_DEDUPE = {\n    dedupe: true\n};\nconst useSWRHandler = (_key, fetcher, config)=>{\n    const { cache , compare , suspense , fallbackData , revalidateOnMount , revalidateIfStale , refreshInterval , refreshWhenHidden , refreshWhenOffline , keepPreviousData  } = config;\n    const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n    // `key` is the identifier of the SWR internal state,\n    // `fnArg` is the argument/arguments parsed from the key, which will be passed\n    // to the fetcher.\n    // All of them are derived from `_key`.\n    const [key, fnArg] = serialize(_key);\n    // If it's the initial render of this hook.\n    const initialMountedRef = useRef(false);\n    // If the hook is unmounted already. This will be used to prevent some effects\n    // to be called after unmounting.\n    const unmountedRef = useRef(false);\n    // Refs to keep the key and config.\n    const keyRef = useRef(key);\n    const fetcherRef = useRef(fetcher);\n    const configRef = useRef(config);\n    const getConfig = ()=>configRef.current;\n    const isActive = ()=>getConfig().isVisible() && getConfig().isOnline();\n    const [getCache, setCache, subscribeCache, getInitialCache] = createCacheHelper(cache, key);\n    const stateDependencies = useRef({}).current;\n    const fallback = isUndefined(fallbackData) ? config.fallback[key] : fallbackData;\n    const isEqual = (prev, current)=>{\n        for(const _ in stateDependencies){\n            const t = _;\n            if (t === 'data') {\n                if (!compare(prev[t], current[t])) {\n                    if (!isUndefined(prev[t])) {\n                        return false;\n                    }\n                    if (!compare(returnedData, current[t])) {\n                        return false;\n                    }\n                }\n            } else {\n                if (current[t] !== prev[t]) {\n                    return false;\n                }\n            }\n        }\n        return true;\n    };\n    const getSnapshot = useMemo(()=>{\n        const shouldStartRequest = (()=>{\n            if (!key) return false;\n            if (!fetcher) return false;\n            // If `revalidateOnMount` is set, we take the value directly.\n            if (!isUndefined(revalidateOnMount)) return revalidateOnMount;\n            // If it's paused, we skip revalidation.\n            if (getConfig().isPaused()) return false;\n            if (suspense) return false;\n            if (!isUndefined(revalidateIfStale)) return revalidateIfStale;\n            return true;\n        })();\n        // Get the cache and merge it with expected states.\n        const getSelectedCache = (state)=>{\n            // We only select the needed fields from the state.\n            const snapshot = mergeObjects(state);\n            delete snapshot._k;\n            if (!shouldStartRequest) {\n                return snapshot;\n            }\n            return {\n                isValidating: true,\n                isLoading: true,\n                ...snapshot\n            };\n        };\n        const cachedData = getCache();\n        const initialData = getInitialCache();\n        const clientSnapshot = getSelectedCache(cachedData);\n        const serverSnapshot = cachedData === initialData ? clientSnapshot : getSelectedCache(initialData);\n        // To make sure that we are returning the same object reference to avoid\n        // unnecessary re-renders, we keep the previous snapshot and use deep\n        // comparison to check if we need to return a new one.\n        let memorizedSnapshot = clientSnapshot;\n        return [\n            ()=>{\n                const newSnapshot = getSelectedCache(getCache());\n                const compareResult = isEqual(newSnapshot, memorizedSnapshot);\n                if (compareResult) {\n                    // Mentally, we should always return the `memorizedSnapshot` here\n                    // as there's no change between the new and old snapshots.\n                    // However, since the `isEqual` function only compares selected fields,\n                    // the values of the unselected fields might be changed. That's\n                    // simply because we didn't track them.\n                    // To support the case in https://github.com/vercel/swr/pull/2576,\n                    // we need to update these fields in the `memorizedSnapshot` too\n                    // with direct mutations to ensure the snapshot is always up-to-date\n                    // even for the unselected fields, but only trigger re-renders when\n                    // the selected fields are changed.\n                    memorizedSnapshot.data = newSnapshot.data;\n                    memorizedSnapshot.isLoading = newSnapshot.isLoading;\n                    memorizedSnapshot.isValidating = newSnapshot.isValidating;\n                    memorizedSnapshot.error = newSnapshot.error;\n                    return memorizedSnapshot;\n                } else {\n                    memorizedSnapshot = newSnapshot;\n                    return newSnapshot;\n                }\n            },\n            ()=>serverSnapshot\n        ];\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        cache,\n        key\n    ]);\n    // Get the current state that SWR should return.\n    const cached = useSyncExternalStore(useCallback((callback)=>subscribeCache(key, (current, prev)=>{\n            if (!isEqual(prev, current)) callback();\n        }), // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        cache,\n        key\n    ]), getSnapshot[0], getSnapshot[1]);\n    const isInitialMount = !initialMountedRef.current;\n    const hasRevalidator = EVENT_REVALIDATORS[key] && EVENT_REVALIDATORS[key].length > 0;\n    const cachedData = cached.data;\n    const data = isUndefined(cachedData) ? fallback : cachedData;\n    const error = cached.error;\n    // Use a ref to store previously returned data. Use the initial data as its initial value.\n    const laggyDataRef = useRef(data);\n    const returnedData = keepPreviousData ? isUndefined(cachedData) ? laggyDataRef.current : cachedData : data;\n    // - Suspense mode and there's stale data for the initial render.\n    // - Not suspense mode and there is no fallback data and `revalidateIfStale` is enabled.\n    // - `revalidateIfStale` is enabled but `data` is not defined.\n    const shouldDoInitialRevalidation = (()=>{\n        // if a key already has revalidators and also has error, we should not trigger revalidation\n        if (hasRevalidator && !isUndefined(error)) return false;\n        // If `revalidateOnMount` is set, we take the value directly.\n        if (isInitialMount && !isUndefined(revalidateOnMount)) return revalidateOnMount;\n        // If it's paused, we skip revalidation.\n        if (getConfig().isPaused()) return false;\n        // Under suspense mode, it will always fetch on render if there is no\n        // stale data so no need to revalidate immediately mount it again.\n        // If data exists, only revalidate if `revalidateIfStale` is true.\n        if (suspense) return isUndefined(data) ? false : revalidateIfStale;\n        // If there is no stale data, we need to revalidate when mount;\n        // If `revalidateIfStale` is set to true, we will always revalidate.\n        return isUndefined(data) || revalidateIfStale;\n    })();\n    // Resolve the default validating state:\n    // If it's able to validate, and it should revalidate when mount, this will be true.\n    const defaultValidatingState = !!(key && fetcher && isInitialMount && shouldDoInitialRevalidation);\n    const isValidating = isUndefined(cached.isValidating) ? defaultValidatingState : cached.isValidating;\n    const isLoading = isUndefined(cached.isLoading) ? defaultValidatingState : cached.isLoading;\n    // The revalidation function is a carefully crafted wrapper of the original\n    // `fetcher`, to correctly handle the many edge cases.\n    const revalidate = useCallback(async (revalidateOpts)=>{\n        const currentFetcher = fetcherRef.current;\n        if (!key || !currentFetcher || unmountedRef.current || getConfig().isPaused()) {\n            return false;\n        }\n        let newData;\n        let startAt;\n        let loading = true;\n        const opts = revalidateOpts || {};\n        // If there is no ongoing concurrent request, or `dedupe` is not set, a\n        // new request should be initiated.\n        const shouldStartNewRequest = !FETCH[key] || !opts.dedupe;\n        /*\n         For React 17\n         Do unmount check for calls:\n         If key has changed during the revalidation, or the component has been\n         unmounted, old dispatch and old event callbacks should not take any\n         effect\n\n        For React 18\n        only check if key has changed\n        https://github.com/reactwg/react-18/discussions/82\n      */ const callbackSafeguard = ()=>{\n            if (IS_REACT_LEGACY) {\n                return !unmountedRef.current && key === keyRef.current && initialMountedRef.current;\n            }\n            return key === keyRef.current;\n        };\n        // The final state object when the request finishes.\n        const finalState = {\n            isValidating: false,\n            isLoading: false\n        };\n        const finishRequestAndUpdateState = ()=>{\n            setCache(finalState);\n        };\n        const cleanupState = ()=>{\n            // Check if it's still the same request before deleting it.\n            const requestInfo = FETCH[key];\n            if (requestInfo && requestInfo[1] === startAt) {\n                delete FETCH[key];\n            }\n        };\n        // Start fetching. Change the `isValidating` state, update the cache.\n        const initialState = {\n            isValidating: true\n        };\n        // It is in the `isLoading` state, if and only if there is no cached data.\n        // This bypasses fallback data and laggy data.\n        if (isUndefined(getCache().data)) {\n            initialState.isLoading = true;\n        }\n        try {\n            if (shouldStartNewRequest) {\n                setCache(initialState);\n                // If no cache is being rendered currently (it shows a blank page),\n                // we trigger the loading slow event.\n                if (config.loadingTimeout && isUndefined(getCache().data)) {\n                    setTimeout(()=>{\n                        if (loading && callbackSafeguard()) {\n                            getConfig().onLoadingSlow(key, config);\n                        }\n                    }, config.loadingTimeout);\n                }\n                // Start the request and save the timestamp.\n                // Key must be truthy if entering here.\n                FETCH[key] = [\n                    currentFetcher(fnArg),\n                    getTimestamp()\n                ];\n            }\n            [newData, startAt] = FETCH[key];\n            newData = await newData;\n            if (shouldStartNewRequest) {\n                // If the request isn't interrupted, clean it up after the\n                // deduplication interval.\n                setTimeout(cleanupState, config.dedupingInterval);\n            }\n            // If there're other ongoing request(s), started after the current one,\n            // we need to ignore the current one to avoid possible race conditions:\n            //   req1------------------>res1        (current one)\n            //        req2---------------->res2\n            // the request that fired later will always be kept.\n            // The timestamp maybe be `undefined` or a number\n            if (!FETCH[key] || FETCH[key][1] !== startAt) {\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Clear error.\n            finalState.error = UNDEFINED;\n            // If there're other mutations(s), that overlapped with the current revalidation:\n            // case 1:\n            //   req------------------>res\n            //       mutate------>end\n            // case 2:\n            //         req------------>res\n            //   mutate------>end\n            // case 3:\n            //   req------------------>res\n            //       mutate-------...---------->\n            // we have to ignore the revalidation result (res) because it's no longer fresh.\n            // meanwhile, a new revalidation should be triggered when the mutation ends.\n            const mutationInfo = MUTATION[key];\n            if (!isUndefined(mutationInfo) && // case 1\n            (startAt <= mutationInfo[0] || // case 2\n            startAt <= mutationInfo[1] || // case 3\n            mutationInfo[1] === 0)) {\n                finishRequestAndUpdateState();\n                if (shouldStartNewRequest) {\n                    if (callbackSafeguard()) {\n                        getConfig().onDiscarded(key);\n                    }\n                }\n                return false;\n            }\n            // Deep compare with the latest state to avoid extra re-renders.\n            // For local state, compare and assign.\n            const cacheData = getCache().data;\n            // Since the compare fn could be custom fn\n            // cacheData might be different from newData even when compare fn returns True\n            finalState.data = compare(cacheData, newData) ? cacheData : newData;\n            // Trigger the successful callback if it's the original request.\n            if (shouldStartNewRequest) {\n                if (callbackSafeguard()) {\n                    getConfig().onSuccess(newData, key, config);\n                }\n            }\n        } catch (err) {\n            cleanupState();\n            const currentConfig = getConfig();\n            const { shouldRetryOnError  } = currentConfig;\n            // Not paused, we continue handling the error. Otherwise, discard it.\n            if (!currentConfig.isPaused()) {\n                // Get a new error, don't use deep comparison for errors.\n                finalState.error = err;\n                // Error event and retry logic. Only for the actual request, not\n                // deduped ones.\n                if (shouldStartNewRequest && callbackSafeguard()) {\n                    currentConfig.onError(err, key, currentConfig);\n                    if (shouldRetryOnError === true || isFunction(shouldRetryOnError) && shouldRetryOnError(err)) {\n                        if (isActive()) {\n                            // If it's inactive, stop. It will auto-revalidate when\n                            // refocusing or reconnecting.\n                            // When retrying, deduplication is always enabled.\n                            currentConfig.onErrorRetry(err, key, currentConfig, (_opts)=>{\n                                const revalidators = EVENT_REVALIDATORS[key];\n                                if (revalidators && revalidators[0]) {\n                                    revalidators[0](revalidateEvents.ERROR_REVALIDATE_EVENT, _opts);\n                                }\n                            }, {\n                                retryCount: (opts.retryCount || 0) + 1,\n                                dedupe: true\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        // Mark loading as stopped.\n        loading = false;\n        // Update the current hook's state.\n        finishRequestAndUpdateState();\n        return true;\n    }, // `setState` is immutable, and `eventsCallback`, `fnArg`, and\n    // `keyValidating` are depending on `key`, so we can exclude them from\n    // the deps array.\n    //\n    // FIXME:\n    // `fn` and `config` might be changed during the lifecycle,\n    // but they might be changed every render like this.\n    // `useSWR('key', () => fetch('/api/'), { suspense: true })`\n    // So we omit the values from the deps array\n    // even though it might cause unexpected behaviors.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        key,\n        cache\n    ]);\n    // Similar to the global mutate but bound to the current cache and key.\n    // `cache` isn't allowed to change during the lifecycle.\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const boundMutate = useCallback(// Use callback to make sure `keyRef.current` returns latest result every time\n    (...args)=>{\n        return internalMutate(cache, keyRef.current, ...args);\n    }, // eslint-disable-next-line react-hooks/exhaustive-deps\n    []);\n    // The logic for updating refs.\n    useIsomorphicLayoutEffect(()=>{\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        // Handle laggy data updates. If there's cached data of the current key,\n        // it'll be the correct reference.\n        if (!isUndefined(cachedData)) {\n            laggyDataRef.current = cachedData;\n        }\n    });\n    // After mounted or key changed.\n    useIsomorphicLayoutEffect(()=>{\n        if (!key) return;\n        const softRevalidate = revalidate.bind(UNDEFINED, WITH_DEDUPE);\n        // Expose revalidators to global event listeners. So we can trigger\n        // revalidation from the outside.\n        let nextFocusRevalidatedAt = 0;\n        const onRevalidate = (type, opts = {})=>{\n            if (type == revalidateEvents.FOCUS_EVENT) {\n                const now = Date.now();\n                if (getConfig().revalidateOnFocus && now > nextFocusRevalidatedAt && isActive()) {\n                    nextFocusRevalidatedAt = now + getConfig().focusThrottleInterval;\n                    softRevalidate();\n                }\n            } else if (type == revalidateEvents.RECONNECT_EVENT) {\n                if (getConfig().revalidateOnReconnect && isActive()) {\n                    softRevalidate();\n                }\n            } else if (type == revalidateEvents.MUTATE_EVENT) {\n                return revalidate();\n            } else if (type == revalidateEvents.ERROR_REVALIDATE_EVENT) {\n                return revalidate(opts);\n            }\n            return;\n        };\n        const unsubEvents = subscribeCallback(key, EVENT_REVALIDATORS, onRevalidate);\n        // Mark the component as mounted and update corresponding refs.\n        unmountedRef.current = false;\n        keyRef.current = key;\n        initialMountedRef.current = true;\n        // Keep the original key in the cache.\n        setCache({\n            _k: fnArg\n        });\n        // Trigger a revalidation\n        if (shouldDoInitialRevalidation) {\n            if (isUndefined(data) || IS_SERVER) {\n                // Revalidate immediately.\n                softRevalidate();\n            } else {\n                // Delay the revalidate if we have data to return so we won't block\n                // rendering.\n                rAF(softRevalidate);\n            }\n        }\n        return ()=>{\n            // Mark it as unmounted.\n            unmountedRef.current = true;\n            unsubEvents();\n        };\n    }, [\n        key\n    ]);\n    // Polling\n    useIsomorphicLayoutEffect(()=>{\n        let timer;\n        function next() {\n            // Use the passed interval\n            // ...or invoke the function with the updated data to get the interval\n            const interval = isFunction(refreshInterval) ? refreshInterval(getCache().data) : refreshInterval;\n            // We only start the next interval if `refreshInterval` is not 0, and:\n            // - `force` is true, which is the start of polling\n            // - or `timer` is not 0, which means the effect wasn't canceled\n            if (interval && timer !== -1) {\n                timer = setTimeout(execute, interval);\n            }\n        }\n        function execute() {\n            // Check if it's OK to execute:\n            // Only revalidate when the page is visible, online, and not errored.\n            if (!getCache().error && (refreshWhenHidden || getConfig().isVisible()) && (refreshWhenOffline || getConfig().isOnline())) {\n                revalidate(WITH_DEDUPE).then(next);\n            } else {\n                // Schedule the next interval to check again.\n                next();\n            }\n        }\n        next();\n        return ()=>{\n            if (timer) {\n                clearTimeout(timer);\n                timer = -1;\n            }\n        };\n    }, [\n        refreshInterval,\n        refreshWhenHidden,\n        refreshWhenOffline,\n        key\n    ]);\n    // Display debug info in React DevTools.\n    useDebugValue(returnedData);\n    // In Suspense mode, we can't return the empty `data` state.\n    // If there is an `error`, the `error` needs to be thrown to the error boundary.\n    // If there is no `error`, the `revalidation` promise needs to be thrown to\n    // the suspense boundary.\n    if (suspense && isUndefined(data) && key) {\n        // SWR should throw when trying to use Suspense on the server with React 18,\n        // without providing any initial data. See:\n        // https://github.com/vercel/swr/issues/1832\n        if (!IS_REACT_LEGACY && IS_SERVER) {\n            throw new Error('Fallback data is required when using suspense in SSR.');\n        }\n        // Always update fetcher and config refs even with the Suspense mode.\n        fetcherRef.current = fetcher;\n        configRef.current = config;\n        unmountedRef.current = false;\n        const req = PRELOAD[key];\n        if (!isUndefined(req)) {\n            const promise = boundMutate(req);\n            use(promise);\n        }\n        if (isUndefined(error)) {\n            const promise = revalidate(WITH_DEDUPE);\n            if (!isUndefined(returnedData)) {\n                promise.status = 'fulfilled';\n                promise.value = true;\n            }\n            use(promise);\n        } else {\n            throw error;\n        }\n    }\n    return {\n        mutate: boundMutate,\n        get data () {\n            stateDependencies.data = true;\n            return returnedData;\n        },\n        get error () {\n            stateDependencies.error = true;\n            return error;\n        },\n        get isValidating () {\n            stateDependencies.isValidating = true;\n            return isValidating;\n        },\n        get isLoading () {\n            stateDependencies.isLoading = true;\n            return isLoading;\n        }\n    };\n};\nconst SWRConfig = OBJECT.defineProperty(SWRConfig$1, 'defaultValue', {\n    value: defaultConfig\n});\n/**\n * A hook to fetch data.\n *\n * @link https://swr.vercel.app\n * @example\n * ```jsx\n * import useSWR from 'swr'\n * function Profile() {\n *   const { data, error } = useSWR('/api/user', fetcher)\n *   if (error) return <div>failed to load</div>\n *   if (!data) return <div>loading...</div>\n *   return <div>hello {data.name}!</div>\n * }\n * ```\n */ const useSWR = withArgs(useSWRHandler);\n\n// useSWR\n\nexport { SWRConfig, useSWR as default, unstable_serialize };\n"], "mappings": "AAAA,OAAOA,YAAY,IAAIC,MAAM,EAAEC,OAAO,EAAEC,WAAW,EAAEC,aAAa,QAAQ,OAAO;AACjF,SAASC,oBAAoB,QAAQ,uCAAuC;AAC5E,SAASC,SAAS,EAAEC,MAAM,EAAEC,SAAS,IAAIC,WAAW,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,WAAW,EAAEC,YAAY,EAAEC,SAAS,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,cAAc,EAAEC,yBAAyB,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,GAAG,EAAEC,eAAe,EAAEC,YAAY,QAAQ,eAAe;AACxT,SAASC,MAAM,EAAEC,OAAO,EAAEC,YAAY,QAAQ,eAAe;AAE7D,MAAMC,kBAAkB,GAAIC,GAAG,IAAGxB,SAAS,CAACwB,GAAG,CAAC,CAAC,CAAC,CAAC;;AAEnD;AACA,MAAMC,GAAG,GAAG/B,YAAY,CAAC+B,GAAG,KAAMC,OAAO,IAAG;EACxC,IAAIA,OAAO,CAACC,MAAM,KAAK,SAAS,EAAE;IAC9B,MAAMD,OAAO;EACjB,CAAC,MAAM,IAAIA,OAAO,CAACC,MAAM,KAAK,WAAW,EAAE;IACvC,OAAOD,OAAO,CAACE,KAAK;EACxB,CAAC,MAAM,IAAIF,OAAO,CAACC,MAAM,KAAK,UAAU,EAAE;IACtC,MAAMD,OAAO,CAACG,MAAM;EACxB,CAAC,MAAM;IACHH,OAAO,CAACC,MAAM,GAAG,SAAS;IAC1BD,OAAO,CAACI,IAAI,CAAEC,CAAC,IAAG;MACdL,OAAO,CAACC,MAAM,GAAG,WAAW;MAC5BD,OAAO,CAACE,KAAK,GAAGG,CAAC;IACrB,CAAC,EAAGC,CAAC,IAAG;MACJN,OAAO,CAACC,MAAM,GAAG,UAAU;MAC3BD,OAAO,CAACG,MAAM,GAAGG,CAAC;IACtB,CAAC,CAAC;IACF,MAAMN,OAAO;EACjB;AACJ,CAAC,CAAC;AACF,MAAMO,WAAW,GAAG;EAChBC,MAAM,EAAE;AACZ,CAAC;AACD,MAAMC,aAAa,GAAGA,CAACC,IAAI,EAAEC,OAAO,EAAEC,MAAM,KAAG;EAC3C,MAAM;IAAEC,KAAK;IAAGC,OAAO;IAAGC,QAAQ;IAAGC,YAAY;IAAGC,iBAAiB;IAAGC,iBAAiB;IAAGC,eAAe;IAAGC,iBAAiB;IAAGC,kBAAkB;IAAGC;EAAkB,CAAC,GAAGV,MAAM;EACnL,MAAM,CAACW,kBAAkB,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,CAAC,GAAG9C,cAAc,CAAC+C,GAAG,CAACd,KAAK,CAAC;EAChF;EACA;EACA;EACA;EACA,MAAM,CAACf,GAAG,EAAE8B,KAAK,CAAC,GAAGtD,SAAS,CAACoC,IAAI,CAAC;EACpC;EACA,MAAMmB,iBAAiB,GAAG5D,MAAM,CAAC,KAAK,CAAC;EACvC;EACA;EACA,MAAM6D,YAAY,GAAG7D,MAAM,CAAC,KAAK,CAAC;EAClC;EACA,MAAM8D,MAAM,GAAG9D,MAAM,CAAC6B,GAAG,CAAC;EAC1B,MAAMkC,UAAU,GAAG/D,MAAM,CAAC0C,OAAO,CAAC;EAClC,MAAMsB,SAAS,GAAGhE,MAAM,CAAC2C,MAAM,CAAC;EAChC,MAAMsB,SAAS,GAAGA,CAAA,KAAID,SAAS,CAACE,OAAO;EACvC,MAAMC,QAAQ,GAAGA,CAAA,KAAIF,SAAS,CAAC,CAAC,CAACG,SAAS,CAAC,CAAC,IAAIH,SAAS,CAAC,CAAC,CAACI,QAAQ,CAAC,CAAC;EACtE,MAAM,CAACC,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,eAAe,CAAC,GAAG7D,iBAAiB,CAACgC,KAAK,EAAEf,GAAG,CAAC;EAC3F,MAAM6C,iBAAiB,GAAG1E,MAAM,CAAC,CAAC,CAAC,CAAC,CAACkE,OAAO;EAC5C,MAAMS,QAAQ,GAAG9D,WAAW,CAACkC,YAAY,CAAC,GAAGJ,MAAM,CAACgC,QAAQ,CAAC9C,GAAG,CAAC,GAAGkB,YAAY;EAChF,MAAM6B,OAAO,GAAGA,CAACC,IAAI,EAAEX,OAAO,KAAG;IAC7B,KAAI,MAAMY,CAAC,IAAIJ,iBAAiB,EAAC;MAC7B,MAAMK,CAAC,GAAGD,CAAC;MACX,IAAIC,CAAC,KAAK,MAAM,EAAE;QACd,IAAI,CAAClC,OAAO,CAACgC,IAAI,CAACE,CAAC,CAAC,EAAEb,OAAO,CAACa,CAAC,CAAC,CAAC,EAAE;UAC/B,IAAI,CAAClE,WAAW,CAACgE,IAAI,CAACE,CAAC,CAAC,CAAC,EAAE;YACvB,OAAO,KAAK;UAChB;UACA,IAAI,CAAClC,OAAO,CAACmC,YAAY,EAAEd,OAAO,CAACa,CAAC,CAAC,CAAC,EAAE;YACpC,OAAO,KAAK;UAChB;QACJ;MACJ,CAAC,MAAM;QACH,IAAIb,OAAO,CAACa,CAAC,CAAC,KAAKF,IAAI,CAACE,CAAC,CAAC,EAAE;UACxB,OAAO,KAAK;QAChB;MACJ;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EACD,MAAME,WAAW,GAAGhF,OAAO,CAAC,MAAI;IAC5B,MAAMiF,kBAAkB,GAAG,CAAC,MAAI;MAC5B,IAAI,CAACrD,GAAG,EAAE,OAAO,KAAK;MACtB,IAAI,CAACa,OAAO,EAAE,OAAO,KAAK;MAC1B;MACA,IAAI,CAAC7B,WAAW,CAACmC,iBAAiB,CAAC,EAAE,OAAOA,iBAAiB;MAC7D;MACA,IAAIiB,SAAS,CAAC,CAAC,CAACkB,QAAQ,CAAC,CAAC,EAAE,OAAO,KAAK;MACxC,IAAIrC,QAAQ,EAAE,OAAO,KAAK;MAC1B,IAAI,CAACjC,WAAW,CAACoC,iBAAiB,CAAC,EAAE,OAAOA,iBAAiB;MAC7D,OAAO,IAAI;IACf,CAAC,EAAE,CAAC;IACJ;IACA,MAAMmC,gBAAgB,GAAIC,KAAK,IAAG;MAC9B;MACA,MAAMC,QAAQ,GAAG9D,YAAY,CAAC6D,KAAK,CAAC;MACpC,OAAOC,QAAQ,CAACC,EAAE;MAClB,IAAI,CAACL,kBAAkB,EAAE;QACrB,OAAOI,QAAQ;MACnB;MACA,OAAO;QACHE,YAAY,EAAE,IAAI;QAClBC,SAAS,EAAE,IAAI;QACf,GAAGH;MACP,CAAC;IACL,CAAC;IACD,MAAMI,UAAU,GAAGpB,QAAQ,CAAC,CAAC;IAC7B,MAAMqB,WAAW,GAAGlB,eAAe,CAAC,CAAC;IACrC,MAAMmB,cAAc,GAAGR,gBAAgB,CAACM,UAAU,CAAC;IACnD,MAAMG,cAAc,GAAGH,UAAU,KAAKC,WAAW,GAAGC,cAAc,GAAGR,gBAAgB,CAACO,WAAW,CAAC;IAClG;IACA;IACA;IACA,IAAIG,iBAAiB,GAAGF,cAAc;IACtC,OAAO,CACH,MAAI;MACA,MAAMG,WAAW,GAAGX,gBAAgB,CAACd,QAAQ,CAAC,CAAC,CAAC;MAChD,MAAM0B,aAAa,GAAGpB,OAAO,CAACmB,WAAW,EAAED,iBAAiB,CAAC;MAC7D,IAAIE,aAAa,EAAE;QACf;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAF,iBAAiB,CAACG,IAAI,GAAGF,WAAW,CAACE,IAAI;QACzCH,iBAAiB,CAACL,SAAS,GAAGM,WAAW,CAACN,SAAS;QACnDK,iBAAiB,CAACN,YAAY,GAAGO,WAAW,CAACP,YAAY;QACzDM,iBAAiB,CAACI,KAAK,GAAGH,WAAW,CAACG,KAAK;QAC3C,OAAOJ,iBAAiB;MAC5B,CAAC,MAAM;QACHA,iBAAiB,GAAGC,WAAW;QAC/B,OAAOA,WAAW;MACtB;IACJ,CAAC,EACD,MAAIF,cAAc,CACrB;IACL;EACA,CAAC,EAAE,CACCjD,KAAK,EACLf,GAAG,CACN,CAAC;EACF;EACA,MAAMsE,MAAM,GAAG/F,oBAAoB,CAACF,WAAW,CAAEkG,QAAQ,IAAG5B,cAAc,CAAC3C,GAAG,EAAE,CAACqC,OAAO,EAAEW,IAAI,KAAG;IACzF,IAAI,CAACD,OAAO,CAACC,IAAI,EAAEX,OAAO,CAAC,EAAEkC,QAAQ,CAAC,CAAC;EAC3C,CAAC,CAAC;EAAE;EACR,CACIxD,KAAK,EACLf,GAAG,CACN,CAAC,EAAEoD,WAAW,CAAC,CAAC,CAAC,EAAEA,WAAW,CAAC,CAAC,CAAC,CAAC;EACnC,MAAMoB,cAAc,GAAG,CAACzC,iBAAiB,CAACM,OAAO;EACjD,MAAMoC,cAAc,GAAGhD,kBAAkB,CAACzB,GAAG,CAAC,IAAIyB,kBAAkB,CAACzB,GAAG,CAAC,CAAC0E,MAAM,GAAG,CAAC;EACpF,MAAMb,UAAU,GAAGS,MAAM,CAACF,IAAI;EAC9B,MAAMA,IAAI,GAAGpF,WAAW,CAAC6E,UAAU,CAAC,GAAGf,QAAQ,GAAGe,UAAU;EAC5D,MAAMQ,KAAK,GAAGC,MAAM,CAACD,KAAK;EAC1B;EACA,MAAMM,YAAY,GAAGxG,MAAM,CAACiG,IAAI,CAAC;EACjC,MAAMjB,YAAY,GAAG3B,gBAAgB,GAAGxC,WAAW,CAAC6E,UAAU,CAAC,GAAGc,YAAY,CAACtC,OAAO,GAAGwB,UAAU,GAAGO,IAAI;EAC1G;EACA;EACA;EACA,MAAMQ,2BAA2B,GAAG,CAAC,MAAI;IACrC;IACA,IAAIH,cAAc,IAAI,CAACzF,WAAW,CAACqF,KAAK,CAAC,EAAE,OAAO,KAAK;IACvD;IACA,IAAIG,cAAc,IAAI,CAACxF,WAAW,CAACmC,iBAAiB,CAAC,EAAE,OAAOA,iBAAiB;IAC/E;IACA,IAAIiB,SAAS,CAAC,CAAC,CAACkB,QAAQ,CAAC,CAAC,EAAE,OAAO,KAAK;IACxC;IACA;IACA;IACA,IAAIrC,QAAQ,EAAE,OAAOjC,WAAW,CAACoF,IAAI,CAAC,GAAG,KAAK,GAAGhD,iBAAiB;IAClE;IACA;IACA,OAAOpC,WAAW,CAACoF,IAAI,CAAC,IAAIhD,iBAAiB;EACjD,CAAC,EAAE,CAAC;EACJ;EACA;EACA,MAAMyD,sBAAsB,GAAG,CAAC,EAAE7E,GAAG,IAAIa,OAAO,IAAI2D,cAAc,IAAII,2BAA2B,CAAC;EAClG,MAAMjB,YAAY,GAAG3E,WAAW,CAACsF,MAAM,CAACX,YAAY,CAAC,GAAGkB,sBAAsB,GAAGP,MAAM,CAACX,YAAY;EACpG,MAAMC,SAAS,GAAG5E,WAAW,CAACsF,MAAM,CAACV,SAAS,CAAC,GAAGiB,sBAAsB,GAAGP,MAAM,CAACV,SAAS;EAC3F;EACA;EACA,MAAMkB,UAAU,GAAGzG,WAAW,CAAC,MAAO0G,cAAc,IAAG;IACnD,MAAMC,cAAc,GAAG9C,UAAU,CAACG,OAAO;IACzC,IAAI,CAACrC,GAAG,IAAI,CAACgF,cAAc,IAAIhD,YAAY,CAACK,OAAO,IAAID,SAAS,CAAC,CAAC,CAACkB,QAAQ,CAAC,CAAC,EAAE;MAC3E,OAAO,KAAK;IAChB;IACA,IAAI2B,OAAO;IACX,IAAIC,OAAO;IACX,IAAIC,OAAO,GAAG,IAAI;IAClB,MAAMC,IAAI,GAAGL,cAAc,IAAI,CAAC,CAAC;IACjC;IACA;IACA,MAAMM,qBAAqB,GAAG,CAAC1D,KAAK,CAAC3B,GAAG,CAAC,IAAI,CAACoF,IAAI,CAAC1E,MAAM;IACzD;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACS,MAAM4E,iBAAiB,GAAGA,CAAA,KAAI;MAC3B,IAAI5F,eAAe,EAAE;QACjB,OAAO,CAACsC,YAAY,CAACK,OAAO,IAAIrC,GAAG,KAAKiC,MAAM,CAACI,OAAO,IAAIN,iBAAiB,CAACM,OAAO;MACvF;MACA,OAAOrC,GAAG,KAAKiC,MAAM,CAACI,OAAO;IACjC,CAAC;IACD;IACA,MAAMkD,UAAU,GAAG;MACf5B,YAAY,EAAE,KAAK;MACnBC,SAAS,EAAE;IACf,CAAC;IACD,MAAM4B,2BAA2B,GAAGA,CAAA,KAAI;MACpC9C,QAAQ,CAAC6C,UAAU,CAAC;IACxB,CAAC;IACD,MAAME,YAAY,GAAGA,CAAA,KAAI;MACrB;MACA,MAAMC,WAAW,GAAG/D,KAAK,CAAC3B,GAAG,CAAC;MAC9B,IAAI0F,WAAW,IAAIA,WAAW,CAAC,CAAC,CAAC,KAAKR,OAAO,EAAE;QAC3C,OAAOvD,KAAK,CAAC3B,GAAG,CAAC;MACrB;IACJ,CAAC;IACD;IACA,MAAM2F,YAAY,GAAG;MACjBhC,YAAY,EAAE;IAClB,CAAC;IACD;IACA;IACA,IAAI3E,WAAW,CAACyD,QAAQ,CAAC,CAAC,CAAC2B,IAAI,CAAC,EAAE;MAC9BuB,YAAY,CAAC/B,SAAS,GAAG,IAAI;IACjC;IACA,IAAI;MACA,IAAIyB,qBAAqB,EAAE;QACvB3C,QAAQ,CAACiD,YAAY,CAAC;QACtB;QACA;QACA,IAAI7E,MAAM,CAAC8E,cAAc,IAAI5G,WAAW,CAACyD,QAAQ,CAAC,CAAC,CAAC2B,IAAI,CAAC,EAAE;UACvDyB,UAAU,CAAC,MAAI;YACX,IAAIV,OAAO,IAAIG,iBAAiB,CAAC,CAAC,EAAE;cAChClD,SAAS,CAAC,CAAC,CAAC0D,aAAa,CAAC9F,GAAG,EAAEc,MAAM,CAAC;YAC1C;UACJ,CAAC,EAAEA,MAAM,CAAC8E,cAAc,CAAC;QAC7B;QACA;QACA;QACAjE,KAAK,CAAC3B,GAAG,CAAC,GAAG,CACTgF,cAAc,CAAClD,KAAK,CAAC,EACrB7C,YAAY,CAAC,CAAC,CACjB;MACL;MACA,CAACgG,OAAO,EAAEC,OAAO,CAAC,GAAGvD,KAAK,CAAC3B,GAAG,CAAC;MAC/BiF,OAAO,GAAG,MAAMA,OAAO;MACvB,IAAII,qBAAqB,EAAE;QACvB;QACA;QACAQ,UAAU,CAACJ,YAAY,EAAE3E,MAAM,CAACiF,gBAAgB,CAAC;MACrD;MACA;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACpE,KAAK,CAAC3B,GAAG,CAAC,IAAI2B,KAAK,CAAC3B,GAAG,CAAC,CAAC,CAAC,CAAC,KAAKkF,OAAO,EAAE;QAC1C,IAAIG,qBAAqB,EAAE;UACvB,IAAIC,iBAAiB,CAAC,CAAC,EAAE;YACrBlD,SAAS,CAAC,CAAC,CAAC4D,WAAW,CAAChG,GAAG,CAAC;UAChC;QACJ;QACA,OAAO,KAAK;MAChB;MACA;MACAuF,UAAU,CAAClB,KAAK,GAAGnF,SAAS;MAC5B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,MAAM+G,YAAY,GAAGvE,QAAQ,CAAC1B,GAAG,CAAC;MAClC,IAAI,CAAChB,WAAW,CAACiH,YAAY,CAAC;MAAI;MACjCf,OAAO,IAAIe,YAAY,CAAC,CAAC,CAAC;MAAI;MAC/Bf,OAAO,IAAIe,YAAY,CAAC,CAAC,CAAC;MAAI;MAC9BA,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE;QACpBT,2BAA2B,CAAC,CAAC;QAC7B,IAAIH,qBAAqB,EAAE;UACvB,IAAIC,iBAAiB,CAAC,CAAC,EAAE;YACrBlD,SAAS,CAAC,CAAC,CAAC4D,WAAW,CAAChG,GAAG,CAAC;UAChC;QACJ;QACA,OAAO,KAAK;MAChB;MACA;MACA;MACA,MAAMkG,SAAS,GAAGzD,QAAQ,CAAC,CAAC,CAAC2B,IAAI;MACjC;MACA;MACAmB,UAAU,CAACnB,IAAI,GAAGpD,OAAO,CAACkF,SAAS,EAAEjB,OAAO,CAAC,GAAGiB,SAAS,GAAGjB,OAAO;MACnE;MACA,IAAII,qBAAqB,EAAE;QACvB,IAAIC,iBAAiB,CAAC,CAAC,EAAE;UACrBlD,SAAS,CAAC,CAAC,CAAC+D,SAAS,CAAClB,OAAO,EAAEjF,GAAG,EAAEc,MAAM,CAAC;QAC/C;MACJ;IACJ,CAAC,CAAC,OAAOsF,GAAG,EAAE;MACVX,YAAY,CAAC,CAAC;MACd,MAAMY,aAAa,GAAGjE,SAAS,CAAC,CAAC;MACjC,MAAM;QAAEkE;MAAoB,CAAC,GAAGD,aAAa;MAC7C;MACA,IAAI,CAACA,aAAa,CAAC/C,QAAQ,CAAC,CAAC,EAAE;QAC3B;QACAiC,UAAU,CAAClB,KAAK,GAAG+B,GAAG;QACtB;QACA;QACA,IAAIf,qBAAqB,IAAIC,iBAAiB,CAAC,CAAC,EAAE;UAC9Ce,aAAa,CAACE,OAAO,CAACH,GAAG,EAAEpG,GAAG,EAAEqG,aAAa,CAAC;UAC9C,IAAIC,kBAAkB,KAAK,IAAI,IAAInH,UAAU,CAACmH,kBAAkB,CAAC,IAAIA,kBAAkB,CAACF,GAAG,CAAC,EAAE;YAC1F,IAAI9D,QAAQ,CAAC,CAAC,EAAE;cACZ;cACA;cACA;cACA+D,aAAa,CAACG,YAAY,CAACJ,GAAG,EAAEpG,GAAG,EAAEqG,aAAa,EAAGI,KAAK,IAAG;gBACzD,MAAMC,YAAY,GAAGjF,kBAAkB,CAACzB,GAAG,CAAC;gBAC5C,IAAI0G,YAAY,IAAIA,YAAY,CAAC,CAAC,CAAC,EAAE;kBACjCA,YAAY,CAAC,CAAC,CAAC,CAACtH,gBAAgB,CAACuH,sBAAsB,EAAEF,KAAK,CAAC;gBACnE;cACJ,CAAC,EAAE;gBACCG,UAAU,EAAE,CAACxB,IAAI,CAACwB,UAAU,IAAI,CAAC,IAAI,CAAC;gBACtClG,MAAM,EAAE;cACZ,CAAC,CAAC;YACN;UACJ;QACJ;MACJ;IACJ;IACA;IACAyE,OAAO,GAAG,KAAK;IACf;IACAK,2BAA2B,CAAC,CAAC;IAC7B,OAAO,IAAI;EACf,CAAC;EAAE;EACH;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,CACIxF,GAAG,EACHe,KAAK,CACR,CAAC;EACF;EACA;EACA;EACA,MAAM8F,WAAW,GAAGxI,WAAW;EAAC;EAChC,CAAC,GAAGyI,IAAI,KAAG;IACP,OAAOzH,cAAc,CAAC0B,KAAK,EAAEkB,MAAM,CAACI,OAAO,EAAE,GAAGyE,IAAI,CAAC;EACzD,CAAC;EAAE;EACH,EAAE,CAAC;EACH;EACAxH,yBAAyB,CAAC,MAAI;IAC1B4C,UAAU,CAACG,OAAO,GAAGxB,OAAO;IAC5BsB,SAAS,CAACE,OAAO,GAAGvB,MAAM;IAC1B;IACA;IACA,IAAI,CAAC9B,WAAW,CAAC6E,UAAU,CAAC,EAAE;MAC1Bc,YAAY,CAACtC,OAAO,GAAGwB,UAAU;IACrC;EACJ,CAAC,CAAC;EACF;EACAvE,yBAAyB,CAAC,MAAI;IAC1B,IAAI,CAACU,GAAG,EAAE;IACV,MAAM+G,cAAc,GAAGjC,UAAU,CAACkC,IAAI,CAAC9H,SAAS,EAAEuB,WAAW,CAAC;IAC9D;IACA;IACA,IAAIwG,sBAAsB,GAAG,CAAC;IAC9B,MAAMC,YAAY,GAAGA,CAACC,IAAI,EAAE/B,IAAI,GAAG,CAAC,CAAC,KAAG;MACpC,IAAI+B,IAAI,IAAI/H,gBAAgB,CAACgI,WAAW,EAAE;QACtC,MAAMC,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;QACtB,IAAIjF,SAAS,CAAC,CAAC,CAACmF,iBAAiB,IAAIF,GAAG,GAAGJ,sBAAsB,IAAI3E,QAAQ,CAAC,CAAC,EAAE;UAC7E2E,sBAAsB,GAAGI,GAAG,GAAGjF,SAAS,CAAC,CAAC,CAACoF,qBAAqB;UAChET,cAAc,CAAC,CAAC;QACpB;MACJ,CAAC,MAAM,IAAII,IAAI,IAAI/H,gBAAgB,CAACqI,eAAe,EAAE;QACjD,IAAIrF,SAAS,CAAC,CAAC,CAACsF,qBAAqB,IAAIpF,QAAQ,CAAC,CAAC,EAAE;UACjDyE,cAAc,CAAC,CAAC;QACpB;MACJ,CAAC,MAAM,IAAII,IAAI,IAAI/H,gBAAgB,CAACuI,YAAY,EAAE;QAC9C,OAAO7C,UAAU,CAAC,CAAC;MACvB,CAAC,MAAM,IAAIqC,IAAI,IAAI/H,gBAAgB,CAACuH,sBAAsB,EAAE;QACxD,OAAO7B,UAAU,CAACM,IAAI,CAAC;MAC3B;MACA;IACJ,CAAC;IACD,MAAMwC,WAAW,GAAGrI,iBAAiB,CAACS,GAAG,EAAEyB,kBAAkB,EAAEyF,YAAY,CAAC;IAC5E;IACAlF,YAAY,CAACK,OAAO,GAAG,KAAK;IAC5BJ,MAAM,CAACI,OAAO,GAAGrC,GAAG;IACpB+B,iBAAiB,CAACM,OAAO,GAAG,IAAI;IAChC;IACAK,QAAQ,CAAC;MACLgB,EAAE,EAAE5B;IACR,CAAC,CAAC;IACF;IACA,IAAI8C,2BAA2B,EAAE;MAC7B,IAAI5F,WAAW,CAACoF,IAAI,CAAC,IAAI5E,SAAS,EAAE;QAChC;QACAuH,cAAc,CAAC,CAAC;MACpB,CAAC,MAAM;QACH;QACA;QACAtH,GAAG,CAACsH,cAAc,CAAC;MACvB;IACJ;IACA,OAAO,MAAI;MACP;MACA/E,YAAY,CAACK,OAAO,GAAG,IAAI;MAC3BuF,WAAW,CAAC,CAAC;IACjB,CAAC;EACL,CAAC,EAAE,CACC5H,GAAG,CACN,CAAC;EACF;EACAV,yBAAyB,CAAC,MAAI;IAC1B,IAAIuI,KAAK;IACT,SAASC,IAAIA,CAAA,EAAG;MACZ;MACA;MACA,MAAMC,QAAQ,GAAG5I,UAAU,CAACkC,eAAe,CAAC,GAAGA,eAAe,CAACoB,QAAQ,CAAC,CAAC,CAAC2B,IAAI,CAAC,GAAG/C,eAAe;MACjG;MACA;MACA;MACA,IAAI0G,QAAQ,IAAIF,KAAK,KAAK,CAAC,CAAC,EAAE;QAC1BA,KAAK,GAAGhC,UAAU,CAACmC,OAAO,EAAED,QAAQ,CAAC;MACzC;IACJ;IACA,SAASC,OAAOA,CAAA,EAAG;MACf;MACA;MACA,IAAI,CAACvF,QAAQ,CAAC,CAAC,CAAC4B,KAAK,KAAK/C,iBAAiB,IAAIc,SAAS,CAAC,CAAC,CAACG,SAAS,CAAC,CAAC,CAAC,KAAKhB,kBAAkB,IAAIa,SAAS,CAAC,CAAC,CAACI,QAAQ,CAAC,CAAC,CAAC,EAAE;QACvHsC,UAAU,CAACrE,WAAW,CAAC,CAACH,IAAI,CAACwH,IAAI,CAAC;MACtC,CAAC,MAAM;QACH;QACAA,IAAI,CAAC,CAAC;MACV;IACJ;IACAA,IAAI,CAAC,CAAC;IACN,OAAO,MAAI;MACP,IAAID,KAAK,EAAE;QACPI,YAAY,CAACJ,KAAK,CAAC;QACnBA,KAAK,GAAG,CAAC,CAAC;MACd;IACJ,CAAC;EACL,CAAC,EAAE,CACCxG,eAAe,EACfC,iBAAiB,EACjBC,kBAAkB,EAClBvB,GAAG,CACN,CAAC;EACF;EACA1B,aAAa,CAAC6E,YAAY,CAAC;EAC3B;EACA;EACA;EACA;EACA,IAAIlC,QAAQ,IAAIjC,WAAW,CAACoF,IAAI,CAAC,IAAIpE,GAAG,EAAE;IACtC;IACA;IACA;IACA,IAAI,CAACN,eAAe,IAAIF,SAAS,EAAE;MAC/B,MAAM,IAAI0I,KAAK,CAAC,uDAAuD,CAAC;IAC5E;IACA;IACAhG,UAAU,CAACG,OAAO,GAAGxB,OAAO;IAC5BsB,SAAS,CAACE,OAAO,GAAGvB,MAAM;IAC1BkB,YAAY,CAACK,OAAO,GAAG,KAAK;IAC5B,MAAM8F,GAAG,GAAGvG,OAAO,CAAC5B,GAAG,CAAC;IACxB,IAAI,CAAChB,WAAW,CAACmJ,GAAG,CAAC,EAAE;MACnB,MAAMjI,OAAO,GAAG2G,WAAW,CAACsB,GAAG,CAAC;MAChClI,GAAG,CAACC,OAAO,CAAC;IAChB;IACA,IAAIlB,WAAW,CAACqF,KAAK,CAAC,EAAE;MACpB,MAAMnE,OAAO,GAAG4E,UAAU,CAACrE,WAAW,CAAC;MACvC,IAAI,CAACzB,WAAW,CAACmE,YAAY,CAAC,EAAE;QAC5BjD,OAAO,CAACC,MAAM,GAAG,WAAW;QAC5BD,OAAO,CAACE,KAAK,GAAG,IAAI;MACxB;MACAH,GAAG,CAACC,OAAO,CAAC;IAChB,CAAC,MAAM;MACH,MAAMmE,KAAK;IACf;EACJ;EACA,OAAO;IACHzE,MAAM,EAAEiH,WAAW;IACnB,IAAIzC,IAAIA,CAAA,EAAI;MACRvB,iBAAiB,CAACuB,IAAI,GAAG,IAAI;MAC7B,OAAOjB,YAAY;IACvB,CAAC;IACD,IAAIkB,KAAKA,CAAA,EAAI;MACTxB,iBAAiB,CAACwB,KAAK,GAAG,IAAI;MAC9B,OAAOA,KAAK;IAChB,CAAC;IACD,IAAIV,YAAYA,CAAA,EAAI;MAChBd,iBAAiB,CAACc,YAAY,GAAG,IAAI;MACrC,OAAOA,YAAY;IACvB,CAAC;IACD,IAAIC,SAASA,CAAA,EAAI;MACbf,iBAAiB,CAACe,SAAS,GAAG,IAAI;MAClC,OAAOA,SAAS;IACpB;EACJ,CAAC;AACL,CAAC;AACD,MAAMlF,SAAS,GAAGD,MAAM,CAAC2J,cAAc,CAACzJ,WAAW,EAAE,cAAc,EAAE;EACjEyB,KAAK,EAAExB;AACX,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,MAAMyJ,MAAM,GAAGxJ,QAAQ,CAAC8B,aAAa,CAAC;;AAE1C;;AAEA,SAASjC,SAAS,EAAE2J,MAAM,IAAIC,OAAO,EAAEvI,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}