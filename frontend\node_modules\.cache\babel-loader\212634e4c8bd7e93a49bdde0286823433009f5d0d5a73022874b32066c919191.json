{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport { useOrganizations } from \"@clerk/shared/react\";\nexport { useOrganizations };", "map": {"version": 3, "names": ["useOrganizations"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\hooks\\useOrganizations.ts"], "sourcesContent": ["export { useOrganizations } from '@clerk/shared/react';\n"], "mappings": ";AAAA,SAASA,gBAAA,QAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}