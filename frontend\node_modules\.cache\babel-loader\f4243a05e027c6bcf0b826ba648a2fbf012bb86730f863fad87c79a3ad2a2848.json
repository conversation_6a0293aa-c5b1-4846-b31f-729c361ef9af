{"ast": null, "code": "import { handleValueOrFn } from \"./chunk-TRWMHODU.mjs\";\nimport \"./chunk-NDCDZYN6.mjs\";\nexport { handleValueOrFn };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["import {\n  handleValueOrFn\n} from \"./chunk-TRWMHODU.mjs\";\nimport \"./chunk-NDCDZYN6.mjs\";\nexport {\n  handleValueOrFn\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}