{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport { SessionContext, useSessionContext } from \"@clerk/shared/react\";\nexport { SessionContext, useSessionContext };", "map": {"version": 3, "names": ["SessionContext", "useSessionContext"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\contexts\\SessionContext.tsx"], "sourcesContent": ["export { SessionContext, useSessionContext } from '@clerk/shared/react';\n"], "mappings": ";AAAA,SAASA,cAAA,EAAgBC,iBAAA,QAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}