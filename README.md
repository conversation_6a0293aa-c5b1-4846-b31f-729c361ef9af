# 艺术品推荐系统

基于情感分析的智能艺术品推荐系统，通过分析用户的情感表达来推荐合适的艺术品。

## 功能特点

- **情感检测**：分析用户输入中的情感表达
- **多轮对话**：当情感表达不明确时，引导用户进行更深入的交流
- **个性化推荐**：结合用户情感和偏好推荐艺术品
- **渐进式界面**：从简单问题开始，逐步深入了解用户

## 系统架构

### 后端 (FastAPI)
- **情感分析服务**：使用Gemini API进行情感和偏好提取
- **用户管理系统**：基于Clerk认证的完整用户管理，支持PostgreSQL数据库
- **推荐引擎**：基于情感和偏好匹配艺术品
- **安全防护**：JWT认证、输入验证、SQL注入防护

### 前端 (HTML/CSS/JavaScript)
- **响应式设计**：适配不同设备
- **多页面状态**：初始页面 → 聊天页面 → 推荐页面
- **实时交互**：流畅的用户体验
- **动画效果**：增强视觉体验

## 快速开始

### 方法一：使用启动脚本（推荐）

**Windows用户：**
```bash
# 双击运行或在命令行执行
start.bat
```

**Linux/Mac用户：**
```bash
chmod +x start.sh
./start.sh
```

### 方法二：手动启动

**1. 启动后端服务**
```bash
cd backend
pip install -r requirements.txt
python main.py
```
后端服务将在 `http://localhost:8000` 启动

**2. 启动前端服务**
```bash
cd frontend
python server.py
```
前端服务将在 `http://localhost:3000` 启动，并自动打开浏览器

### 3. 测试系统

访问 `http://localhost:3000/test.html` 进行API测试

### 4. 使用系统

1. 访问 `http://localhost:3000`
2. 在首页回答 "How do you feel today?"
3. 根据情感表达的明确程度：
   - **明确情感**：直接跳转到推荐页面
   - **模糊表达**：进入多轮对话引导
4. 最终获得个性化的艺术品推荐

### 使用示例

**明确情感表达：**
- "我今天非常开心！" → 直接推荐
- "我感到很悲伤..." → 直接推荐
- "我很焦虑，需要平静的艺术品" → 直接推荐

**需要引导的表达：**
- "还好吧" → 系统引导："能告诉我更多关于你今天的感受吗？"
- "嗯" → 系统引导："你现在的心情如何？开心还是有些沮丧？"

## 工作流程

```
用户输入 → 情感检测 → 判断情感强度
    ↓
情感明确 → 直接推荐
    ↓
情感模糊 → 多轮引导 → 情感明确 → 推荐艺术品
```

## API 接口

### POST /api/chat/
聊天和情感分析接口

**请求体：**
```json
{
    "user_id": "string",
    "message": "string"
}
```

**响应：**
```json
{
    "user_id": "string",
    "ai_response": "string",
    "extracted_elements": {
        "mood": "string",
        "emotion_intensity": "low|medium|high",
        "colors": ["string"],
        "themes": ["string"],
        "styles": ["string"],
        "is_recommendation_query": "boolean",
        "needs_guidance": "boolean"
    },
    "recommendation_triggered": "boolean",
    "user_profile": "object"
}
```

### POST /api/recommend/
艺术品推荐接口

**请求体：**
```json
{
    "user_id": "string",
    "extracted_elements": "object",
    "user_profile": "object"
}
```

**响应：**
```json
[
    {
        "id": "string",
        "title": "string",
        "artist": "string",
        "style": "string",
        "colors": ["string"],
        "theme": "string"
    }
]
```

## 配置说明

### 环境变量 (.env)
```
# Gemini API 配置
GEMINI_API_KEY=your_gemini_api_key

# PostgreSQL 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_NAME=artech_db
DB_USER=postgres
DB_PASSWORD=your_password
DB_MIN_CONNECTIONS=5
DB_MAX_CONNECTIONS=20

# Clerk 认证配置
CLERK_SECRET_KEY=your_clerk_secret_key
CLERK_PUBLISHABLE_KEY=your_clerk_publishable_key

# 保留的 DeepSeek 配置（如需要）
DEEPSEEK_API_URL=https://ds.yovole.com/api
DEEPSEEK_API_KEY=your_deepseek_api_key
DEEPSEEK_MODEL=DeepSeek-V3
```

## 技术栈

- **后端**：FastAPI, Pydantic, asyncpg, pyjwt, httpx, python-dotenv
- **前端**：HTML5, CSS3, JavaScript (ES6+)
- **数据库**：PostgreSQL
- **认证**：Clerk
- **AI服务**：Google Gemini API
- **部署**：Uvicorn

## 开发说明

### 项目结构
```
artech/
├── backend/
│   ├── main.py              # 主应用入口
│   ├── database/            # 数据库配置和架构
│   │   ├── config.py        # 数据库连接管理
│   │   └── schema.sql       # 数据库架构
│   ├── user_management/     # 用户管理系统
│   │   ├── models.py        # 用户相关数据模型
│   │   ├── user_repository.py    # 数据访问层
│   │   ├── chat_repository.py # 聊天数据访问层
│   │   ├── service.py       # 业务逻辑层
│   │   ├── auth.py          # Clerk认证中间件
│   │   ├── routes.py        # 用户管理API路由
│   │   └── README.md        # 用户管理系统文档
│   ├── models/              # 通用数据模型
│   ├── routers/             # API路由
│   ├── services/            # 业务服务
│   ├── utils/               # 工具函数
│   └── requirements.txt     # 依赖列表
├── frontend/
│   ├── index.html           # 主页面
│   └── server.py            # 前端服务器
└── README.md
```

## 数据库设置

### 1. 安装 PostgreSQL
```bash
# Ubuntu/Debian
sudo apt-get install postgresql postgresql-contrib

# macOS (使用 Homebrew)
brew install postgresql

# Windows
# 下载并安装 PostgreSQL 官方安装包
```

### 2. 创建数据库
```bash
# 创建数据库
createdb artech_db

# 运行数据库架构脚本
psql -d artech_db -f backend/database/schema.sql
```

### 3. 配置环境变量
在 `backend/.env` 文件中配置数据库连接信息。

## Clerk 认证设置

### 1. 创建 Clerk 应用
1. 访问 [Clerk Dashboard](https://dashboard.clerk.dev/)
2. 创建新应用
3. 获取 API 密钥

### 2. 配置环境变量
```env
CLERK_SECRET_KEY=sk_test_...
CLERK_PUBLISHABLE_KEY=pk_test_...
```

### 3. 前端集成
在前端应用中集成 Clerk SDK，获取用户 token 后调用后端 API。

## 新功能特性

### 🔐 强健的用户认证系统
- Clerk 集成，支持多种登录方式
- JWT token 验证
- 自动用户创建和管理

### 📊 完整的用户数据管理
- 用户偏好跟踪
- 情绪历史记录
- 聊天会话管理
- 推荐历史和反馈

### 🛡️ 企业级安全特性
- SQL 注入防护
- 输入验证和清理
- 认证中间件
- 活动日志记录

### 扩展建议

1. **数据库集成**：使用PostgreSQL或MongoDB存储用户数据
2. **用户认证**：添加登录注册功能
3. **艺术品数据库**：集成真实的艺术品数据源
4. **推荐算法优化**：使用机器学习改进推荐精度
5. **移动端适配**：开发移动应用
6. **多语言支持**：支持更多语言

## 许可证

MIT License
