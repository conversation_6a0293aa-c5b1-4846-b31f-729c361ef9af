{"ast": null, "code": "/**\n * @license lucide-react v0.294.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */\n\nimport createLucideIcon from '../createLucideIcon.js';\nconst LampWallUp = createLucideIcon(\"LampWallUp\", [[\"path\", {\n  d: \"M11 4h6l3 7H8l3-7Z\",\n  key: \"11x1ee\"\n}], [\"path\", {\n  d: \"M14 11v5a2 2 0 0 1-2 2H8\",\n  key: \"eutp5o\"\n}], [\"path\", {\n  d: \"M4 15h2a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2H4v-6Z\",\n  key: \"1iuthr\"\n}]]);\nexport { LampWallUp as default };", "map": {"version": 3, "names": ["LampWallUp", "createLucideIcon", "d", "key"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\lucide-react\\src\\icons\\lamp-wall-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\n\n/**\n * @component @name LampWallUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTEgNGg2bDMgN0g4bDMtN1oiIC8+CiAgPHBhdGggZD0iTTE0IDExdjVhMiAyIDAgMCAxLTIgMkg4IiAvPgogIDxwYXRoIGQ9Ik00IDE1aDJhMiAyIDAgMCAxIDIgMnYyYTIgMiAwIDAgMS0yIDJINHYtNloiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/lamp-wall-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LampWallUp = createLucideIcon('LampWallUp', [\n  ['path', { d: 'M11 4h6l3 7H8l3-7Z', key: '11x1ee' }],\n  ['path', { d: 'M14 11v5a2 2 0 0 1-2 2H8', key: 'eutp5o' }],\n  ['path', { d: 'M4 15h2a2 2 0 0 1 2 2v2a2 2 0 0 1-2 2H4v-6Z', key: '1iuthr' }],\n]);\n\nexport default LampWallUp;\n"], "mappings": ";;;;;;;;AAaM,MAAAA,UAAA,GAAaC,gBAAA,CAAiB,YAAc,GAChD,CAAC,MAAQ;EAAEC,CAAA,EAAG,oBAAsB;EAAAC,GAAA,EAAK;AAAA,CAAU,GACnD,CAAC,MAAQ;EAAED,CAAA,EAAG,0BAA4B;EAAAC,GAAA,EAAK;AAAA,CAAU,GACzD,CAAC,MAAQ;EAAED,CAAA,EAAG,6CAA+C;EAAAC,GAAA,EAAK;AAAA,CAAU,EAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}