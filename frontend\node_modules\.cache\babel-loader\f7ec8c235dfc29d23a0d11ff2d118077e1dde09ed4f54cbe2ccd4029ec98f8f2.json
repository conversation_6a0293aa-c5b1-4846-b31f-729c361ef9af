{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nconst clerkLoaded = isomorphicClerk => {\n  return new Promise(resolve => {\n    if (isomorphicClerk.loaded) {\n      resolve();\n    }\n    isomorphicClerk.addOnLoaded(resolve);\n  });\n};\nconst createGetToken = isomorphicClerk => {\n  return async options => {\n    await clerkLoaded(isomorphicClerk);\n    if (!isomorphicClerk.session) {\n      return null;\n    }\n    return isomorphicClerk.session.getToken(options);\n  };\n};\nconst createSignOut = isomorphicClerk => {\n  return async (...args) => {\n    await clerkLoaded(isomorphicClerk);\n    return isomorphicClerk.signOut(...args);\n  };\n};\nexport { createGetToken, createSignOut };", "map": {"version": 3, "names": ["clerkLoaded", "isomorphicClerk", "Promise", "resolve", "loaded", "addOnLoaded", "createGetToken", "options", "session", "getToken", "createSignOut", "args", "signOut"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\hooks\\utils.ts"], "sourcesContent": ["import type IsomorphicClerk from '../isomorphicClerk';\n\n/**\n * @internal\n */\nconst clerkLoaded = (isomorphicClerk: IsomorphicClerk) => {\n  return new Promise<void>(resolve => {\n    if (isomorphicClerk.loaded) {\n      resolve();\n    }\n    isomorphicClerk.addOnLoaded(resolve);\n  });\n};\n\n/**\n * @internal\n */\nexport const createGetToken = (isomorphicClerk: IsomorphicClerk) => {\n  return async (options: any) => {\n    await clerkLoaded(isomorphicClerk);\n    if (!isomorphicClerk.session) {\n      return null;\n    }\n    return isomorphicClerk.session.getToken(options);\n  };\n};\n\n/**\n * @internal\n */\nexport const createSignOut = (isomorphicClerk: IsomorphicClerk) => {\n  return async (...args: any) => {\n    await clerkLoaded(isomorphicClerk);\n    return isomorphicClerk.signOut(...args);\n  };\n};\n"], "mappings": ";AAKA,MAAMA,WAAA,GAAeC,eAAA,IAAqC;EACxD,OAAO,IAAIC,OAAA,CAAcC,OAAA,IAAW;IAClC,IAAIF,eAAA,CAAgBG,MAAA,EAAQ;MAC1BD,OAAA,CAAQ;IACV;IACAF,eAAA,CAAgBI,WAAA,CAAYF,OAAO;EACrC,CAAC;AACH;AAKO,MAAMG,cAAA,GAAkBL,eAAA,IAAqC;EAClE,OAAO,MAAOM,OAAA,IAAiB;IAC7B,MAAMP,WAAA,CAAYC,eAAe;IACjC,IAAI,CAACA,eAAA,CAAgBO,OAAA,EAAS;MAC5B,OAAO;IACT;IACA,OAAOP,eAAA,CAAgBO,OAAA,CAAQC,QAAA,CAASF,OAAO;EACjD;AACF;AAKO,MAAMG,aAAA,GAAiBT,eAAA,IAAqC;EACjE,OAAO,UAAUU,IAAA,KAAc;IAC7B,MAAMX,WAAA,CAAYC,eAAe;IACjC,OAAOA,eAAA,CAAgBW,OAAA,CAAQ,GAAGD,IAAI;EACxC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}