{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport { useClientContext } from \"../contexts/ClientContext\";\nimport { useIsomorphicClerkContext } from \"../contexts/IsomorphicClerkContext\";\nconst useSessionList = () => {\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const client = useClientContext();\n  if (!client) {\n    return {\n      isLoaded: false,\n      sessions: void 0,\n      setSession: void 0,\n      setActive: void 0\n    };\n  }\n  return {\n    isLoaded: true,\n    sessions: client.sessions,\n    setSession: isomorphicClerk.setSession,\n    setActive: isomorphicClerk.setActive\n  };\n};\nexport { useSessionList };", "map": {"version": 3, "names": ["useClientContext", "useIsomorphicClerkContext", "useSessionList", "isomorphicClerk", "client", "isLoaded", "sessions", "setSession", "setActive"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\hooks\\useSessionList.ts"], "sourcesContent": ["import type { SessionResource, SetActive, SetSession } from '@clerk/types';\n\nimport { useClientContext } from '../contexts/ClientContext';\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\n\ntype UseSessionListReturn =\n  | {\n      isLoaded: false;\n      sessions: undefined;\n      /**\n       * @deprecated This method is deprecated and will be removed in the future. Use {@link Clerk.setActive} instead\n       * Set the current session explicitly. Setting the session to `null` unsets the active session and signs out the user.\n       * @param session Passed session resource object, session id (string version) or null\n       * @param beforeEmit Callback run just before the active session is set to the passed object. Can be used to hook up for pre-navigation actions.\n       */\n      setSession: undefined;\n      setActive: undefined;\n    }\n  | {\n      isLoaded: true;\n      sessions: SessionResource[];\n\n      /**\n       * @deprecated This method is deprecated and will be removed in the future. Use {@link Clerk.setActive} instead\n       * Set the current session explicitly. Setting the session to `null` unsets the active session and signs out the user.\n       * @param session Passed session resource object, session id (string version) or null\n       * @param beforeEmit Callback run just before the active session is set to the passed object. Can be used to hook up for pre-navigation actions.\n       */\n      setSession: SetSession;\n      setActive: SetActive;\n    };\n\ntype UseSessionList = () => UseSessionListReturn;\n\nexport const useSessionList: UseSessionList = () => {\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const client = useClientContext();\n\n  if (!client) {\n    return { isLoaded: false, sessions: undefined, setSession: undefined, setActive: undefined };\n  }\n\n  return {\n    isLoaded: true,\n    sessions: client.sessions,\n    setSession: isomorphicClerk.setSession,\n    setActive: isomorphicClerk.setActive,\n  };\n};\n"], "mappings": ";AAEA,SAASA,gBAAA,QAAwB;AACjC,SAASC,yBAAA,QAAiC;AA+BnC,MAAMC,cAAA,GAAiCA,CAAA,KAAM;EAClD,MAAMC,eAAA,GAAkBF,yBAAA,CAA0B;EAClD,MAAMG,MAAA,GAASJ,gBAAA,CAAiB;EAEhC,IAAI,CAACI,MAAA,EAAQ;IACX,OAAO;MAAEC,QAAA,EAAU;MAAOC,QAAA,EAAU;MAAWC,UAAA,EAAY;MAAWC,SAAA,EAAW;IAAU;EAC7F;EAEA,OAAO;IACLH,QAAA,EAAU;IACVC,QAAA,EAAUF,MAAA,CAAOE,QAAA;IACjBC,UAAA,EAAYJ,eAAA,CAAgBI,UAAA;IAC5BC,SAAA,EAAWL,eAAA,CAAgBK;EAC7B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}