{"ast": null, "code": "import { ClerkAP<PERSON><PERSON>ponseError, ClerkRuntimeError, EmailLinkError, EmailLinkErrorCode, MagicLinkError, MagicLinkErrorCode, buildErrorThrower, is4xxError, isCaptchaError, isClerkAPIResponseError, isClerkRuntimeError, isEmailLinkError, isKnownError, isMagicLinkError, isMetamaskError, isNetworkError, isPasswordPwnedError, isUnauthorizedError, isUserLockedError, parseError, parseErrors } from \"./chunk-VN4YMSVR.mjs\";\nimport \"./chunk-IC4FGZI3.mjs\";\nimport \"./chunk-NDCDZYN6.mjs\";\nexport { ClerkAPIResponseError, ClerkRuntimeError, EmailLinkError, EmailLinkErrorCode, MagicLinkError, MagicLinkErrorCode, buildErrorThrower, is4xxError, isCaptchaError, isClerkAPIResponseError, isClerkRuntimeError, isEmailLinkError, isKnownError, isMagicLinkError, isMetamaskError, isNetworkError, isPasswordPwnedError, isUnauthorizedError, isUserLockedError, parseError, parseErrors };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["import {\n  ClerkAP<PERSON><PERSON>ponseError,\n  ClerkRuntimeError,\n  EmailLinkError,\n  EmailLinkErrorCode,\n  MagicLinkError,\n  MagicLinkErrorCode,\n  buildErrorThrower,\n  is4xxError,\n  isCaptchaError,\n  isClerkAPIResponseError,\n  isClerkRuntimeError,\n  isEmailLinkError,\n  isKnownError,\n  isMagicLinkError,\n  isMetamaskError,\n  isNetworkError,\n  isPasswordPwnedError,\n  isUnauthorizedError,\n  isUserLockedError,\n  parseError,\n  parseErrors\n} from \"./chunk-VN4YMSVR.mjs\";\nimport \"./chunk-IC4FGZI3.mjs\";\nimport \"./chunk-NDCDZYN6.mjs\";\nexport {\n  ClerkAPIResponseError,\n  ClerkRuntimeError,\n  EmailLinkError,\n  EmailLinkErrorCode,\n  MagicLinkError,\n  MagicLinkErrorCode,\n  buildErrorThrower,\n  is4xxError,\n  isCaptchaError,\n  isClerkAPIResponseError,\n  isClerkRuntimeError,\n  isEmailLinkError,\n  isKnownError,\n  isMagicLinkError,\n  isMetamaskError,\n  isNetworkError,\n  isPasswordPwnedError,\n  isUnauthorizedError,\n  isUserLockedError,\n  parseError,\n  parseErrors\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}