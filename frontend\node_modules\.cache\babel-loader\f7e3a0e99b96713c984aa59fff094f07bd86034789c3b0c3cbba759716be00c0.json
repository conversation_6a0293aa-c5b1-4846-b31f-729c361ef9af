{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport { logErrorInDevMode } from \"@clerk/shared\";\nimport React from \"react\";\nimport { OrganizationProfileLink, OrganizationProfilePage, UserProfileLink, UserProfilePage } from \"../components/uiComponents\";\nimport { customLinkWrongProps, customPagesIgnoredComponent, customPageWrongProps } from \"../errors\";\nimport { useCustomElementPortal } from \"./useCustomElementPortal\";\nconst isThatComponent = (v, component) => {\n  return !!v && React.isValidElement(v) && (v == null ? void 0 : v.type) === component;\n};\nconst useUserProfileCustomPages = children => {\n  const reorderItemsLabels = [\"account\", \"security\"];\n  return useCustomPages({\n    children,\n    reorderItemsLabels,\n    LinkComponent: UserProfileLink,\n    PageComponent: UserProfilePage,\n    componentName: \"UserProfile\"\n  });\n};\nconst useOrganizationProfileCustomPages = children => {\n  const reorderItemsLabels = [\"members\", \"settings\"];\n  return useCustomPages({\n    children,\n    reorderItemsLabels,\n    LinkComponent: OrganizationProfileLink,\n    PageComponent: OrganizationProfilePage,\n    componentName: \"OrganizationProfile\"\n  });\n};\nconst useCustomPages = ({\n  children,\n  LinkComponent,\n  PageComponent,\n  reorderItemsLabels,\n  componentName\n}) => {\n  const validChildren = [];\n  React.Children.forEach(children, child => {\n    if (!isThatComponent(child, PageComponent) && !isThatComponent(child, LinkComponent)) {\n      if (child) {\n        logErrorInDevMode(customPagesIgnoredComponent(componentName));\n      }\n      return;\n    }\n    const {\n      props\n    } = child;\n    const {\n      children: children2,\n      label,\n      url,\n      labelIcon\n    } = props;\n    if (isThatComponent(child, PageComponent)) {\n      if (isReorderItem(props, reorderItemsLabels)) {\n        validChildren.push({\n          label\n        });\n      } else if (isCustomPage(props)) {\n        validChildren.push({\n          label,\n          labelIcon,\n          children: children2,\n          url\n        });\n      } else {\n        logErrorInDevMode(customPageWrongProps(componentName));\n        return;\n      }\n    }\n    if (isThatComponent(child, LinkComponent)) {\n      if (isExternalLink(props)) {\n        validChildren.push({\n          label,\n          labelIcon,\n          url\n        });\n      } else {\n        logErrorInDevMode(customLinkWrongProps(componentName));\n        return;\n      }\n    }\n  });\n  const customPageContents = [];\n  const customPageLabelIcons = [];\n  const customLinkLabelIcons = [];\n  validChildren.forEach((cp, index) => {\n    if (isCustomPage(cp)) {\n      customPageContents.push({\n        component: cp.children,\n        id: index\n      });\n      customPageLabelIcons.push({\n        component: cp.labelIcon,\n        id: index\n      });\n      return;\n    }\n    if (isExternalLink(cp)) {\n      customLinkLabelIcons.push({\n        component: cp.labelIcon,\n        id: index\n      });\n    }\n  });\n  const customPageContentsPortals = useCustomElementPortal(customPageContents);\n  const customPageLabelIconsPortals = useCustomElementPortal(customPageLabelIcons);\n  const customLinkLabelIconsPortals = useCustomElementPortal(customLinkLabelIcons);\n  const customPages = [];\n  const customPagesPortals = [];\n  validChildren.forEach((cp, index) => {\n    if (isReorderItem(cp, reorderItemsLabels)) {\n      customPages.push({\n        label: cp.label\n      });\n      return;\n    }\n    if (isCustomPage(cp)) {\n      const {\n        portal: contentPortal,\n        mount,\n        unmount\n      } = customPageContentsPortals.find(p => p.id === index);\n      const {\n        portal: labelPortal,\n        mount: mountIcon,\n        unmount: unmountIcon\n      } = customPageLabelIconsPortals.find(p => p.id === index);\n      customPages.push({\n        label: cp.label,\n        url: cp.url,\n        mount,\n        unmount,\n        mountIcon,\n        unmountIcon\n      });\n      customPagesPortals.push(contentPortal);\n      customPagesPortals.push(labelPortal);\n      return;\n    }\n    if (isExternalLink(cp)) {\n      const {\n        portal: labelPortal,\n        mount: mountIcon,\n        unmount: unmountIcon\n      } = customLinkLabelIconsPortals.find(p => p.id === index);\n      customPages.push({\n        label: cp.label,\n        url: cp.url,\n        mountIcon,\n        unmountIcon\n      });\n      customPagesPortals.push(labelPortal);\n      return;\n    }\n  });\n  return {\n    customPages,\n    customPagesPortals\n  };\n};\nconst isReorderItem = (childProps, validItems) => {\n  const {\n    children,\n    label,\n    url,\n    labelIcon\n  } = childProps;\n  return !children && !url && !labelIcon && validItems.some(v => v === label);\n};\nconst isCustomPage = childProps => {\n  const {\n    children,\n    label,\n    url,\n    labelIcon\n  } = childProps;\n  return !!children && !!url && !!labelIcon && !!label;\n};\nconst isExternalLink = childProps => {\n  const {\n    children,\n    label,\n    url,\n    labelIcon\n  } = childProps;\n  return !children && !!url && !!labelIcon && !!label;\n};\nexport { useOrganizationProfileCustomPages, useUserProfileCustomPages };", "map": {"version": 3, "names": ["logErrorInDevMode", "React", "OrganizationProfileLink", "OrganizationProfilePage", "UserProfileLink", "UserProfilePage", "customLinkWrongProps", "customPagesIgnoredComponent", "customPageWrongProps", "useCustomElementPortal", "isThatComponent", "v", "component", "isValidElement", "type", "useUserProfileCustomPages", "children", "reorderItemsLabels", "useCustomPages", "LinkComponent", "PageComponent", "componentName", "useOrganizationProfileCustomPages", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Children", "for<PERSON>ach", "child", "props", "children2", "label", "url", "labelIcon", "isReorderItem", "push", "isCustomPage", "isExternalLink", "customPageContents", "customPageLabelIcons", "customLinkLabelIcons", "cp", "index", "id", "customPageContentsPortals", "customPageLabelIconsPortals", "customLinkLabelIconsPortals", "customPages", "customPagesPortals", "portal", "contentPortal", "mount", "unmount", "find", "p", "labelPortal", "mountIcon", "unmountIcon", "childProps", "validItems", "some"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\utils\\useCustomPages.tsx"], "sourcesContent": ["import { logErrorInDevMode } from '@clerk/shared';\nimport type { CustomPage } from '@clerk/types';\nimport type { ReactElement } from 'react';\nimport React from 'react';\n\nimport {\n  OrganizationProfileLink,\n  OrganizationProfilePage,\n  UserProfileLink,\n  UserProfilePage,\n} from '../components/uiComponents';\nimport { customLinkWrongProps, customPagesIgnoredComponent, customPageWrongProps } from '../errors';\nimport type { UserProfilePageProps } from '../types';\nimport type { UseCustomElementPortalParams, UseCustomElementPortalReturn } from './useCustomElementPortal';\nimport { useCustomElementPortal } from './useCustomElementPortal';\n\nconst isThatComponent = (v: any, component: React.ReactNode): v is React.ReactNode => {\n  return !!v && React.isValidElement(v) && (v as React.ReactElement)?.type === component;\n};\n\nexport const useUserProfileCustomPages = (children: React.ReactNode | React.ReactNode[]) => {\n  const reorderItemsLabels = ['account', 'security'];\n  return useCustomPages({\n    children,\n    reorderItemsLabels,\n    LinkComponent: UserProfileLink,\n    PageComponent: UserProfilePage,\n    componentName: 'UserProfile',\n  });\n};\n\nexport const useOrganizationProfileCustomPages = (children: React.ReactNode | React.ReactNode[]) => {\n  const reorderItemsLabels = ['members', 'settings'];\n  return useCustomPages({\n    children,\n    reorderItemsLabels,\n    LinkComponent: OrganizationProfileLink,\n    PageComponent: OrganizationProfilePage,\n    componentName: 'OrganizationProfile',\n  });\n};\n\ntype UseCustomPagesParams = {\n  children: React.ReactNode | React.ReactNode[];\n  LinkComponent: any;\n  PageComponent: any;\n  reorderItemsLabels: string[];\n  componentName: string;\n};\n\ntype CustomPageWithIdType = UserProfilePageProps & { children?: React.ReactNode };\n\nconst useCustomPages = ({\n  children,\n  LinkComponent,\n  PageComponent,\n  reorderItemsLabels,\n  componentName,\n}: UseCustomPagesParams) => {\n  const validChildren: CustomPageWithIdType[] = [];\n\n  React.Children.forEach(children, child => {\n    if (!isThatComponent(child, PageComponent) && !isThatComponent(child, LinkComponent)) {\n      if (child) {\n        logErrorInDevMode(customPagesIgnoredComponent(componentName));\n      }\n      return;\n    }\n\n    const { props } = child as ReactElement;\n\n    const { children, label, url, labelIcon } = props;\n\n    if (isThatComponent(child, PageComponent)) {\n      if (isReorderItem(props, reorderItemsLabels)) {\n        // This is a reordering item\n        validChildren.push({ label });\n      } else if (isCustomPage(props)) {\n        // this is a custom page\n        validChildren.push({ label, labelIcon, children, url });\n      } else {\n        logErrorInDevMode(customPageWrongProps(componentName));\n        return;\n      }\n    }\n\n    if (isThatComponent(child, LinkComponent)) {\n      if (isExternalLink(props)) {\n        // This is an external link\n        validChildren.push({ label, labelIcon, url });\n      } else {\n        logErrorInDevMode(customLinkWrongProps(componentName));\n        return;\n      }\n    }\n  });\n\n  const customPageContents: UseCustomElementPortalParams[] = [];\n  const customPageLabelIcons: UseCustomElementPortalParams[] = [];\n  const customLinkLabelIcons: UseCustomElementPortalParams[] = [];\n\n  validChildren.forEach((cp, index) => {\n    if (isCustomPage(cp)) {\n      customPageContents.push({ component: cp.children, id: index });\n      customPageLabelIcons.push({ component: cp.labelIcon, id: index });\n      return;\n    }\n    if (isExternalLink(cp)) {\n      customLinkLabelIcons.push({ component: cp.labelIcon, id: index });\n    }\n  });\n\n  const customPageContentsPortals = useCustomElementPortal(customPageContents);\n  const customPageLabelIconsPortals = useCustomElementPortal(customPageLabelIcons);\n  const customLinkLabelIconsPortals = useCustomElementPortal(customLinkLabelIcons);\n\n  const customPages: CustomPage[] = [];\n  const customPagesPortals: React.ComponentType[] = [];\n\n  validChildren.forEach((cp, index) => {\n    if (isReorderItem(cp, reorderItemsLabels)) {\n      customPages.push({ label: cp.label });\n      return;\n    }\n    if (isCustomPage(cp)) {\n      const {\n        portal: contentPortal,\n        mount,\n        unmount,\n      } = customPageContentsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      const {\n        portal: labelPortal,\n        mount: mountIcon,\n        unmount: unmountIcon,\n      } = customPageLabelIconsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      customPages.push({ label: cp.label, url: cp.url, mount, unmount, mountIcon, unmountIcon });\n      customPagesPortals.push(contentPortal);\n      customPagesPortals.push(labelPortal);\n      return;\n    }\n    if (isExternalLink(cp)) {\n      const {\n        portal: labelPortal,\n        mount: mountIcon,\n        unmount: unmountIcon,\n      } = customLinkLabelIconsPortals.find(p => p.id === index) as UseCustomElementPortalReturn;\n      customPages.push({ label: cp.label, url: cp.url, mountIcon, unmountIcon });\n      customPagesPortals.push(labelPortal);\n      return;\n    }\n  });\n\n  return { customPages, customPagesPortals };\n};\n\nconst isReorderItem = (childProps: any, validItems: string[]): boolean => {\n  const { children, label, url, labelIcon } = childProps;\n  return !children && !url && !labelIcon && validItems.some(v => v === label);\n};\n\nconst isCustomPage = (childProps: any): boolean => {\n  const { children, label, url, labelIcon } = childProps;\n  return !!children && !!url && !!labelIcon && !!label;\n};\n\nconst isExternalLink = (childProps: any): boolean => {\n  const { children, label, url, labelIcon } = childProps;\n  return !children && !!url && !!labelIcon && !!label;\n};\n"], "mappings": ";AAAA,SAASA,iBAAA,QAAyB;AAGlC,OAAOC,KAAA,MAAW;AAElB,SACEC,uBAAA,EACAC,uBAAA,EACAC,eAAA,EACAC,eAAA,QACK;AACP,SAASC,oBAAA,EAAsBC,2BAAA,EAA6BC,oBAAA,QAA4B;AAGxF,SAASC,sBAAA,QAA8B;AAEvC,MAAMC,eAAA,GAAkBA,CAACC,CAAA,EAAQC,SAAA,KAAqD;EACpF,OAAO,CAAC,CAACD,CAAA,IAAKV,KAAA,CAAMY,cAAA,CAAeF,CAAC,MAAMA,CAAA,oBAAAA,CAAA,CAA0BG,IAAA,MAASF,SAAA;AAC/E;AAEO,MAAMG,yBAAA,GAA6BC,QAAA,IAAkD;EAC1F,MAAMC,kBAAA,GAAqB,CAAC,WAAW,UAAU;EACjD,OAAOC,cAAA,CAAe;IACpBF,QAAA;IACAC,kBAAA;IACAE,aAAA,EAAef,eAAA;IACfgB,aAAA,EAAef,eAAA;IACfgB,aAAA,EAAe;EACjB,CAAC;AACH;AAEO,MAAMC,iCAAA,GAAqCN,QAAA,IAAkD;EAClG,MAAMC,kBAAA,GAAqB,CAAC,WAAW,UAAU;EACjD,OAAOC,cAAA,CAAe;IACpBF,QAAA;IACAC,kBAAA;IACAE,aAAA,EAAejB,uBAAA;IACfkB,aAAA,EAAejB,uBAAA;IACfkB,aAAA,EAAe;EACjB,CAAC;AACH;AAYA,MAAMH,cAAA,GAAiBA,CAAC;EACtBF,QAAA;EACAG,aAAA;EACAC,aAAA;EACAH,kBAAA;EACAI;AACF,MAA4B;EAC1B,MAAME,aAAA,GAAwC,EAAC;EAE/CtB,KAAA,CAAMuB,QAAA,CAASC,OAAA,CAAQT,QAAA,EAAUU,KAAA,IAAS;IACxC,IAAI,CAAChB,eAAA,CAAgBgB,KAAA,EAAON,aAAa,KAAK,CAACV,eAAA,CAAgBgB,KAAA,EAAOP,aAAa,GAAG;MACpF,IAAIO,KAAA,EAAO;QACT1B,iBAAA,CAAkBO,2BAAA,CAA4Bc,aAAa,CAAC;MAC9D;MACA;IACF;IAEA,MAAM;MAAEM;IAAM,IAAID,KAAA;IAElB,MAAM;MAAEV,QAAA,EAAAY,SAAA;MAAUC,KAAA;MAAOC,GAAA;MAAKC;IAAU,IAAIJ,KAAA;IAE5C,IAAIjB,eAAA,CAAgBgB,KAAA,EAAON,aAAa,GAAG;MACzC,IAAIY,aAAA,CAAcL,KAAA,EAAOV,kBAAkB,GAAG;QAE5CM,aAAA,CAAcU,IAAA,CAAK;UAAEJ;QAAM,CAAC;MAC9B,WAAWK,YAAA,CAAaP,KAAK,GAAG;QAE9BJ,aAAA,CAAcU,IAAA,CAAK;UAAEJ,KAAA;UAAOE,SAAA;UAAWf,QAAA,EAAAY,SAAA;UAAUE;QAAI,CAAC;MACxD,OAAO;QACL9B,iBAAA,CAAkBQ,oBAAA,CAAqBa,aAAa,CAAC;QACrD;MACF;IACF;IAEA,IAAIX,eAAA,CAAgBgB,KAAA,EAAOP,aAAa,GAAG;MACzC,IAAIgB,cAAA,CAAeR,KAAK,GAAG;QAEzBJ,aAAA,CAAcU,IAAA,CAAK;UAAEJ,KAAA;UAAOE,SAAA;UAAWD;QAAI,CAAC;MAC9C,OAAO;QACL9B,iBAAA,CAAkBM,oBAAA,CAAqBe,aAAa,CAAC;QACrD;MACF;IACF;EACF,CAAC;EAED,MAAMe,kBAAA,GAAqD,EAAC;EAC5D,MAAMC,oBAAA,GAAuD,EAAC;EAC9D,MAAMC,oBAAA,GAAuD,EAAC;EAE9Df,aAAA,CAAcE,OAAA,CAAQ,CAACc,EAAA,EAAIC,KAAA,KAAU;IACnC,IAAIN,YAAA,CAAaK,EAAE,GAAG;MACpBH,kBAAA,CAAmBH,IAAA,CAAK;QAAErB,SAAA,EAAW2B,EAAA,CAAGvB,QAAA;QAAUyB,EAAA,EAAID;MAAM,CAAC;MAC7DH,oBAAA,CAAqBJ,IAAA,CAAK;QAAErB,SAAA,EAAW2B,EAAA,CAAGR,SAAA;QAAWU,EAAA,EAAID;MAAM,CAAC;MAChE;IACF;IACA,IAAIL,cAAA,CAAeI,EAAE,GAAG;MACtBD,oBAAA,CAAqBL,IAAA,CAAK;QAAErB,SAAA,EAAW2B,EAAA,CAAGR,SAAA;QAAWU,EAAA,EAAID;MAAM,CAAC;IAClE;EACF,CAAC;EAED,MAAME,yBAAA,GAA4BjC,sBAAA,CAAuB2B,kBAAkB;EAC3E,MAAMO,2BAAA,GAA8BlC,sBAAA,CAAuB4B,oBAAoB;EAC/E,MAAMO,2BAAA,GAA8BnC,sBAAA,CAAuB6B,oBAAoB;EAE/E,MAAMO,WAAA,GAA4B,EAAC;EACnC,MAAMC,kBAAA,GAA4C,EAAC;EAEnDvB,aAAA,CAAcE,OAAA,CAAQ,CAACc,EAAA,EAAIC,KAAA,KAAU;IACnC,IAAIR,aAAA,CAAcO,EAAA,EAAItB,kBAAkB,GAAG;MACzC4B,WAAA,CAAYZ,IAAA,CAAK;QAAEJ,KAAA,EAAOU,EAAA,CAAGV;MAAM,CAAC;MACpC;IACF;IACA,IAAIK,YAAA,CAAaK,EAAE,GAAG;MACpB,MAAM;QACJQ,MAAA,EAAQC,aAAA;QACRC,KAAA;QACAC;MACF,IAAIR,yBAAA,CAA0BS,IAAA,CAAKC,CAAA,IAAKA,CAAA,CAAEX,EAAA,KAAOD,KAAK;MACtD,MAAM;QACJO,MAAA,EAAQM,WAAA;QACRJ,KAAA,EAAOK,SAAA;QACPJ,OAAA,EAASK;MACX,IAAIZ,2BAAA,CAA4BQ,IAAA,CAAKC,CAAA,IAAKA,CAAA,CAAEX,EAAA,KAAOD,KAAK;MACxDK,WAAA,CAAYZ,IAAA,CAAK;QAAEJ,KAAA,EAAOU,EAAA,CAAGV,KAAA;QAAOC,GAAA,EAAKS,EAAA,CAAGT,GAAA;QAAKmB,KAAA;QAAOC,OAAA;QAASI,SAAA;QAAWC;MAAY,CAAC;MACzFT,kBAAA,CAAmBb,IAAA,CAAKe,aAAa;MACrCF,kBAAA,CAAmBb,IAAA,CAAKoB,WAAW;MACnC;IACF;IACA,IAAIlB,cAAA,CAAeI,EAAE,GAAG;MACtB,MAAM;QACJQ,MAAA,EAAQM,WAAA;QACRJ,KAAA,EAAOK,SAAA;QACPJ,OAAA,EAASK;MACX,IAAIX,2BAAA,CAA4BO,IAAA,CAAKC,CAAA,IAAKA,CAAA,CAAEX,EAAA,KAAOD,KAAK;MACxDK,WAAA,CAAYZ,IAAA,CAAK;QAAEJ,KAAA,EAAOU,EAAA,CAAGV,KAAA;QAAOC,GAAA,EAAKS,EAAA,CAAGT,GAAA;QAAKwB,SAAA;QAAWC;MAAY,CAAC;MACzET,kBAAA,CAAmBb,IAAA,CAAKoB,WAAW;MACnC;IACF;EACF,CAAC;EAED,OAAO;IAAER,WAAA;IAAaC;EAAmB;AAC3C;AAEA,MAAMd,aAAA,GAAgBA,CAACwB,UAAA,EAAiBC,UAAA,KAAkC;EACxE,MAAM;IAAEzC,QAAA;IAAUa,KAAA;IAAOC,GAAA;IAAKC;EAAU,IAAIyB,UAAA;EAC5C,OAAO,CAACxC,QAAA,IAAY,CAACc,GAAA,IAAO,CAACC,SAAA,IAAa0B,UAAA,CAAWC,IAAA,CAAK/C,CAAA,IAAKA,CAAA,KAAMkB,KAAK;AAC5E;AAEA,MAAMK,YAAA,GAAgBsB,UAAA,IAA6B;EACjD,MAAM;IAAExC,QAAA;IAAUa,KAAA;IAAOC,GAAA;IAAKC;EAAU,IAAIyB,UAAA;EAC5C,OAAO,CAAC,CAACxC,QAAA,IAAY,CAAC,CAACc,GAAA,IAAO,CAAC,CAACC,SAAA,IAAa,CAAC,CAACF,KAAA;AACjD;AAEA,MAAMM,cAAA,GAAkBqB,UAAA,IAA6B;EACnD,MAAM;IAAExC,QAAA;IAAUa,KAAA;IAAOC,GAAA;IAAKC;EAAU,IAAIyB,UAAA;EAC5C,OAAO,CAACxC,QAAA,IAAY,CAAC,CAACc,GAAA,IAAO,CAAC,CAACC,SAAA,IAAa,CAAC,CAACF,KAAA;AAChD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}