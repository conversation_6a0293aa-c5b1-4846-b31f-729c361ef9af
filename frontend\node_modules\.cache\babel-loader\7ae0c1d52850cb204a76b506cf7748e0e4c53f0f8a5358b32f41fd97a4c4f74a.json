{"ast": null, "code": "// src/date.ts\nvar MILLISECONDS_IN_DAY = 864e5;\nfunction dateTo12HourTime(date) {\n  if (!date) {\n    return \"\";\n  }\n  return date.toLocaleString(\"en-US\", {\n    hour: \"2-digit\",\n    minute: \"numeric\",\n    hour12: true\n  });\n}\nfunction differenceInCalendarDays(a, b, {\n  absolute = true\n} = {}) {\n  if (!a || !b) {\n    return 0;\n  }\n  const utcA = Date.UTC(a.getFullYear(), a.getMonth(), a.getDate());\n  const utcB = Date.UTC(b.getFullYear(), b.getMonth(), b.getDate());\n  const diff = Math.floor((utcB - utcA) / MILLISECONDS_IN_DAY);\n  return absolute ? Math.abs(diff) : diff;\n}\nfunction normalizeDate(d) {\n  try {\n    return new Date(d || /* @__PURE__ */new Date());\n  } catch (e) {\n    return /* @__PURE__ */new Date();\n  }\n}\nfunction formatRelative(props) {\n  const {\n    date,\n    relativeTo\n  } = props;\n  if (!date || !relativeTo) {\n    return null;\n  }\n  const a = normalizeDate(date);\n  const b = normalizeDate(relativeTo);\n  const differenceInDays = differenceInCalendarDays(b, a, {\n    absolute: false\n  });\n  if (differenceInDays < -6) {\n    return {\n      relativeDateCase: \"other\",\n      date: a\n    };\n  }\n  if (differenceInDays < -1) {\n    return {\n      relativeDateCase: \"previous6Days\",\n      date: a\n    };\n  }\n  if (differenceInDays === -1) {\n    return {\n      relativeDateCase: \"lastDay\",\n      date: a\n    };\n  }\n  if (differenceInDays === 0) {\n    return {\n      relativeDateCase: \"sameDay\",\n      date: a\n    };\n  }\n  if (differenceInDays === 1) {\n    return {\n      relativeDateCase: \"nextDay\",\n      date: a\n    };\n  }\n  if (differenceInDays < 7) {\n    return {\n      relativeDateCase: \"next6Days\",\n      date: a\n    };\n  }\n  return {\n    relativeDateCase: \"other\",\n    date: a\n  };\n}\nfunction addYears(initialDate, yearsToAdd) {\n  const date = normalizeDate(initialDate);\n  date.setFullYear(date.getFullYear() + yearsToAdd);\n  return date;\n}\nexport { dateTo12HourTime, differenceInCalendarDays, normalizeDate, formatRelative, addYears };", "map": {"version": 3, "names": ["MILLISECONDS_IN_DAY", "dateTo12HourTime", "date", "toLocaleString", "hour", "minute", "hour12", "differenceInCalendarDays", "a", "b", "absolute", "utcA", "Date", "UTC", "getFullYear", "getMonth", "getDate", "utcB", "diff", "Math", "floor", "abs", "normalizeDate", "d", "e", "formatRelative", "props", "relativeTo", "differenceInDays", "relativeDateCase", "addYears", "initialDate", "yearsToAdd", "setFullYear"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\date.ts"], "sourcesContent": ["const MILLISECONDS_IN_DAY = 86400000;\n\nexport function dateTo12HourTime(date: Date): string {\n  if (!date) {\n    return '';\n  }\n  return date.toLocaleString('en-US', {\n    hour: '2-digit',\n    minute: 'numeric',\n    hour12: true,\n  });\n}\n\nexport function differenceInCalendarDays(a: Date, b: Date, { absolute = true } = {}): number {\n  if (!a || !b) {\n    return 0;\n  }\n  const utcA = Date.UTC(a.getFullYear(), a.getMonth(), a.getDate());\n  const utcB = Date.UTC(b.getFullYear(), b.getMonth(), b.getDate());\n  const diff = Math.floor((utcB - utcA) / MILLISECONDS_IN_DAY);\n  return absolute ? Math.abs(diff) : diff;\n}\n\nexport function normalizeDate(d: Date | string | number): Date {\n  try {\n    return new Date(d || new Date());\n  } catch (e) {\n    return new Date();\n  }\n}\n\ntype DateFormatRelativeParams = {\n  date: Date | string | number;\n  relativeTo: Date | string | number;\n};\n\nexport type RelativeDateCase = 'previous6Days' | 'lastDay' | 'sameDay' | 'nextDay' | 'next6Days' | 'other';\ntype RelativeDateReturn = { relativeDateCase: RelativeDateCase; date: Date } | null;\n\nexport function formatRelative(props: DateFormatRelativeParams): RelativeDateReturn {\n  const { date, relativeTo } = props;\n  if (!date || !relativeTo) {\n    return null;\n  }\n  const a = normalizeDate(date);\n  const b = normalizeDate(relativeTo);\n  const differenceInDays = differenceInCalendarDays(b, a, { absolute: false });\n\n  if (differenceInDays < -6) {\n    return { relativeDateCase: 'other', date: a };\n  }\n  if (differenceInDays < -1) {\n    return { relativeDateCase: 'previous6Days', date: a };\n  }\n  if (differenceInDays === -1) {\n    return { relativeDateCase: 'lastDay', date: a };\n  }\n  if (differenceInDays === 0) {\n    return { relativeDateCase: 'sameDay', date: a };\n  }\n  if (differenceInDays === 1) {\n    return { relativeDateCase: 'nextDay', date: a };\n  }\n  if (differenceInDays < 7) {\n    return { relativeDateCase: 'next6Days', date: a };\n  }\n  return { relativeDateCase: 'other', date: a };\n}\n\nexport function addYears(initialDate: Date | number | string, yearsToAdd: number): Date {\n  const date = normalizeDate(initialDate);\n  date.setFullYear(date.getFullYear() + yearsToAdd);\n  return date;\n}\n"], "mappings": ";AAAA,IAAMA,mBAAA,GAAsB;AAErB,SAASC,iBAAiBC,IAAA,EAAoB;EACnD,IAAI,CAACA,IAAA,EAAM;IACT,OAAO;EACT;EACA,OAAOA,IAAA,CAAKC,cAAA,CAAe,SAAS;IAClCC,IAAA,EAAM;IACNC,MAAA,EAAQ;IACRC,MAAA,EAAQ;EACV,CAAC;AACH;AAEO,SAASC,yBAAyBC,CAAA,EAASC,CAAA,EAAS;EAAEC,QAAA,GAAW;AAAK,IAAI,CAAC,GAAW;EAC3F,IAAI,CAACF,CAAA,IAAK,CAACC,CAAA,EAAG;IACZ,OAAO;EACT;EACA,MAAME,IAAA,GAAOC,IAAA,CAAKC,GAAA,CAAIL,CAAA,CAAEM,WAAA,CAAY,GAAGN,CAAA,CAAEO,QAAA,CAAS,GAAGP,CAAA,CAAEQ,OAAA,CAAQ,CAAC;EAChE,MAAMC,IAAA,GAAOL,IAAA,CAAKC,GAAA,CAAIJ,CAAA,CAAEK,WAAA,CAAY,GAAGL,CAAA,CAAEM,QAAA,CAAS,GAAGN,CAAA,CAAEO,OAAA,CAAQ,CAAC;EAChE,MAAME,IAAA,GAAOC,IAAA,CAAKC,KAAA,EAAOH,IAAA,GAAON,IAAA,IAAQX,mBAAmB;EAC3D,OAAOU,QAAA,GAAWS,IAAA,CAAKE,GAAA,CAAIH,IAAI,IAAIA,IAAA;AACrC;AAEO,SAASI,cAAcC,CAAA,EAAiC;EAC7D,IAAI;IACF,OAAO,IAAIX,IAAA,CAAKW,CAAA,IAAK,mBAAIX,IAAA,CAAK,CAAC;EACjC,SAASY,CAAA,EAAG;IACV,OAAO,mBAAIZ,IAAA,CAAK;EAClB;AACF;AAUO,SAASa,eAAeC,KAAA,EAAqD;EAClF,MAAM;IAAExB,IAAA;IAAMyB;EAAW,IAAID,KAAA;EAC7B,IAAI,CAACxB,IAAA,IAAQ,CAACyB,UAAA,EAAY;IACxB,OAAO;EACT;EACA,MAAMnB,CAAA,GAAIc,aAAA,CAAcpB,IAAI;EAC5B,MAAMO,CAAA,GAAIa,aAAA,CAAcK,UAAU;EAClC,MAAMC,gBAAA,GAAmBrB,wBAAA,CAAyBE,CAAA,EAAGD,CAAA,EAAG;IAAEE,QAAA,EAAU;EAAM,CAAC;EAE3E,IAAIkB,gBAAA,GAAmB,IAAI;IACzB,OAAO;MAAEC,gBAAA,EAAkB;MAAS3B,IAAA,EAAMM;IAAE;EAC9C;EACA,IAAIoB,gBAAA,GAAmB,IAAI;IACzB,OAAO;MAAEC,gBAAA,EAAkB;MAAiB3B,IAAA,EAAMM;IAAE;EACtD;EACA,IAAIoB,gBAAA,KAAqB,IAAI;IAC3B,OAAO;MAAEC,gBAAA,EAAkB;MAAW3B,IAAA,EAAMM;IAAE;EAChD;EACA,IAAIoB,gBAAA,KAAqB,GAAG;IAC1B,OAAO;MAAEC,gBAAA,EAAkB;MAAW3B,IAAA,EAAMM;IAAE;EAChD;EACA,IAAIoB,gBAAA,KAAqB,GAAG;IAC1B,OAAO;MAAEC,gBAAA,EAAkB;MAAW3B,IAAA,EAAMM;IAAE;EAChD;EACA,IAAIoB,gBAAA,GAAmB,GAAG;IACxB,OAAO;MAAEC,gBAAA,EAAkB;MAAa3B,IAAA,EAAMM;IAAE;EAClD;EACA,OAAO;IAAEqB,gBAAA,EAAkB;IAAS3B,IAAA,EAAMM;EAAE;AAC9C;AAEO,SAASsB,SAASC,WAAA,EAAqCC,UAAA,EAA0B;EACtF,MAAM9B,IAAA,GAAOoB,aAAA,CAAcS,WAAW;EACtC7B,IAAA,CAAK+B,WAAA,CAAY/B,IAAA,CAAKY,WAAA,CAAY,IAAIkB,UAAU;EAChD,OAAO9B,IAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}