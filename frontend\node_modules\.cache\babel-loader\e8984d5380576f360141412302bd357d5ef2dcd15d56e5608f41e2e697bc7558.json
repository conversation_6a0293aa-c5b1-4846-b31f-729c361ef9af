{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport { useSessionContext } from \"../contexts/SessionContext\";\nconst useSession = () => {\n  const session = useSessionContext();\n  if (session === void 0) {\n    return {\n      isLoaded: false,\n      isSignedIn: void 0,\n      session: void 0\n    };\n  }\n  if (session === null) {\n    return {\n      isLoaded: true,\n      isSignedIn: false,\n      session: null\n    };\n  }\n  return {\n    isLoaded: true,\n    isSignedIn: true,\n    session\n  };\n};\nexport { useSession };", "map": {"version": 3, "names": ["useSessionContext", "useSession", "session", "isLoaded", "isSignedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\hooks\\useSession.ts"], "sourcesContent": ["import type { ActiveSessionResource } from '@clerk/types';\n\nimport { useSessionContext } from '../contexts/SessionContext';\n\ntype UseSessionReturn =\n  | { isLoaded: false; isSignedIn: undefined; session: undefined }\n  | { isLoaded: true; isSignedIn: false; session: null }\n  | { isLoaded: true; isSignedIn: true; session: ActiveSessionResource };\n\ntype UseSession = () => UseSessionReturn;\n\n/**\n * Returns the current auth state and if a session exists, the session object.\n *\n * Until Clerk loads and initializes, `isLoaded` will be set to `false`.\n * Once Clerk loads, `isLoaded` will be set to `true`, and you can\n * safely access `isSignedIn` state and `session`.\n *\n * For projects using NextJs or Remix, you can make this state available during SSR\n * simply by using the `withServerSideAuth` helper and setting the `loadSession` flag to `true`.\n *\n * @example\n * A simple example:\n *\n * import { useSession } from '@clerk/clerk-react'\n *\n * function Hello() {\n *   const { isSignedIn, session } = useSession();\n *   if(!isSignedIn) {\n *     return null;\n *   }\n *   return <div>{session.updatedAt}</div>\n * }\n *\n * @example\n * Basic example in a NextJs app. This page will be fully rendered during SSR:\n *\n * import { useSession } from '@clerk/nextjs'\n * import { withServerSideAuth } from '@clerk/nextjs/api'\n *\n * export getServerSideProps = withServerSideAuth({ loadSession: true});\n *\n * export HelloPage = () => {\n *   const { isSignedIn, session } = useSession();\n *   if(!isSignedIn) {\n *     return null;\n *   }\n *  return <div>{session.updatedAt}</div>\n * }\n */\nexport const useSession: UseSession = () => {\n  const session = useSessionContext();\n\n  if (session === undefined) {\n    return { isLoaded: false, isSignedIn: undefined, session: undefined };\n  }\n\n  if (session === null) {\n    return { isLoaded: true, isSignedIn: false, session: null };\n  }\n\n  return { isLoaded: true, isSignedIn: true, session };\n};\n"], "mappings": ";AAEA,SAASA,iBAAA,QAAyB;AAgD3B,MAAMC,UAAA,GAAyBA,CAAA,KAAM;EAC1C,MAAMC,OAAA,GAAUF,iBAAA,CAAkB;EAElC,IAAIE,OAAA,KAAY,QAAW;IACzB,OAAO;MAAEC,QAAA,EAAU;MAAOC,UAAA,EAAY;MAAWF,OAAA,EAAS;IAAU;EACtE;EAEA,IAAIA,OAAA,KAAY,MAAM;IACpB,OAAO;MAAEC,QAAA,EAAU;MAAMC,UAAA,EAAY;MAAOF,OAAA,EAAS;IAAK;EAC5D;EAEA,OAAO;IAAEC,QAAA,EAAU;IAAMC,UAAA,EAAY;IAAMF;EAAQ;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}