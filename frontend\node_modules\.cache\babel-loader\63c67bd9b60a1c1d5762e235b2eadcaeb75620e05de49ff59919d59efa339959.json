{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport React from \"react\";\nimport { assertWrappedByClerkProvider } from \"./assertHelpers\";\nconst StructureContextStates = Object.freeze({\n  noGuarantees: Object.freeze({\n    guaranteedLoaded: false\n  }),\n  guaranteedLoaded: Object.freeze({\n    guaranteedLoaded: true\n  })\n});\nconst StructureContext = React.createContext(void 0);\nStructureContext.displayName = \"StructureContext\";\nconst useStructureContext = () => {\n  const structureCtx = React.useContext(StructureContext);\n  assertWrappedByClerkProvider(structureCtx);\n  return structureCtx;\n};\nconst LoadedGuarantee = ({\n  children\n}) => {\n  const structure = useStructureContext();\n  if (structure.guaranteedLoaded) {\n    return /* @__PURE__ */React.createElement(React.Fragment, null, children);\n  }\n  return /* @__PURE__ */React.createElement(StructureContext.Provider, {\n    value: StructureContextStates.guaranteedLoaded\n  }, children);\n};\nexport { LoadedGuarantee, StructureContext, StructureContextStates };", "map": {"version": 3, "names": ["React", "assertWrappedByClerkProvider", "StructureContextStates", "Object", "freeze", "noGuarantees", "guaranteedLoaded", "StructureContext", "createContext", "displayName", "useStructureContext", "structureCtx", "useContext", "LoadedGuarantee", "children", "structure", "createElement", "Fragment", "Provider", "value"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\contexts\\StructureContext.tsx"], "sourcesContent": ["import React from 'react';\n\nimport { assertWrappedByClerkProvider } from './assertHelpers';\n\nexport interface StructureContextValue {\n  guaranteedLoaded: boolean;\n}\n\nexport const StructureContextStates = Object.freeze({\n  noGuarantees: Object.freeze({\n    guaranteedLoaded: false,\n  }),\n  guaranteedLoaded: Object.freeze({\n    guaranteedLoaded: true,\n  }),\n});\n\nexport const StructureContext = React.createContext<StructureContextValue | undefined>(undefined);\n\nStructureContext.displayName = 'StructureContext';\n\nconst useStructureContext = (): StructureContextValue => {\n  const structureCtx = React.useContext(StructureContext);\n  assertWrappedByClerkProvider(structureCtx);\n  return structureCtx;\n};\n\nexport const LoadedGuarantee: React.FC<React.PropsWithChildren<unknown>> = ({ children }) => {\n  const structure = useStructureContext();\n  if (structure.guaranteedLoaded) {\n    return <>{children}</>;\n  }\n  return (\n    <StructureContext.Provider value={StructureContextStates.guaranteedLoaded}>{children}</StructureContext.Provider>\n  );\n};\n"], "mappings": ";AAAA,OAAOA,KAAA,MAAW;AAElB,SAASC,4BAAA,QAAoC;AAMtC,MAAMC,sBAAA,GAAyBC,MAAA,CAAOC,MAAA,CAAO;EAClDC,YAAA,EAAcF,MAAA,CAAOC,MAAA,CAAO;IAC1BE,gBAAA,EAAkB;EACpB,CAAC;EACDA,gBAAA,EAAkBH,MAAA,CAAOC,MAAA,CAAO;IAC9BE,gBAAA,EAAkB;EACpB,CAAC;AACH,CAAC;AAEM,MAAMC,gBAAA,GAAmBP,KAAA,CAAMQ,aAAA,CAAiD,MAAS;AAEhGD,gBAAA,CAAiBE,WAAA,GAAc;AAE/B,MAAMC,mBAAA,GAAsBA,CAAA,KAA6B;EACvD,MAAMC,YAAA,GAAeX,KAAA,CAAMY,UAAA,CAAWL,gBAAgB;EACtDN,4BAAA,CAA6BU,YAAY;EACzC,OAAOA,YAAA;AACT;AAEO,MAAME,eAAA,GAA8DA,CAAC;EAAEC;AAAS,MAAM;EAC3F,MAAMC,SAAA,GAAYL,mBAAA,CAAoB;EACtC,IAAIK,SAAA,CAAUT,gBAAA,EAAkB;IAC9B,OAAO,eAAAN,KAAA,CAAAgB,aAAA,CAAAhB,KAAA,CAAAiB,QAAA,QAAGH,QAAS;EACrB;EACA,OACE,eAAAd,KAAA,CAAAgB,aAAA,CAACT,gBAAA,CAAiBW,QAAA,EAAjB;IAA0BC,KAAA,EAAOjB,sBAAA,CAAuBI;EAAA,GAAmBQ,QAAS;AAEzF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}