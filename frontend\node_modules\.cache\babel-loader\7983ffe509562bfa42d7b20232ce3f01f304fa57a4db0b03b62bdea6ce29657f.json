{"ast": null, "code": "// src/localStorageBroadcastChannel.ts\nvar KEY_PREFIX = \"__lsbc__\";\nvar LocalStorageBroadcastChannel = class {\n  constructor(name) {\n    this.eventTarget = window;\n    this.postMessage = data => {\n      if (typeof window === \"undefined\") {\n        return;\n      }\n      try {\n        window.localStorage.setItem(this.channelKey, JSON.stringify(data));\n        window.localStorage.removeItem(this.channelKey);\n      } catch (e) {}\n    };\n    this.addEventListener = (eventName, listener) => {\n      this.eventTarget.addEventListener(this.prefixEventName(eventName), e => {\n        listener(e);\n      });\n    };\n    this.setupLocalStorageListener = () => {\n      const notifyListeners = e => {\n        if (e.key !== this.channelKey || !e.newValue) {\n          return;\n        }\n        try {\n          const data = JSON.parse(e.newValue || \"\");\n          const event = new MessageEvent(this.prefixEventName(\"message\"), {\n            data\n          });\n          this.eventTarget.dispatchEvent(event);\n        } catch (e2) {}\n      };\n      window.addEventListener(\"storage\", notifyListeners);\n    };\n    this.channelKey = KEY_PREFIX + name;\n    this.setupLocalStorageListener();\n  }\n  prefixEventName(eventName) {\n    return this.channelKey + eventName;\n  }\n};\nexport { LocalStorageBroadcastChannel };", "map": {"version": 3, "names": ["KEY_PREFIX", "LocalStorageBroadcastChannel", "constructor", "name", "eventTarget", "window", "postMessage", "data", "localStorage", "setItem", "channelKey", "JSON", "stringify", "removeItem", "e", "addEventListener", "eventName", "listener", "prefixEventName", "setupLocalStorageListener", "notifyListeners", "key", "newValue", "parse", "event", "MessageEvent", "dispatchEvent", "e2"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\localStorageBroadcastChannel.ts"], "sourcesContent": ["type Listener<T> = (e: MessageEvent<T>) => void;\n\nconst KEY_PREFIX = '__lsbc__';\n\nexport class LocalStorageBroadcastChannel<E> {\n  private readonly eventTarget = window;\n  private readonly channelKey: string;\n\n  constructor(name: string) {\n    this.channelKey = KEY_PREFIX + name;\n    this.setupLocalStorageListener();\n  }\n\n  public postMessage = (data: E): void => {\n    if (typeof window === 'undefined') {\n      // Silently do nothing\n      return;\n    }\n\n    try {\n      window.localStorage.setItem(this.channelKey, JSON.stringify(data));\n      window.localStorage.removeItem(this.channelKey);\n    } catch (e) {\n      // Silently do nothing\n    }\n  };\n\n  public addEventListener = (eventName: 'message', listener: Listener<E>): void => {\n    this.eventTarget.addEventListener(this.prefixEventName(eventName), e => {\n      listener(e as MessageEvent);\n    });\n  };\n\n  private setupLocalStorageListener = () => {\n    const notifyListeners = (e: StorageEvent) => {\n      if (e.key !== this.channelKey || !e.newValue) {\n        return;\n      }\n\n      try {\n        const data = JSON.parse(e.newValue || '');\n        const event = new MessageEvent(this.prefixEventName('message'), {\n          data,\n        });\n        this.eventTarget.dispatchEvent(event);\n      } catch (e) {\n        //\n      }\n    };\n\n    window.addEventListener('storage', notifyListeners);\n  };\n\n  private prefixEventName(eventName: string): string {\n    return this.channelKey + eventName;\n  }\n}\n"], "mappings": ";AAEA,IAAMA,UAAA,GAAa;AAEZ,IAAMC,4BAAA,GAAN,MAAsC;EAI3CC,YAAYC,IAAA,EAAc;IAH1B,KAAiBC,WAAA,GAAcC,MAAA;IAQ/B,KAAOC,WAAA,GAAeC,IAAA,IAAkB;MACtC,IAAI,OAAOF,MAAA,KAAW,aAAa;QAEjC;MACF;MAEA,IAAI;QACFA,MAAA,CAAOG,YAAA,CAAaC,OAAA,CAAQ,KAAKC,UAAA,EAAYC,IAAA,CAAKC,SAAA,CAAUL,IAAI,CAAC;QACjEF,MAAA,CAAOG,YAAA,CAAaK,UAAA,CAAW,KAAKH,UAAU;MAChD,SAASI,CAAA,EAAG,CAEZ;IACF;IAEA,KAAOC,gBAAA,GAAmB,CAACC,SAAA,EAAsBC,QAAA,KAAgC;MAC/E,KAAKb,WAAA,CAAYW,gBAAA,CAAiB,KAAKG,eAAA,CAAgBF,SAAS,GAAGF,CAAA,IAAK;QACtEG,QAAA,CAASH,CAAiB;MAC5B,CAAC;IACH;IAEA,KAAQK,yBAAA,GAA4B,MAAM;MACxC,MAAMC,eAAA,GAAmBN,CAAA,IAAoB;QAC3C,IAAIA,CAAA,CAAEO,GAAA,KAAQ,KAAKX,UAAA,IAAc,CAACI,CAAA,CAAEQ,QAAA,EAAU;UAC5C;QACF;QAEA,IAAI;UACF,MAAMf,IAAA,GAAOI,IAAA,CAAKY,KAAA,CAAMT,CAAA,CAAEQ,QAAA,IAAY,EAAE;UACxC,MAAME,KAAA,GAAQ,IAAIC,YAAA,CAAa,KAAKP,eAAA,CAAgB,SAAS,GAAG;YAC9DX;UACF,CAAC;UACD,KAAKH,WAAA,CAAYsB,aAAA,CAAcF,KAAK;QACtC,SAASG,EAAA,EAAG,CAEZ;MACF;MAEAtB,MAAA,CAAOU,gBAAA,CAAiB,WAAWK,eAAe;IACpD;IA1CE,KAAKV,UAAA,GAAaV,UAAA,GAAaG,IAAA;IAC/B,KAAKgB,yBAAA,CAA0B;EACjC;EA0CQD,gBAAgBF,SAAA,EAA2B;IACjD,OAAO,KAAKN,UAAA,GAAaM,SAAA;EAC3B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}