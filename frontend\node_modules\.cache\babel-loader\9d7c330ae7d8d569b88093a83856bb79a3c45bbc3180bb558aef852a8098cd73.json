{"ast": null, "code": "// src/utils/noop.ts\nvar noop = (..._args) => {};\n\n// src/workerTimers/workerTimers.worker.ts\nvar workerTimers_worker_default = 'const respond=r=>{self.postMessage(r)},workerToTabIds={};self.addEventListener(\"message\",r=>{const e=r.data;switch(e.type){case\"setTimeout\":workerToTabIds[e.id]=setTimeout(()=>{respond({id:e.id})},e.ms);break;case\"clearTimeout\":workerToTabIds[e.id]&&(clearTimeout(workerToTabIds[e.id]),delete workerToTabIds[e.id]);break;case\"setInterval\":workerToTabIds[e.id]=setInterval(()=>{respond({id:e.id})},e.ms);break;case\"clearInterval\":workerToTabIds[e.id]&&(clearInterval(workerToTabIds[e.id]),delete workerToTabIds[e.id]);break}});\\n';\n\n// src/workerTimers/createWorkerTimers.ts\nvar createWebWorker = (source, opts = {}) => {\n  if (typeof Worker === \"undefined\") {\n    return null;\n  }\n  try {\n    const blob = new Blob([source], {\n      type: \"application/javascript; charset=utf-8\"\n    });\n    const workerScript = globalThis.URL.createObjectURL(blob);\n    return new Worker(workerScript, opts);\n  } catch (e) {\n    console.warn(\"Clerk: Cannot create worker from blob. Consider adding worker-src blob:; to your CSP\");\n    return null;\n  }\n};\nvar fallbackTimers = () => {\n  const setTimeout = globalThis.setTimeout.bind(globalThis);\n  const setInterval = globalThis.setInterval.bind(globalThis);\n  const clearTimeout = globalThis.clearTimeout.bind(globalThis);\n  const clearInterval = globalThis.clearInterval.bind(globalThis);\n  return {\n    setTimeout,\n    setInterval,\n    clearTimeout,\n    clearInterval,\n    cleanup: noop\n  };\n};\nvar createWorkerTimers = () => {\n  let id = 0;\n  const generateId = () => id++;\n  const callbacks = /* @__PURE__ */new Map();\n  const post = (w, p) => w == null ? void 0 : w.postMessage(p);\n  const handleMessage = e => {\n    var _a;\n    (_a = callbacks.get(e.data.id)) == null ? void 0 : _a();\n  };\n  let worker = createWebWorker(workerTimers_worker_default, {\n    name: \"clerk-timers\"\n  });\n  worker == null ? void 0 : worker.addEventListener(\"message\", handleMessage);\n  if (!worker) {\n    return fallbackTimers();\n  }\n  const init = () => {\n    if (!worker) {\n      worker = createWebWorker(workerTimers_worker_default, {\n        name: \"clerk-timers\"\n      });\n      worker == null ? void 0 : worker.addEventListener(\"message\", handleMessage);\n    }\n  };\n  const cleanup = () => {\n    if (worker) {\n      worker.terminate();\n      worker = null;\n      callbacks.clear();\n    }\n  };\n  const setTimeout = (cb, ms) => {\n    init();\n    const id2 = generateId();\n    callbacks.set(id2, cb);\n    post(worker, {\n      type: \"setTimeout\",\n      id: id2,\n      ms\n    });\n    return id2;\n  };\n  const setInterval = (cb, ms) => {\n    init();\n    const id2 = generateId();\n    callbacks.set(id2, cb);\n    post(worker, {\n      type: \"setInterval\",\n      id: id2,\n      ms\n    });\n    return id2;\n  };\n  const clearTimeout = id2 => {\n    init();\n    callbacks.delete(id2);\n    post(worker, {\n      type: \"clearTimeout\",\n      id: id2\n    });\n  };\n  const clearInterval = id2 => {\n    init();\n    callbacks.delete(id2);\n    post(worker, {\n      type: \"clearInterval\",\n      id: id2\n    });\n  };\n  return {\n    setTimeout,\n    setInterval,\n    clearTimeout,\n    clearInterval,\n    cleanup\n  };\n};\n\n// src/poller.ts\nfunction Poller({\n  delayInMs\n} = {\n  delayInMs: 1e3\n}) {\n  const workerTimers = createWorkerTimers();\n  let timerId;\n  let stopped = false;\n  const stop = () => {\n    if (timerId) {\n      workerTimers.clearTimeout(timerId);\n      workerTimers.cleanup();\n    }\n    stopped = true;\n  };\n  const run = async cb => {\n    stopped = false;\n    await cb(stop);\n    if (stopped) {\n      return;\n    }\n    timerId = workerTimers.setTimeout(() => {\n      void run(cb);\n    }, delayInMs);\n  };\n  return {\n    run,\n    stop\n  };\n}\nexport { noop, createWorkerTimers, Poller };", "map": {"version": 3, "names": ["noop", "_args", "workerTimers_worker_default", "createWebWorker", "source", "opts", "Worker", "blob", "Blob", "type", "workerScript", "globalThis", "URL", "createObjectURL", "e", "console", "warn", "fallbackTimers", "setTimeout", "bind", "setInterval", "clearTimeout", "clearInterval", "cleanup", "createWorkerTimers", "id", "generateId", "callbacks", "Map", "post", "w", "p", "postMessage", "handleMessage", "_a", "get", "data", "worker", "name", "addEventListener", "init", "terminate", "clear", "cb", "ms", "id2", "set", "delete", "Poller", "delayInMs", "workerTimers", "timerId", "stopped", "stop", "run"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\utils\\noop.ts", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\workerTimers\\workerTimers.worker.ts", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\workerTimers\\createWorkerTimers.ts", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\poller.ts"], "sourcesContent": ["// eslint-disable-next-line @typescript-eslint/no-empty-function\nexport const noop = (..._args: any[]): void => {};\n", "const respond=r=>{self.postMessage(r)},workerToTabIds={};self.addEventListener(\"message\",r=>{const e=r.data;switch(e.type){case\"setTimeout\":workerToTabIds[e.id]=setTimeout(()=>{respond({id:e.id})},e.ms);break;case\"clearTimeout\":workerToTabIds[e.id]&&(clearTimeout(workerToTabIds[e.id]),delete workerToTabIds[e.id]);break;case\"setInterval\":workerToTabIds[e.id]=setInterval(()=>{respond({id:e.id})},e.ms);break;case\"clearInterval\":workerToTabIds[e.id]&&(clearInterval(workerToTabIds[e.id]),delete workerToTabIds[e.id]);break}});\n", "import { noop } from '../utils/noop';\nimport type {\n  WorkerClearTimeout,\n  WorkerSetTimeout,\n  WorkerTimeoutCallback,\n  WorkerTimerEvent,\n  WorkerTimerId,\n  WorkerTimerResponseEvent,\n} from './workerTimers.types';\n// @ts-ignore\nimport pollerWorkerSource from './workerTimers.worker';\n\nconst createWebWorker = (source: string, opts: ConstructorParameters<typeof Worker>[1] = {}): Worker | null => {\n  if (typeof Worker === 'undefined') {\n    return null;\n  }\n\n  try {\n    const blob = new Blob([source], { type: 'application/javascript; charset=utf-8' });\n    const workerScript = globalThis.URL.createObjectURL(blob);\n    return new Worker(workerScript, opts);\n  } catch (e) {\n    console.warn('Clerk: Cannot create worker from blob. Consider adding worker-src blob:; to your CSP');\n    return null;\n  }\n};\n\nconst fallbackTimers = () => {\n  const setTimeout = globalThis.setTimeout.bind(globalThis) as WorkerSetTimeout;\n  const setInterval = globalThis.setInterval.bind(globalThis) as WorkerSetTimeout;\n  const clearTimeout = globalThis.clearTimeout.bind(globalThis) as WorkerClearTimeout;\n  const clearInterval = globalThis.clearInterval.bind(globalThis) as WorkerClearTimeout;\n  return { setTimeout, setInterval, clearTimeout, clearInterval, cleanup: noop };\n};\n\nexport const createWorkerTimers = () => {\n  let id = 0;\n  const generateId = () => id++;\n  const callbacks = new Map<WorkerTimerId, WorkerTimeoutCallback>();\n  const post = (w: Worker | null, p: WorkerTimerEvent) => w?.postMessage(p);\n  const handleMessage = (e: MessageEvent<WorkerTimerResponseEvent>) => {\n    callbacks.get(e.data.id)?.();\n  };\n\n  let worker = createWebWorker(pollerWorkerSource, { name: 'clerk-timers' });\n  worker?.addEventListener('message', handleMessage);\n\n  if (!worker) {\n    return fallbackTimers();\n  }\n\n  const init = () => {\n    if (!worker) {\n      worker = createWebWorker(pollerWorkerSource, { name: 'clerk-timers' });\n      worker?.addEventListener('message', handleMessage);\n    }\n  };\n\n  const cleanup = () => {\n    if (worker) {\n      worker.terminate();\n      worker = null;\n      callbacks.clear();\n    }\n  };\n\n  const setTimeout: WorkerSetTimeout = (cb, ms) => {\n    init();\n    const id = generateId();\n    callbacks.set(id, cb);\n    post(worker, { type: 'setTimeout', id, ms });\n    return id;\n  };\n\n  const setInterval: WorkerSetTimeout = (cb, ms) => {\n    init();\n    const id = generateId();\n    callbacks.set(id, cb);\n    post(worker, { type: 'setInterval', id, ms });\n    return id;\n  };\n\n  const clearTimeout: WorkerClearTimeout = id => {\n    init();\n    callbacks.delete(id);\n    post(worker, { type: 'clearTimeout', id });\n  };\n\n  const clearInterval: WorkerClearTimeout = id => {\n    init();\n    callbacks.delete(id);\n    post(worker, { type: 'clearInterval', id });\n  };\n\n  return { setTimeout, setInterval, clearTimeout, clearInterval, cleanup };\n};\n", "import { createWorkerTimers } from './workerTimers';\n\nexport type PollerStop = () => void;\nexport type PollerCallback = (stop: PollerStop) => Promise<unknown>;\nexport type PollerRun = (cb: PollerCallback) => Promise<void>;\n\ntype PollerOptions = {\n  delayInMs: number;\n};\n\nexport type Poller = {\n  run: PollerRun;\n  stop: PollerStop;\n};\n\nexport function Poller({ delayInMs }: PollerOptions = { delayInMs: 1000 }): Poller {\n  const workerTimers = createWorkerTimers();\n\n  let timerId: number | undefined;\n  let stopped = false;\n\n  const stop: PollerStop = () => {\n    if (timerId) {\n      workerTimers.clearTimeout(timerId);\n      workerTimers.cleanup();\n    }\n    stopped = true;\n  };\n\n  const run: PollerRun = async cb => {\n    stopped = false;\n    await cb(stop);\n    if (stopped) {\n      return;\n    }\n\n    timerId = workerTimers.setTimeout(() => {\n      void run(cb);\n    }, delayInMs) as any as number;\n  };\n\n  return { run, stop };\n}\n"], "mappings": ";AACO,IAAMA,IAAA,GAAOA,CAAA,GAAIC,KAAA,KAAuB,CAAC;;;ACDhD,IAAAC,2BAAA;;;ACYA,IAAMC,eAAA,GAAkBA,CAACC,MAAA,EAAgBC,IAAA,GAAgD,CAAC,MAAqB;EAC7G,IAAI,OAAOC,MAAA,KAAW,aAAa;IACjC,OAAO;EACT;EAEA,IAAI;IACF,MAAMC,IAAA,GAAO,IAAIC,IAAA,CAAK,CAACJ,MAAM,GAAG;MAAEK,IAAA,EAAM;IAAwC,CAAC;IACjF,MAAMC,YAAA,GAAeC,UAAA,CAAWC,GAAA,CAAIC,eAAA,CAAgBN,IAAI;IACxD,OAAO,IAAID,MAAA,CAAOI,YAAA,EAAcL,IAAI;EACtC,SAASS,CAAA,EAAG;IACVC,OAAA,CAAQC,IAAA,CAAK,sFAAsF;IACnG,OAAO;EACT;AACF;AAEA,IAAMC,cAAA,GAAiBA,CAAA,KAAM;EAC3B,MAAMC,UAAA,GAAaP,UAAA,CAAWO,UAAA,CAAWC,IAAA,CAAKR,UAAU;EACxD,MAAMS,WAAA,GAAcT,UAAA,CAAWS,WAAA,CAAYD,IAAA,CAAKR,UAAU;EAC1D,MAAMU,YAAA,GAAeV,UAAA,CAAWU,YAAA,CAAaF,IAAA,CAAKR,UAAU;EAC5D,MAAMW,aAAA,GAAgBX,UAAA,CAAWW,aAAA,CAAcH,IAAA,CAAKR,UAAU;EAC9D,OAAO;IAAEO,UAAA;IAAYE,WAAA;IAAaC,YAAA;IAAcC,aAAA;IAAeC,OAAA,EAASvB;EAAK;AAC/E;AAEO,IAAMwB,kBAAA,GAAqBA,CAAA,KAAM;EACtC,IAAIC,EAAA,GAAK;EACT,MAAMC,UAAA,GAAaA,CAAA,KAAMD,EAAA;EACzB,MAAME,SAAA,GAAY,mBAAIC,GAAA,CAA0C;EAChE,MAAMC,IAAA,GAAOA,CAACC,CAAA,EAAkBC,CAAA,KAAwBD,CAAA,oBAAAA,CAAA,CAAGE,WAAA,CAAYD,CAAA;EACvE,MAAME,aAAA,GAAiBnB,CAAA,IAA8C;IAxCvE,IAAAoB,EAAA;IAyCI,CAAAA,EAAA,GAAAP,SAAA,CAAUQ,GAAA,CAAIrB,CAAA,CAAEsB,IAAA,CAAKX,EAAE,MAAvB,gBAAAS,EAAA;EACF;EAEA,IAAIG,MAAA,GAASlC,eAAA,CAAgBD,2BAAA,EAAoB;IAAEoC,IAAA,EAAM;EAAe,CAAC;EACzED,MAAA,oBAAAA,MAAA,CAAQE,gBAAA,CAAiB,WAAWN,aAAA;EAEpC,IAAI,CAACI,MAAA,EAAQ;IACX,OAAOpB,cAAA,CAAe;EACxB;EAEA,MAAMuB,IAAA,GAAOA,CAAA,KAAM;IACjB,IAAI,CAACH,MAAA,EAAQ;MACXA,MAAA,GAASlC,eAAA,CAAgBD,2BAAA,EAAoB;QAAEoC,IAAA,EAAM;MAAe,CAAC;MACrED,MAAA,oBAAAA,MAAA,CAAQE,gBAAA,CAAiB,WAAWN,aAAA;IACtC;EACF;EAEA,MAAMV,OAAA,GAAUA,CAAA,KAAM;IACpB,IAAIc,MAAA,EAAQ;MACVA,MAAA,CAAOI,SAAA,CAAU;MACjBJ,MAAA,GAAS;MACTV,SAAA,CAAUe,KAAA,CAAM;IAClB;EACF;EAEA,MAAMxB,UAAA,GAA+BA,CAACyB,EAAA,EAAIC,EAAA,KAAO;IAC/CJ,IAAA,CAAK;IACL,MAAMK,GAAA,GAAKnB,UAAA,CAAW;IACtBC,SAAA,CAAUmB,GAAA,CAAID,GAAA,EAAIF,EAAE;IACpBd,IAAA,CAAKQ,MAAA,EAAQ;MAAE5B,IAAA,EAAM;MAAcgB,EAAA,EAAAoB,GAAA;MAAID;IAAG,CAAC;IAC3C,OAAOC,GAAA;EACT;EAEA,MAAMzB,WAAA,GAAgCA,CAACuB,EAAA,EAAIC,EAAA,KAAO;IAChDJ,IAAA,CAAK;IACL,MAAMK,GAAA,GAAKnB,UAAA,CAAW;IACtBC,SAAA,CAAUmB,GAAA,CAAID,GAAA,EAAIF,EAAE;IACpBd,IAAA,CAAKQ,MAAA,EAAQ;MAAE5B,IAAA,EAAM;MAAegB,EAAA,EAAAoB,GAAA;MAAID;IAAG,CAAC;IAC5C,OAAOC,GAAA;EACT;EAEA,MAAMxB,YAAA,GAAmCwB,GAAA,IAAM;IAC7CL,IAAA,CAAK;IACLb,SAAA,CAAUoB,MAAA,CAAOF,GAAE;IACnBhB,IAAA,CAAKQ,MAAA,EAAQ;MAAE5B,IAAA,EAAM;MAAgBgB,EAAA,EAAAoB;IAAG,CAAC;EAC3C;EAEA,MAAMvB,aAAA,GAAoCuB,GAAA,IAAM;IAC9CL,IAAA,CAAK;IACLb,SAAA,CAAUoB,MAAA,CAAOF,GAAE;IACnBhB,IAAA,CAAKQ,MAAA,EAAQ;MAAE5B,IAAA,EAAM;MAAiBgB,EAAA,EAAAoB;IAAG,CAAC;EAC5C;EAEA,OAAO;IAAE3B,UAAA;IAAYE,WAAA;IAAaC,YAAA;IAAcC,aAAA;IAAeC;EAAQ;AACzE;;;AChFO,SAASyB,OAAO;EAAEC;AAAU,IAAmB;EAAEA,SAAA,EAAW;AAAK,GAAW;EACjF,MAAMC,YAAA,GAAe1B,kBAAA,CAAmB;EAExC,IAAI2B,OAAA;EACJ,IAAIC,OAAA,GAAU;EAEd,MAAMC,IAAA,GAAmBA,CAAA,KAAM;IAC7B,IAAIF,OAAA,EAAS;MACXD,YAAA,CAAa7B,YAAA,CAAa8B,OAAO;MACjCD,YAAA,CAAa3B,OAAA,CAAQ;IACvB;IACA6B,OAAA,GAAU;EACZ;EAEA,MAAME,GAAA,GAAiB,MAAMX,EAAA,IAAM;IACjCS,OAAA,GAAU;IACV,MAAMT,EAAA,CAAGU,IAAI;IACb,IAAID,OAAA,EAAS;MACX;IACF;IAEAD,OAAA,GAAUD,YAAA,CAAahC,UAAA,CAAW,MAAM;MACtC,KAAKoC,GAAA,CAAIX,EAAE;IACb,GAAGM,SAAS;EACd;EAEA,OAAO;IAAEK,GAAA;IAAKD;EAAK;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}