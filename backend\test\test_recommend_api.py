#!/usr/bin/env python3
"""
测试推荐 API 的脚本
"""

import asyncio
import httpx
import json
import sys
import os
from dotenv import load_dotenv

load_dotenv()

# API 配置
API_BASE_URL = "http://localhost:8000"
RECOMMEND_ENDPOINT = f"{API_BASE_URL}/api/recommend/"

async def test_recommend_api():
    """测试推荐 API"""
    print("🚀 开始测试推荐 API...")
    
    # 测试用例
    test_cases = [
        {
            "name": "开心情绪推荐",
            "data": {
                "user_id": "test_user_001",
                "extracted_elements": {
                    "mood": "happy",
                    "emotion_intensity": "high",
                    "colors": ["yellow", "orange"],
                    "themes": ["nature"],
                    "styles": ["印象派"],
                    "is_recommendation_query": True,
                    "needs_guidance": False,
                    "is_malicious": False
                },
                "user_profile": {
                    "user_id": "test_user_001",
                    "preferences": {
                        "colors": ["blue", "green"],
                        "themes": ["nature", "landscape"],
                        "styles": ["印象派", "后印象派"]
                    },
                    "mood": "happy",
                    "last_updated": "2024-01-01T12:00:00"
                }
            }
        },
        {
            "name": "悲伤情绪推荐",
            "data": {
                "user_id": "test_user_002",
                "extracted_elements": {
                    "mood": "sad",
                    "emotion_intensity": "medium",
                    "colors": ["blue"],
                    "themes": ["abstract"],
                    "styles": [],
                    "is_recommendation_query": True,
                    "needs_guidance": False,
                    "is_malicious": False
                },
                "user_profile": {
                    "user_id": "test_user_002",
                    "preferences": {},
                    "mood": "sad",
                    "last_updated": "2024-01-01T12:00:00"
                }
            }
        },
        {
            "name": "平静情绪推荐",
            "data": {
                "user_id": "test_user_003",
                "extracted_elements": {
                    "mood": "calm",
                    "emotion_intensity": "medium",
                    "colors": ["blue", "green"],
                    "themes": ["nature"],
                    "styles": ["印象派"],
                    "is_recommendation_query": True,
                    "needs_guidance": False,
                    "is_malicious": False
                },
                "user_profile": {
                    "user_id": "test_user_003",
                    "preferences": {
                        "colors": ["blue", "green", "purple"],
                        "themes": ["nature", "landscape"],
                        "styles": ["印象派"]
                    },
                    "mood": "calm",
                    "last_updated": "2024-01-01T12:00:00"
                }
            }
        },
        {
            "name": "无情绪默认推荐",
            "data": {
                "user_id": "test_user_004",
                "extracted_elements": {
                    "mood": None,
                    "emotion_intensity": "low",
                    "colors": [],
                    "themes": [],
                    "styles": [],
                    "is_recommendation_query": True,
                    "needs_guidance": False,
                    "is_malicious": False
                },
                "user_profile": {
                    "user_id": "test_user_004",
                    "preferences": {},
                    "mood": None,
                    "last_updated": None
                }
            }
        },
        {
            "name": "愤怒情绪推荐",
            "data": {
                "user_id": "test_user_005",
                "extracted_elements": {
                    "mood": "angry",
                    "emotion_intensity": "high",
                    "colors": ["red"],
                    "themes": ["portrait"],
                    "styles": ["表现主义"],
                    "is_recommendation_query": True,
                    "needs_guidance": False,
                    "is_malicious": False
                },
                "user_profile": {
                    "user_id": "test_user_005",
                    "preferences": {
                        "colors": ["red", "orange"],
                        "themes": ["portrait"],
                        "styles": ["表现主义", "浪漫主义"]
                    },
                    "mood": "angry",
                    "last_updated": "2024-01-01T12:00:00"
                }
            }
        }
    ]
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📝 测试用例 {i}: {test_case['name']}")
            print(f"   用户情绪: {test_case['data']['extracted_elements']['mood']}")
            print(f"   偏好颜色: {test_case['data']['extracted_elements']['colors']}")
            print(f"   偏好主题: {test_case['data']['extracted_elements']['themes']}")
            print(f"   偏好风格: {test_case['data']['extracted_elements']['styles']}")
            
            try:
                # 发送请求
                response = await client.post(
                    RECOMMEND_ENDPOINT,
                    json=test_case['data'],
                    headers={"Content-Type": "application/json"}
                )
                
                # 检查状态码
                if response.status_code == 200:
                    print("✅ API 调用成功")
                    
                    # 解析响应
                    recommendations = response.json()
                    
                    # 验证响应结构
                    if isinstance(recommendations, list):
                        print(f"✅ 返回推荐数量: {len(recommendations)}")
                        
                        # 显示推荐结果
                        for j, artwork in enumerate(recommendations, 1):
                            required_fields = ["id", "title", "artist", "style", "colors", "theme", "description"]
                            missing_fields = [field for field in required_fields if field not in artwork]
                            
                            if missing_fields:
                                print(f"❌ 艺术品 {j} 缺少字段: {missing_fields}")
                            else:
                                print(f"   推荐 {j}: {artwork['title']} - {artwork['artist']}")
                                print(f"      风格: {artwork['style']}")
                                print(f"      颜色: {artwork['colors']}")
                                print(f"      主题: {artwork['theme']}")
                                print(f"      描述: {artwork['description'][:50]}...")
                        
                        # 验证推荐质量
                        mood = test_case['data']['extracted_elements']['mood']
                        colors = test_case['data']['extracted_elements']['colors']
                        themes = test_case['data']['extracted_elements']['themes']
                        
                        if recommendations:
                            print(f"\n   📊 推荐质量分析:")
                            
                            # 检查情绪匹配
                            if mood:
                                mood_matched = any(
                                    mood in ["happy", "calm"] and "向日葵" in artwork['title'] or
                                    mood in ["happy", "calm"] and "睡莲" in artwork['title'] or
                                    mood == "sad" and "星夜" in artwork['title'] or
                                    mood == "angry" and "呐喊" in artwork['title']
                                    for artwork in recommendations
                                )
                                if mood_matched:
                                    print(f"      ✅ 情绪匹配良好 ({mood})")
                                else:
                                    print(f"      ⚠️  情绪匹配可能不够精确 ({mood})")
                            
                            # 检查颜色匹配
                            if colors:
                                color_matched = any(
                                    any(color in artwork['colors'] for color in colors)
                                    for artwork in recommendations
                                )
                                if color_matched:
                                    print(f"      ✅ 颜色偏好匹配")
                                else:
                                    print(f"      ⚠️  颜色偏好匹配较少")
                            
                            # 检查主题匹配
                            if themes:
                                theme_matched = any(
                                    artwork['theme'] in themes
                                    for artwork in recommendations
                                )
                                if theme_matched:
                                    print(f"      ✅ 主题偏好匹配")
                                else:
                                    print(f"      ⚠️  主题偏好匹配较少")
                        
                    else:
                        print("❌ 响应格式错误: 期望数组格式")
                    
                    # 显示完整 JSON（可选）
                    print(f"\n📄 完整推荐 JSON:")
                    print(json.dumps(recommendations, ensure_ascii=False, indent=2))
                    
                else:
                    print(f"❌ API 调用失败: HTTP {response.status_code}")
                    print(f"   错误信息: {response.text}")
                    
            except httpx.ConnectError:
                print("❌ 连接失败: 请确保后端服务正在运行 (http://localhost:8000)")
                return False
            except httpx.TimeoutException:
                print("❌ 请求超时")
                return False
            except Exception as e:
                print(f"❌ 测试失败: {e}")
                return False
    
    print("\n🎉 推荐 API 测试完成!")
    return True

async def test_server_health():
    """测试服务器健康状态"""
    print("🔍 检查服务器状态...")
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(f"{API_BASE_URL}/")
            
            if response.status_code == 200:
                print("✅ 服务器运行正常")
                return True
            else:
                print(f"❌ 服务器响应异常: HTTP {response.status_code}")
                return False
                
    except httpx.ConnectError:
        print("❌ 无法连接到服务器")
        print("   请确保后端服务正在运行:")
        print("   cd backend && python main.py")
        return False
    except Exception as e:
        print(f"❌ 健康检查失败: {e}")
        return False

async def main():
    """主函数"""
    print("=" * 60)
    print("推荐 API 测试脚本")
    print("=" * 60)
    
    # 检查服务器状态
    server_ok = await test_server_health()
    if not server_ok:
        print("\n❌ 服务器不可用，无法进行 API 测试")
        return
    
    print("\n" + "=" * 60)
    
    # 运行 API 测试
    success = await test_recommend_api()
    
    if success:
        print("\n🎉 所有测试通过!")
    else:
        print("\n❌ 部分测试失败!")

if __name__ == "__main__":
    asyncio.run(main())
