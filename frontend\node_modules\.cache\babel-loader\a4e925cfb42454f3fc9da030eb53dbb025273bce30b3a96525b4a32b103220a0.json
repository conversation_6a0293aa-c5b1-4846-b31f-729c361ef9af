{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport { ClerkInstanceContext, useClerkInstanceContext } from \"@clerk/shared/react\";\nconst [IsomorphicClerkContext, useIsomorphicClerkContext] = [ClerkInstanceContext, useClerkInstanceContext];\nexport { IsomorphicClerkContext, useIsomorphicClerkContext };", "map": {"version": 3, "names": ["ClerkInstanceContext", "useClerkInstanceContext", "IsomorphicClerkContext", "useIsomorphicClerkContext"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\contexts\\IsomorphicClerkContext.tsx"], "sourcesContent": ["import { ClerkInstanceContext, useClerkInstanceContext } from '@clerk/shared/react';\n\nexport const [IsomorphicClerkContext, useIsomorphicClerkContext] = [ClerkInstanceContext, useClerkInstanceContext];\n"], "mappings": ";AAAA,SAASA,oBAAA,EAAsBC,uBAAA,QAA+B;AAEvD,MAAM,CAACC,sBAAA,EAAwBC,yBAAyB,IAAI,CAACH,oBAAA,EAAsBC,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}