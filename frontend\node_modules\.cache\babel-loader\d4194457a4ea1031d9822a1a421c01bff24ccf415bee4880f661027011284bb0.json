{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nfunction isConstructor(f) {\n  return typeof f === \"function\";\n}\nexport { isConstructor };", "map": {"version": 3, "names": ["isConstructor", "f"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\utils\\isConstructor.ts"], "sourcesContent": ["export function isConstructor<T>(f: any): f is T {\n  return typeof f === 'function';\n}\n"], "mappings": ";AAAO,SAASA,cAAiBC,CAAA,EAAgB;EAC/C,OAAO,OAAOA,CAAA,KAAM;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}