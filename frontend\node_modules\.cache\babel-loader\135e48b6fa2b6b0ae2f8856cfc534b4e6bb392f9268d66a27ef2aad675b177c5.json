{"ast": null, "code": "// src/color.ts\nvar IS_HEX_COLOR_REGEX = /^#?([A-F0-9]{6}|[A-F0-9]{3})$/i;\nvar IS_RGB_COLOR_REGEX = /^rgb\\((\\d+),\\s*(\\d+),\\s*(\\d+)\\)$/i;\nvar IS_RGBA_COLOR_REGEX = /^rgba\\((\\d+),\\s*(\\d+),\\s*(\\d+)(,\\s*\\d+(\\.\\d+)?)\\)$/i;\nvar IS_HSL_COLOR_REGEX = /^hsl\\((\\d+),\\s*([\\d.]+)%,\\s*([\\d.]+)%\\)$/i;\nvar IS_HSLA_COLOR_REGEX = /^hsla\\((\\d+),\\s*([\\d.]+)%,\\s*([\\d.]+)%(,\\s*\\d+(\\.\\d+)?)*\\)$/i;\nvar isValidHexString = s => {\n  return !!s.match(IS_HEX_COLOR_REGEX);\n};\nvar isValidRgbaString = s => {\n  return !!(s.match(IS_RGB_COLOR_REGEX) || s.match(IS_RGBA_COLOR_REGEX));\n};\nvar isValidHslaString = s => {\n  return !!s.match(IS_HSL_COLOR_REGEX) || !!s.match(IS_HSLA_COLOR_REGEX);\n};\nvar isRGBColor = c => {\n  return typeof c !== \"string\" && \"r\" in c;\n};\nvar isHSLColor = c => {\n  return typeof c !== \"string\" && \"h\" in c;\n};\nvar isTransparent = c => {\n  return c === \"transparent\";\n};\nvar hasAlpha = color => {\n  return typeof color !== \"string\" && color.a != void 0 && color.a < 1;\n};\nvar CLEAN_HSLA_REGEX = /[hsla()]/g;\nvar CLEAN_RGBA_REGEX = /[rgba()]/g;\nvar stringToHslaColor = value => {\n  if (value === \"transparent\") {\n    return {\n      h: 0,\n      s: 0,\n      l: 0,\n      a: 0\n    };\n  }\n  if (isValidHexString(value)) {\n    return hexStringToHslaColor(value);\n  }\n  if (isValidHslaString(value)) {\n    return parseHslaString(value);\n  }\n  if (isValidRgbaString(value)) {\n    return rgbaStringToHslaColor(value);\n  }\n  return null;\n};\nvar stringToSameTypeColor = value => {\n  value = value.trim();\n  if (isValidHexString(value)) {\n    return value.startsWith(\"#\") ? value : `#${value}`;\n  }\n  if (isValidRgbaString(value)) {\n    return parseRgbaString(value);\n  }\n  if (isValidHslaString(value)) {\n    return parseHslaString(value);\n  }\n  if (isTransparent(value)) {\n    return value;\n  }\n  return \"\";\n};\nvar colorToSameTypeString = color => {\n  if (typeof color === \"string\" && (isValidHexString(color) || isTransparent(color))) {\n    return color;\n  }\n  if (isRGBColor(color)) {\n    return rgbaColorToRgbaString(color);\n  }\n  if (isHSLColor(color)) {\n    return hslaColorToHslaString(color);\n  }\n  return \"\";\n};\nvar hexStringToRgbaColor = hex => {\n  hex = hex.replace(\"#\", \"\");\n  const r = parseInt(hex.substring(0, 2), 16);\n  const g = parseInt(hex.substring(2, 4), 16);\n  const b = parseInt(hex.substring(4, 6), 16);\n  return {\n    r,\n    g,\n    b\n  };\n};\nvar rgbaColorToRgbaString = color => {\n  const {\n    a,\n    b,\n    g,\n    r\n  } = color;\n  return color.a === 0 ? \"transparent\" : color.a != void 0 ? `rgba(${r},${g},${b},${a})` : `rgb(${r},${g},${b})`;\n};\nvar hslaColorToHslaString = color => {\n  const {\n    h,\n    s,\n    l,\n    a\n  } = color;\n  const sPerc = Math.round(s * 100);\n  const lPerc = Math.round(l * 100);\n  return color.a === 0 ? \"transparent\" : color.a != void 0 ? `hsla(${h},${sPerc}%,${lPerc}%,${a})` : `hsl(${h},${sPerc}%,${lPerc}%)`;\n};\nvar hexStringToHslaColor = hex => {\n  const rgbaString = colorToSameTypeString(hexStringToRgbaColor(hex));\n  return rgbaStringToHslaColor(rgbaString);\n};\nvar rgbaStringToHslaColor = rgba => {\n  const rgbaColor = parseRgbaString(rgba);\n  const r = rgbaColor.r / 255;\n  const g = rgbaColor.g / 255;\n  const b = rgbaColor.b / 255;\n  const max = Math.max(r, g, b),\n    min = Math.min(r, g, b);\n  let h, s;\n  const l = (max + min) / 2;\n  if (max == min) {\n    h = s = 0;\n  } else {\n    const d = max - min;\n    s = l >= 0.5 ? d / (2 - (max + min)) : d / (max + min);\n    switch (max) {\n      case r:\n        h = (g - b) / d * 60;\n        break;\n      case g:\n        h = ((b - r) / d + 2) * 60;\n        break;\n      default:\n        h = ((r - g) / d + 4) * 60;\n        break;\n    }\n  }\n  const res = {\n    h: Math.round(h),\n    s,\n    l\n  };\n  const a = rgbaColor.a;\n  if (a != void 0) {\n    res.a = a;\n  }\n  return res;\n};\nvar parseRgbaString = str => {\n  const [r, g, b, a] = str.replace(CLEAN_RGBA_REGEX, \"\").split(\",\").map(c => Number.parseFloat(c));\n  return {\n    r,\n    g,\n    b,\n    a\n  };\n};\nvar parseHslaString = str => {\n  const [h, s, l, a] = str.replace(CLEAN_HSLA_REGEX, \"\").split(\",\").map(c => Number.parseFloat(c));\n  return {\n    h,\n    s: s / 100,\n    l: l / 100,\n    a\n  };\n};\nexport { isValidHexString, isValidRgbaString, isValidHslaString, isRGBColor, isHSLColor, isTransparent, hasAlpha, stringToHslaColor, stringToSameTypeColor, colorToSameTypeString, hexStringToRgbaColor };", "map": {"version": 3, "names": ["IS_HEX_COLOR_REGEX", "IS_RGB_COLOR_REGEX", "IS_RGBA_COLOR_REGEX", "IS_HSL_COLOR_REGEX", "IS_HSLA_COLOR_REGEX", "isValidHexString", "s", "match", "isValidRgbaString", "isValidHslaString", "isRGBColor", "c", "isHSLColor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "has<PERSON><PERSON><PERSON>", "color", "a", "CLEAN_HSLA_REGEX", "CLEAN_RGBA_REGEX", "stringToHslaColor", "value", "h", "l", "hexStringToHslaColor", "parseHslaString", "rgbaStringToHslaColor", "stringToSameTypeColor", "trim", "startsWith", "parseRgbaString", "colorToSameTypeString", "rgbaColorToRgbaString", "hslaColorToHslaString", "hexStringToRgbaColor", "hex", "replace", "r", "parseInt", "substring", "g", "b", "sPerc", "Math", "round", "lPerc", "rgbaString", "rgba", "rgbaColor", "max", "min", "d", "res", "str", "split", "map", "Number", "parseFloat"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\color.ts"], "sourcesContent": ["import type { Color, HslaColor, RgbaColor, TransparentColor } from '@clerk/types';\n\nconst IS_HEX_COLOR_REGEX = /^#?([A-F0-9]{6}|[A-F0-9]{3})$/i;\n\nconst IS_RGB_COLOR_REGEX = /^rgb\\((\\d+),\\s*(\\d+),\\s*(\\d+)\\)$/i;\nconst IS_RGBA_COLOR_REGEX = /^rgba\\((\\d+),\\s*(\\d+),\\s*(\\d+)(,\\s*\\d+(\\.\\d+)?)\\)$/i;\n\nconst IS_HSL_COLOR_REGEX = /^hsl\\((\\d+),\\s*([\\d.]+)%,\\s*([\\d.]+)%\\)$/i;\nconst IS_HSLA_COLOR_REGEX = /^hsla\\((\\d+),\\s*([\\d.]+)%,\\s*([\\d.]+)%(,\\s*\\d+(\\.\\d+)?)*\\)$/i;\n\nexport const isValidHexString = (s: string) => {\n  return !!s.match(IS_HEX_COLOR_REGEX);\n};\n\nexport const isValidRgbaString = (s: string) => {\n  return !!(s.match(IS_RGB_COLOR_REGEX) || s.match(IS_RGBA_COLOR_REGEX));\n};\n\nexport const isValidHslaString = (s: string) => {\n  return !!s.match(IS_HSL_COLOR_REGEX) || !!s.match(IS_HSLA_COLOR_REGEX);\n};\n\nexport const isRGBColor = (c: Color): c is RgbaColor => {\n  return typeof c !== 'string' && 'r' in c;\n};\n\nexport const isHSLColor = (c: Color): c is HslaColor => {\n  return typeof c !== 'string' && 'h' in c;\n};\n\nexport const isTransparent = (c: Color): c is TransparentColor => {\n  return c === 'transparent';\n};\n\nexport const hasAlpha = (color: Color): boolean => {\n  return typeof color !== 'string' && color.a != undefined && color.a < 1;\n};\n\nconst CLEAN_HSLA_REGEX = /[hsla()]/g;\nconst CLEAN_RGBA_REGEX = /[rgba()]/g;\n\nexport const stringToHslaColor = (value: string): HslaColor | null => {\n  if (value === 'transparent') {\n    return { h: 0, s: 0, l: 0, a: 0 };\n  }\n\n  if (isValidHexString(value)) {\n    return hexStringToHslaColor(value);\n  }\n\n  if (isValidHslaString(value)) {\n    return parseHslaString(value);\n  }\n\n  if (isValidRgbaString(value)) {\n    return rgbaStringToHslaColor(value);\n  }\n\n  return null;\n};\n\nexport const stringToSameTypeColor = (value: string): Color => {\n  value = value.trim();\n  if (isValidHexString(value)) {\n    return value.startsWith('#') ? value : `#${value}`;\n  }\n\n  if (isValidRgbaString(value)) {\n    return parseRgbaString(value);\n  }\n\n  if (isValidHslaString(value)) {\n    return parseHslaString(value);\n  }\n\n  if (isTransparent(value)) {\n    return value;\n  }\n  return '';\n};\n\nexport const colorToSameTypeString = (color: Color): string | TransparentColor => {\n  if (typeof color === 'string' && (isValidHexString(color) || isTransparent(color))) {\n    return color;\n  }\n\n  if (isRGBColor(color)) {\n    return rgbaColorToRgbaString(color);\n  }\n\n  if (isHSLColor(color)) {\n    return hslaColorToHslaString(color);\n  }\n\n  return '';\n};\n\nexport const hexStringToRgbaColor = (hex: string): RgbaColor => {\n  hex = hex.replace('#', '');\n  const r = parseInt(hex.substring(0, 2), 16);\n  const g = parseInt(hex.substring(2, 4), 16);\n  const b = parseInt(hex.substring(4, 6), 16);\n  return { r, g, b };\n};\n\nconst rgbaColorToRgbaString = (color: RgbaColor): string => {\n  const { a, b, g, r } = color;\n  return color.a === 0 ? 'transparent' : color.a != undefined ? `rgba(${r},${g},${b},${a})` : `rgb(${r},${g},${b})`;\n};\n\nconst hslaColorToHslaString = (color: HslaColor): string => {\n  const { h, s, l, a } = color;\n  const sPerc = Math.round(s * 100);\n  const lPerc = Math.round(l * 100);\n  return color.a === 0\n    ? 'transparent'\n    : color.a != undefined\n    ? `hsla(${h},${sPerc}%,${lPerc}%,${a})`\n    : `hsl(${h},${sPerc}%,${lPerc}%)`;\n};\n\nconst hexStringToHslaColor = (hex: string): HslaColor => {\n  const rgbaString = colorToSameTypeString(hexStringToRgbaColor(hex));\n  return rgbaStringToHslaColor(rgbaString);\n};\n\nconst rgbaStringToHslaColor = (rgba: string): HslaColor => {\n  const rgbaColor = parseRgbaString(rgba);\n  const r = rgbaColor.r / 255;\n  const g = rgbaColor.g / 255;\n  const b = rgbaColor.b / 255;\n\n  const max = Math.max(r, g, b),\n    min = Math.min(r, g, b);\n  let h, s;\n  const l = (max + min) / 2;\n\n  if (max == min) {\n    h = s = 0;\n  } else {\n    const d = max - min;\n    s = l >= 0.5 ? d / (2 - (max + min)) : d / (max + min);\n    switch (max) {\n      case r:\n        h = ((g - b) / d) * 60;\n        break;\n      case g:\n        h = ((b - r) / d + 2) * 60;\n        break;\n      default:\n        h = ((r - g) / d + 4) * 60;\n        break;\n    }\n  }\n\n  const res: HslaColor = { h: Math.round(h), s, l };\n  const a = rgbaColor.a;\n  if (a != undefined) {\n    res.a = a;\n  }\n  return res;\n};\n\nconst parseRgbaString = (str: string): RgbaColor => {\n  const [r, g, b, a] = str\n    .replace(CLEAN_RGBA_REGEX, '')\n    .split(',')\n    .map(c => Number.parseFloat(c));\n  return { r, g, b, a };\n};\n\nconst parseHslaString = (str: string): HslaColor => {\n  const [h, s, l, a] = str\n    .replace(CLEAN_HSLA_REGEX, '')\n    .split(',')\n    .map(c => Number.parseFloat(c));\n  return { h, s: s / 100, l: l / 100, a };\n};\n"], "mappings": ";AAEA,IAAMA,kBAAA,GAAqB;AAE3B,IAAMC,kBAAA,GAAqB;AAC3B,IAAMC,mBAAA,GAAsB;AAE5B,IAAMC,kBAAA,GAAqB;AAC3B,IAAMC,mBAAA,GAAsB;AAErB,IAAMC,gBAAA,GAAoBC,CAAA,IAAc;EAC7C,OAAO,CAAC,CAACA,CAAA,CAAEC,KAAA,CAAMP,kBAAkB;AACrC;AAEO,IAAMQ,iBAAA,GAAqBF,CAAA,IAAc;EAC9C,OAAO,CAAC,EAAEA,CAAA,CAAEC,KAAA,CAAMN,kBAAkB,KAAKK,CAAA,CAAEC,KAAA,CAAML,mBAAmB;AACtE;AAEO,IAAMO,iBAAA,GAAqBH,CAAA,IAAc;EAC9C,OAAO,CAAC,CAACA,CAAA,CAAEC,KAAA,CAAMJ,kBAAkB,KAAK,CAAC,CAACG,CAAA,CAAEC,KAAA,CAAMH,mBAAmB;AACvE;AAEO,IAAMM,UAAA,GAAcC,CAAA,IAA6B;EACtD,OAAO,OAAOA,CAAA,KAAM,YAAY,OAAOA,CAAA;AACzC;AAEO,IAAMC,UAAA,GAAcD,CAAA,IAA6B;EACtD,OAAO,OAAOA,CAAA,KAAM,YAAY,OAAOA,CAAA;AACzC;AAEO,IAAME,aAAA,GAAiBF,CAAA,IAAoC;EAChE,OAAOA,CAAA,KAAM;AACf;AAEO,IAAMG,QAAA,GAAYC,KAAA,IAA0B;EACjD,OAAO,OAAOA,KAAA,KAAU,YAAYA,KAAA,CAAMC,CAAA,IAAK,UAAaD,KAAA,CAAMC,CAAA,GAAI;AACxE;AAEA,IAAMC,gBAAA,GAAmB;AACzB,IAAMC,gBAAA,GAAmB;AAElB,IAAMC,iBAAA,GAAqBC,KAAA,IAAoC;EACpE,IAAIA,KAAA,KAAU,eAAe;IAC3B,OAAO;MAAEC,CAAA,EAAG;MAAGf,CAAA,EAAG;MAAGgB,CAAA,EAAG;MAAGN,CAAA,EAAG;IAAE;EAClC;EAEA,IAAIX,gBAAA,CAAiBe,KAAK,GAAG;IAC3B,OAAOG,oBAAA,CAAqBH,KAAK;EACnC;EAEA,IAAIX,iBAAA,CAAkBW,KAAK,GAAG;IAC5B,OAAOI,eAAA,CAAgBJ,KAAK;EAC9B;EAEA,IAAIZ,iBAAA,CAAkBY,KAAK,GAAG;IAC5B,OAAOK,qBAAA,CAAsBL,KAAK;EACpC;EAEA,OAAO;AACT;AAEO,IAAMM,qBAAA,GAAyBN,KAAA,IAAyB;EAC7DA,KAAA,GAAQA,KAAA,CAAMO,IAAA,CAAK;EACnB,IAAItB,gBAAA,CAAiBe,KAAK,GAAG;IAC3B,OAAOA,KAAA,CAAMQ,UAAA,CAAW,GAAG,IAAIR,KAAA,GAAQ,IAAIA,KAAK;EAClD;EAEA,IAAIZ,iBAAA,CAAkBY,KAAK,GAAG;IAC5B,OAAOS,eAAA,CAAgBT,KAAK;EAC9B;EAEA,IAAIX,iBAAA,CAAkBW,KAAK,GAAG;IAC5B,OAAOI,eAAA,CAAgBJ,KAAK;EAC9B;EAEA,IAAIP,aAAA,CAAcO,KAAK,GAAG;IACxB,OAAOA,KAAA;EACT;EACA,OAAO;AACT;AAEO,IAAMU,qBAAA,GAAyBf,KAAA,IAA4C;EAChF,IAAI,OAAOA,KAAA,KAAU,aAAaV,gBAAA,CAAiBU,KAAK,KAAKF,aAAA,CAAcE,KAAK,IAAI;IAClF,OAAOA,KAAA;EACT;EAEA,IAAIL,UAAA,CAAWK,KAAK,GAAG;IACrB,OAAOgB,qBAAA,CAAsBhB,KAAK;EACpC;EAEA,IAAIH,UAAA,CAAWG,KAAK,GAAG;IACrB,OAAOiB,qBAAA,CAAsBjB,KAAK;EACpC;EAEA,OAAO;AACT;AAEO,IAAMkB,oBAAA,GAAwBC,GAAA,IAA2B;EAC9DA,GAAA,GAAMA,GAAA,CAAIC,OAAA,CAAQ,KAAK,EAAE;EACzB,MAAMC,CAAA,GAAIC,QAAA,CAASH,GAAA,CAAII,SAAA,CAAU,GAAG,CAAC,GAAG,EAAE;EAC1C,MAAMC,CAAA,GAAIF,QAAA,CAASH,GAAA,CAAII,SAAA,CAAU,GAAG,CAAC,GAAG,EAAE;EAC1C,MAAME,CAAA,GAAIH,QAAA,CAASH,GAAA,CAAII,SAAA,CAAU,GAAG,CAAC,GAAG,EAAE;EAC1C,OAAO;IAAEF,CAAA;IAAGG,CAAA;IAAGC;EAAE;AACnB;AAEA,IAAMT,qBAAA,GAAyBhB,KAAA,IAA6B;EAC1D,MAAM;IAAEC,CAAA;IAAGwB,CAAA;IAAGD,CAAA;IAAGH;EAAE,IAAIrB,KAAA;EACvB,OAAOA,KAAA,CAAMC,CAAA,KAAM,IAAI,gBAAgBD,KAAA,CAAMC,CAAA,IAAK,SAAY,QAAQoB,CAAC,IAAIG,CAAC,IAAIC,CAAC,IAAIxB,CAAC,MAAM,OAAOoB,CAAC,IAAIG,CAAC,IAAIC,CAAC;AAChH;AAEA,IAAMR,qBAAA,GAAyBjB,KAAA,IAA6B;EAC1D,MAAM;IAAEM,CAAA;IAAGf,CAAA;IAAGgB,CAAA;IAAGN;EAAE,IAAID,KAAA;EACvB,MAAM0B,KAAA,GAAQC,IAAA,CAAKC,KAAA,CAAMrC,CAAA,GAAI,GAAG;EAChC,MAAMsC,KAAA,GAAQF,IAAA,CAAKC,KAAA,CAAMrB,CAAA,GAAI,GAAG;EAChC,OAAOP,KAAA,CAAMC,CAAA,KAAM,IACf,gBACAD,KAAA,CAAMC,CAAA,IAAK,SACX,QAAQK,CAAC,IAAIoB,KAAK,KAAKG,KAAK,KAAK5B,CAAC,MAClC,OAAOK,CAAC,IAAIoB,KAAK,KAAKG,KAAK;AACjC;AAEA,IAAMrB,oBAAA,GAAwBW,GAAA,IAA2B;EACvD,MAAMW,UAAA,GAAaf,qBAAA,CAAsBG,oBAAA,CAAqBC,GAAG,CAAC;EAClE,OAAOT,qBAAA,CAAsBoB,UAAU;AACzC;AAEA,IAAMpB,qBAAA,GAAyBqB,IAAA,IAA4B;EACzD,MAAMC,SAAA,GAAYlB,eAAA,CAAgBiB,IAAI;EACtC,MAAMV,CAAA,GAAIW,SAAA,CAAUX,CAAA,GAAI;EACxB,MAAMG,CAAA,GAAIQ,SAAA,CAAUR,CAAA,GAAI;EACxB,MAAMC,CAAA,GAAIO,SAAA,CAAUP,CAAA,GAAI;EAExB,MAAMQ,GAAA,GAAMN,IAAA,CAAKM,GAAA,CAAIZ,CAAA,EAAGG,CAAA,EAAGC,CAAC;IAC1BS,GAAA,GAAMP,IAAA,CAAKO,GAAA,CAAIb,CAAA,EAAGG,CAAA,EAAGC,CAAC;EACxB,IAAInB,CAAA,EAAGf,CAAA;EACP,MAAMgB,CAAA,IAAK0B,GAAA,GAAMC,GAAA,IAAO;EAExB,IAAID,GAAA,IAAOC,GAAA,EAAK;IACd5B,CAAA,GAAIf,CAAA,GAAI;EACV,OAAO;IACL,MAAM4C,CAAA,GAAIF,GAAA,GAAMC,GAAA;IAChB3C,CAAA,GAAIgB,CAAA,IAAK,MAAM4B,CAAA,IAAK,KAAKF,GAAA,GAAMC,GAAA,KAAQC,CAAA,IAAKF,GAAA,GAAMC,GAAA;IAClD,QAAQD,GAAA;MACN,KAAKZ,CAAA;QACHf,CAAA,IAAMkB,CAAA,GAAIC,CAAA,IAAKU,CAAA,GAAK;QACpB;MACF,KAAKX,CAAA;QACHlB,CAAA,KAAMmB,CAAA,GAAIJ,CAAA,IAAKc,CAAA,GAAI,KAAK;QACxB;MACF;QACE7B,CAAA,KAAMe,CAAA,GAAIG,CAAA,IAAKW,CAAA,GAAI,KAAK;QACxB;IACJ;EACF;EAEA,MAAMC,GAAA,GAAiB;IAAE9B,CAAA,EAAGqB,IAAA,CAAKC,KAAA,CAAMtB,CAAC;IAAGf,CAAA;IAAGgB;EAAE;EAChD,MAAMN,CAAA,GAAI+B,SAAA,CAAU/B,CAAA;EACpB,IAAIA,CAAA,IAAK,QAAW;IAClBmC,GAAA,CAAInC,CAAA,GAAIA,CAAA;EACV;EACA,OAAOmC,GAAA;AACT;AAEA,IAAMtB,eAAA,GAAmBuB,GAAA,IAA2B;EAClD,MAAM,CAAChB,CAAA,EAAGG,CAAA,EAAGC,CAAA,EAAGxB,CAAC,IAAIoC,GAAA,CAClBjB,OAAA,CAAQjB,gBAAA,EAAkB,EAAE,EAC5BmC,KAAA,CAAM,GAAG,EACTC,GAAA,CAAI3C,CAAA,IAAK4C,MAAA,CAAOC,UAAA,CAAW7C,CAAC,CAAC;EAChC,OAAO;IAAEyB,CAAA;IAAGG,CAAA;IAAGC,CAAA;IAAGxB;EAAE;AACtB;AAEA,IAAMQ,eAAA,GAAmB4B,GAAA,IAA2B;EAClD,MAAM,CAAC/B,CAAA,EAAGf,CAAA,EAAGgB,CAAA,EAAGN,CAAC,IAAIoC,GAAA,CAClBjB,OAAA,CAAQlB,gBAAA,EAAkB,EAAE,EAC5BoC,KAAA,CAAM,GAAG,EACTC,GAAA,CAAI3C,CAAA,IAAK4C,MAAA,CAAOC,UAAA,CAAW7C,CAAC,CAAC;EAChC,OAAO;IAAEU,CAAA;IAAGf,CAAA,EAAGA,CAAA,GAAI;IAAKgB,CAAA,EAAGA,CAAA,GAAI;IAAKN;EAAE;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}