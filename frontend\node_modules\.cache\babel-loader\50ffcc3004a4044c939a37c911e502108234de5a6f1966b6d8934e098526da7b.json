{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport { deprecated } from \"@clerk/shared/deprecated\";\nimport React from \"react\";\nimport IsomorphicClerk from \"../isomorphicClerk\";\nimport { deriveState } from \"../utils/deriveState\";\nimport { AuthContext } from \"./AuthContext\";\nimport { ClientContext } from \"./ClientContext\";\nimport { IsomorphicClerkContext } from \"./IsomorphicClerkContext\";\nimport { OrganizationProvider } from \"./OrganizationContext\";\nimport { SessionContext } from \"./SessionContext\";\nimport { UserContext } from \"./UserContext\";\nfunction ClerkContextProvider(props) {\n  const {\n    isomorphicClerkOptions,\n    initialState,\n    children\n  } = props;\n  const {\n    isomorphicClerk: clerk,\n    loaded: clerkLoaded\n  } = useLoadedIsomorphicClerk(isomorphicClerkOptions);\n  if (isomorphicClerkOptions.frontendApi) {\n    deprecated(\"frontendApi\", \"Use `publishableKey` instead.\");\n  }\n  const [state, setState] = React.useState({\n    client: clerk.client,\n    session: clerk.session,\n    user: clerk.user,\n    organization: clerk.organization,\n    lastOrganizationInvitation: null,\n    lastOrganizationMember: null\n  });\n  React.useEffect(() => {\n    return clerk.addListener(e => setState({\n      ...e\n    }));\n  }, []);\n  const derivedState = deriveState(clerkLoaded, state, initialState);\n  const clerkCtx = React.useMemo(() => ({\n    value: clerk\n  }), [clerkLoaded]);\n  const clientCtx = React.useMemo(() => ({\n    value: state.client\n  }), [state.client]);\n  const {\n    sessionId,\n    session,\n    userId,\n    user,\n    orgId,\n    actor,\n    lastOrganizationInvitation,\n    lastOrganizationMember,\n    organization,\n    orgRole,\n    orgSlug,\n    orgPermissions\n  } = derivedState;\n  const authCtx = React.useMemo(() => {\n    const value = {\n      sessionId,\n      userId,\n      actor,\n      orgId,\n      orgRole,\n      orgSlug,\n      orgPermissions\n    };\n    return {\n      value\n    };\n  }, [sessionId, userId, actor, orgId, orgRole, orgSlug]);\n  const userCtx = React.useMemo(() => ({\n    value: user\n  }), [userId, user]);\n  const sessionCtx = React.useMemo(() => ({\n    value: session\n  }), [sessionId, session]);\n  const organizationCtx = React.useMemo(() => {\n    const value = {\n      organization,\n      lastOrganizationInvitation,\n      lastOrganizationMember\n    };\n    return {\n      value\n    };\n  }, [orgId, organization, lastOrganizationInvitation, lastOrganizationMember]);\n  return (\n    // @ts-expect-error value passed is of type IsomorphicClerk where the context expects LoadedClerk\n    /* @__PURE__ */\n    React.createElement(IsomorphicClerkContext.Provider, {\n      value: clerkCtx\n    }, /* @__PURE__ */React.createElement(ClientContext.Provider, {\n      value: clientCtx\n    }, /* @__PURE__ */React.createElement(SessionContext.Provider, {\n      value: sessionCtx\n    }, /* @__PURE__ */React.createElement(OrganizationProvider, {\n      ...organizationCtx.value\n    }, /* @__PURE__ */React.createElement(AuthContext.Provider, {\n      value: authCtx\n    }, /* @__PURE__ */React.createElement(UserContext.Provider, {\n      value: userCtx\n    }, children))))))\n  );\n}\nconst useLoadedIsomorphicClerk = options => {\n  const [loaded, setLoaded] = React.useState(false);\n  const isomorphicClerk = React.useMemo(() => IsomorphicClerk.getOrCreateInstance(options), []);\n  React.useEffect(() => {\n    isomorphicClerk.__unstable__updateProps({\n      appearance: options.appearance\n    });\n  }, [options.appearance]);\n  React.useEffect(() => {\n    isomorphicClerk.__unstable__updateProps({\n      options\n    });\n  }, [options.localization]);\n  React.useEffect(() => {\n    isomorphicClerk.addOnLoaded(() => setLoaded(true));\n  }, []);\n  React.useEffect(() => {\n    return () => {\n      IsomorphicClerk.clearInstance();\n    };\n  }, []);\n  return {\n    isomorphicClerk,\n    loaded\n  };\n};\nexport { ClerkContextProvider };", "map": {"version": 3, "names": ["deprecated", "React", "IsomorphicClerk", "deriveState", "AuthContext", "ClientContext", "IsomorphicClerkContext", "OrganizationProvider", "SessionContext", "UserContext", "Clerk<PERSON><PERSON><PERSON><PERSON><PERSON>rov<PERSON>", "props", "isomorphicClerkOptions", "initialState", "children", "isomorphicClerk", "clerk", "loaded", "clerkLoaded", "useLoadedIsomorphicClerk", "frontendApi", "state", "setState", "useState", "client", "session", "user", "organization", "lastOrganizationInvitation", "lastOrganizationMember", "useEffect", "addListener", "e", "derivedState", "clerkCtx", "useMemo", "value", "clientCtx", "sessionId", "userId", "orgId", "actor", "orgRole", "orgSlug", "orgPermissions", "authCtx", "userCtx", "sessionCtx", "organizationCtx", "createElement", "Provider", "options", "setLoaded", "getOrCreateInstance", "__unstable__updateProps", "appearance", "localization", "addOnLoaded", "clearInstance"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\contexts\\ClerkContextProvider.tsx"], "sourcesContent": ["import { deprecated } from '@clerk/shared/deprecated';\nimport type { ClientResource, InitialState, Resources } from '@clerk/types';\nimport React from 'react';\n\nimport IsomorphicClerk from '../isomorphicClerk';\nimport type { IsomorphicClerkOptions } from '../types';\nimport { deriveState } from '../utils/deriveState';\nimport { AuthContext } from './AuthContext';\nimport { ClientContext } from './ClientContext';\nimport { IsomorphicClerkContext } from './IsomorphicClerkContext';\nimport { OrganizationProvider } from './OrganizationContext';\nimport { SessionContext } from './SessionContext';\nimport { UserContext } from './UserContext';\n\ntype ClerkContextProvider = {\n  isomorphicClerkOptions: IsomorphicClerkOptions;\n  initialState: InitialState | undefined;\n  children: React.ReactNode;\n};\n\nexport type ClerkContextProviderState = Resources;\n\nexport function ClerkContextProvider(props: ClerkContextProvider): JSX.Element | null {\n  const { isomorphicClerkOptions, initialState, children } = props;\n  const { isomorphicClerk: clerk, loaded: clerkLoaded } = useLoadedIsomorphicClerk(isomorphicClerkOptions);\n\n  if (isomorphicClerkOptions.frontendApi) {\n    deprecated('frontendApi', 'Use `publishableKey` instead.');\n  }\n\n  const [state, setState] = React.useState<ClerkContextProviderState>({\n    client: clerk.client as ClientResource,\n    session: clerk.session,\n    user: clerk.user,\n    organization: clerk.organization,\n    lastOrganizationInvitation: null,\n    lastOrganizationMember: null,\n  });\n\n  React.useEffect(() => {\n    return clerk.addListener(e => setState({ ...e }));\n  }, []);\n\n  const derivedState = deriveState(clerkLoaded, state, initialState);\n  const clerkCtx = React.useMemo(() => ({ value: clerk }), [clerkLoaded]);\n  const clientCtx = React.useMemo(() => ({ value: state.client }), [state.client]);\n\n  const {\n    sessionId,\n    session,\n    userId,\n    user,\n    orgId,\n    actor,\n    lastOrganizationInvitation,\n    lastOrganizationMember,\n    organization,\n    orgRole,\n    orgSlug,\n    orgPermissions,\n  } = derivedState;\n\n  const authCtx = React.useMemo(() => {\n    const value = { sessionId, userId, actor, orgId, orgRole, orgSlug, orgPermissions };\n    return { value };\n  }, [sessionId, userId, actor, orgId, orgRole, orgSlug]);\n  const userCtx = React.useMemo(() => ({ value: user }), [userId, user]);\n  const sessionCtx = React.useMemo(() => ({ value: session }), [sessionId, session]);\n  const organizationCtx = React.useMemo(() => {\n    const value = {\n      organization: organization,\n      lastOrganizationInvitation: lastOrganizationInvitation,\n      lastOrganizationMember: lastOrganizationMember,\n    };\n    return { value };\n  }, [orgId, organization, lastOrganizationInvitation, lastOrganizationMember]);\n\n  return (\n    // @ts-expect-error value passed is of type IsomorphicClerk where the context expects LoadedClerk\n    <IsomorphicClerkContext.Provider value={clerkCtx}>\n      <ClientContext.Provider value={clientCtx}>\n        <SessionContext.Provider value={sessionCtx}>\n          <OrganizationProvider {...organizationCtx.value}>\n            <AuthContext.Provider value={authCtx}>\n              <UserContext.Provider value={userCtx}>{children}</UserContext.Provider>\n            </AuthContext.Provider>\n          </OrganizationProvider>\n        </SessionContext.Provider>\n      </ClientContext.Provider>\n    </IsomorphicClerkContext.Provider>\n  );\n}\n\nconst useLoadedIsomorphicClerk = (options: IsomorphicClerkOptions) => {\n  const [loaded, setLoaded] = React.useState(false);\n  const isomorphicClerk = React.useMemo(() => IsomorphicClerk.getOrCreateInstance(options), []);\n\n  React.useEffect(() => {\n    isomorphicClerk.__unstable__updateProps({ appearance: options.appearance });\n  }, [options.appearance]);\n\n  React.useEffect(() => {\n    isomorphicClerk.__unstable__updateProps({ options });\n  }, [options.localization]);\n\n  React.useEffect(() => {\n    isomorphicClerk.addOnLoaded(() => setLoaded(true));\n  }, []);\n\n  React.useEffect(() => {\n    return () => {\n      IsomorphicClerk.clearInstance();\n    };\n  }, []);\n\n  return { isomorphicClerk, loaded };\n};\n"], "mappings": ";AAAA,SAASA,UAAA,QAAkB;AAE3B,OAAOC,KAAA,MAAW;AAElB,OAAOC,eAAA,MAAqB;AAE5B,SAASC,WAAA,QAAmB;AAC5B,SAASC,WAAA,QAAmB;AAC5B,SAASC,aAAA,QAAqB;AAC9B,SAASC,sBAAA,QAA8B;AACvC,SAASC,oBAAA,QAA4B;AACrC,SAASC,cAAA,QAAsB;AAC/B,SAASC,WAAA,QAAmB;AAUrB,SAASC,qBAAqBC,KAAA,EAAiD;EACpF,MAAM;IAAEC,sBAAA;IAAwBC,YAAA;IAAcC;EAAS,IAAIH,KAAA;EAC3D,MAAM;IAAEI,eAAA,EAAiBC,KAAA;IAAOC,MAAA,EAAQC;EAAY,IAAIC,wBAAA,CAAyBP,sBAAsB;EAEvG,IAAIA,sBAAA,CAAuBQ,WAAA,EAAa;IACtCpB,UAAA,CAAW,eAAe,+BAA+B;EAC3D;EAEA,MAAM,CAACqB,KAAA,EAAOC,QAAQ,IAAIrB,KAAA,CAAMsB,QAAA,CAAoC;IAClEC,MAAA,EAAQR,KAAA,CAAMQ,MAAA;IACdC,OAAA,EAAST,KAAA,CAAMS,OAAA;IACfC,IAAA,EAAMV,KAAA,CAAMU,IAAA;IACZC,YAAA,EAAcX,KAAA,CAAMW,YAAA;IACpBC,0BAAA,EAA4B;IAC5BC,sBAAA,EAAwB;EAC1B,CAAC;EAED5B,KAAA,CAAM6B,SAAA,CAAU,MAAM;IACpB,OAAOd,KAAA,CAAMe,WAAA,CAAYC,CAAA,IAAKV,QAAA,CAAS;MAAE,GAAGU;IAAE,CAAC,CAAC;EAClD,GAAG,EAAE;EAEL,MAAMC,YAAA,GAAe9B,WAAA,CAAYe,WAAA,EAAaG,KAAA,EAAOR,YAAY;EACjE,MAAMqB,QAAA,GAAWjC,KAAA,CAAMkC,OAAA,CAAQ,OAAO;IAAEC,KAAA,EAAOpB;EAAM,IAAI,CAACE,WAAW,CAAC;EACtE,MAAMmB,SAAA,GAAYpC,KAAA,CAAMkC,OAAA,CAAQ,OAAO;IAAEC,KAAA,EAAOf,KAAA,CAAMG;EAAO,IAAI,CAACH,KAAA,CAAMG,MAAM,CAAC;EAE/E,MAAM;IACJc,SAAA;IACAb,OAAA;IACAc,MAAA;IACAb,IAAA;IACAc,KAAA;IACAC,KAAA;IACAb,0BAAA;IACAC,sBAAA;IACAF,YAAA;IACAe,OAAA;IACAC,OAAA;IACAC;EACF,IAAIX,YAAA;EAEJ,MAAMY,OAAA,GAAU5C,KAAA,CAAMkC,OAAA,CAAQ,MAAM;IAClC,MAAMC,KAAA,GAAQ;MAAEE,SAAA;MAAWC,MAAA;MAAQE,KAAA;MAAOD,KAAA;MAAOE,OAAA;MAASC,OAAA;MAASC;IAAe;IAClF,OAAO;MAAER;IAAM;EACjB,GAAG,CAACE,SAAA,EAAWC,MAAA,EAAQE,KAAA,EAAOD,KAAA,EAAOE,OAAA,EAASC,OAAO,CAAC;EACtD,MAAMG,OAAA,GAAU7C,KAAA,CAAMkC,OAAA,CAAQ,OAAO;IAAEC,KAAA,EAAOV;EAAK,IAAI,CAACa,MAAA,EAAQb,IAAI,CAAC;EACrE,MAAMqB,UAAA,GAAa9C,KAAA,CAAMkC,OAAA,CAAQ,OAAO;IAAEC,KAAA,EAAOX;EAAQ,IAAI,CAACa,SAAA,EAAWb,OAAO,CAAC;EACjF,MAAMuB,eAAA,GAAkB/C,KAAA,CAAMkC,OAAA,CAAQ,MAAM;IAC1C,MAAMC,KAAA,GAAQ;MACZT,YAAA;MACAC,0BAAA;MACAC;IACF;IACA,OAAO;MAAEO;IAAM;EACjB,GAAG,CAACI,KAAA,EAAOb,YAAA,EAAcC,0BAAA,EAA4BC,sBAAsB,CAAC;EAE5E;IAAA;IAEE;IAAA5B,KAAA,CAAAgD,aAAA,CAAC3C,sBAAA,CAAuB4C,QAAA,EAAvB;MAAgCd,KAAA,EAAOF;IAAA,GACtC,eAAAjC,KAAA,CAAAgD,aAAA,CAAC5C,aAAA,CAAc6C,QAAA,EAAd;MAAuBd,KAAA,EAAOC;IAAA,GAC7B,eAAApC,KAAA,CAAAgD,aAAA,CAACzC,cAAA,CAAe0C,QAAA,EAAf;MAAwBd,KAAA,EAAOW;IAAA,GAC9B,eAAA9C,KAAA,CAAAgD,aAAA,CAAC1C,oBAAA;MAAsB,GAAGyC,eAAA,CAAgBZ;IAAA,GACxC,eAAAnC,KAAA,CAAAgD,aAAA,CAAC7C,WAAA,CAAY8C,QAAA,EAAZ;MAAqBd,KAAA,EAAOS;IAAA,GAC3B,eAAA5C,KAAA,CAAAgD,aAAA,CAACxC,WAAA,CAAYyC,QAAA,EAAZ;MAAqBd,KAAA,EAAOU;IAAA,GAAUhC,QAAS,CAClD,CACF,CACF,CACF,CACF;EAAA;AAEJ;AAEA,MAAMK,wBAAA,GAA4BgC,OAAA,IAAoC;EACpE,MAAM,CAAClC,MAAA,EAAQmC,SAAS,IAAInD,KAAA,CAAMsB,QAAA,CAAS,KAAK;EAChD,MAAMR,eAAA,GAAkBd,KAAA,CAAMkC,OAAA,CAAQ,MAAMjC,eAAA,CAAgBmD,mBAAA,CAAoBF,OAAO,GAAG,EAAE;EAE5FlD,KAAA,CAAM6B,SAAA,CAAU,MAAM;IACpBf,eAAA,CAAgBuC,uBAAA,CAAwB;MAAEC,UAAA,EAAYJ,OAAA,CAAQI;IAAW,CAAC;EAC5E,GAAG,CAACJ,OAAA,CAAQI,UAAU,CAAC;EAEvBtD,KAAA,CAAM6B,SAAA,CAAU,MAAM;IACpBf,eAAA,CAAgBuC,uBAAA,CAAwB;MAAEH;IAAQ,CAAC;EACrD,GAAG,CAACA,OAAA,CAAQK,YAAY,CAAC;EAEzBvD,KAAA,CAAM6B,SAAA,CAAU,MAAM;IACpBf,eAAA,CAAgB0C,WAAA,CAAY,MAAML,SAAA,CAAU,IAAI,CAAC;EACnD,GAAG,EAAE;EAELnD,KAAA,CAAM6B,SAAA,CAAU,MAAM;IACpB,OAAO,MAAM;MACX5B,eAAA,CAAgBwD,aAAA,CAAc;IAChC;EACF,GAAG,EAAE;EAEL,OAAO;IAAE3C,eAAA;IAAiBE;EAAO;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}