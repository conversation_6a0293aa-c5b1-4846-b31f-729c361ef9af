{"ast": null, "code": "// src/underscore.ts\nvar toSentence = items => {\n  if (items.length == 0) {\n    return \"\";\n  }\n  if (items.length == 1) {\n    return items[0];\n  }\n  let sentence = items.slice(0, -1).join(\", \");\n  sentence += `, or ${items.slice(-1)}`;\n  return sentence;\n};\nvar IP_V4_ADDRESS_REGEX = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\nfunction isIPV4Address(str) {\n  return IP_V4_ADDRESS_REGEX.test(str || \"\");\n}\nfunction titleize(str) {\n  const s = str || \"\";\n  return s.charAt(0).toUpperCase() + s.slice(1);\n}\nfunction snakeToCamel(str) {\n  return str ? str.replace(/([-_][a-z])/g, match => match.toUpperCase().replace(/-|_/, \"\")) : \"\";\n}\nfunction camelToSnake(str) {\n  return str ? str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`) : \"\";\n}\nvar createDeepObjectTransformer = transform => {\n  const deepTransform = obj => {\n    if (!obj) {\n      return obj;\n    }\n    if (Array.isArray(obj)) {\n      return obj.map(el => {\n        if (typeof el === \"object\" || Array.isArray(el)) {\n          return deepTransform(el);\n        }\n        return el;\n      });\n    }\n    const copy = {\n      ...obj\n    };\n    const keys = Object.keys(copy);\n    for (const oldName of keys) {\n      const newName = transform(oldName.toString());\n      if (newName !== oldName) {\n        copy[newName] = copy[oldName];\n        delete copy[oldName];\n      }\n      if (typeof copy[newName] === \"object\") {\n        copy[newName] = deepTransform(copy[newName]);\n      }\n    }\n    return copy;\n  };\n  return deepTransform;\n};\nvar deepCamelToSnake = createDeepObjectTransformer(camelToSnake);\nvar deepSnakeToCamel = createDeepObjectTransformer(snakeToCamel);\nexport { toSentence, isIPV4Address, titleize, snakeToCamel, camelToSnake, deepCamelToSnake, deepSnakeToCamel };", "map": {"version": 3, "names": ["toSentence", "items", "length", "sentence", "slice", "join", "IP_V4_ADDRESS_REGEX", "isIPV4Address", "str", "test", "titleize", "s", "char<PERSON>t", "toUpperCase", "snakeToCamel", "replace", "match", "camelToSnake", "letter", "toLowerCase", "createDeepObjectTransformer", "transform", "deepTransform", "obj", "Array", "isArray", "map", "el", "copy", "keys", "Object", "old<PERSON>ame", "newName", "toString", "deepCamelToSnake", "deepSnakeToCamel"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\underscore.ts"], "sourcesContent": ["/**\n * Converts an array of strings to a comma-separated sentence\n * @param items {Array<string>}\n * @returns {string} Returns a string with the items joined by a comma and the last item joined by \", or\"\n */\nexport const toSentence = (items: string[]): string => {\n  // TODO: Once <PERSON>fari supports it, use Intl.ListFormat\n  if (items.length == 0) {\n    return '';\n  }\n  if (items.length == 1) {\n    return items[0];\n  }\n  let sentence = items.slice(0, -1).join(', ');\n  sentence += `, or ${items.slice(-1)}`;\n  return sentence;\n};\n\nconst IP_V4_ADDRESS_REGEX =\n  /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;\n\nexport function isIPV4Address(str: string | undefined | null): boolean {\n  return IP_V4_ADDRESS_REGEX.test(str || '');\n}\n\nexport function titleize(str: string | undefined | null): string {\n  const s = str || '';\n  return s.charAt(0).toUpperCase() + s.slice(1);\n}\n\nexport function snakeToCamel(str: string | undefined): string {\n  return str ? str.replace(/([-_][a-z])/g, match => match.toUpperCase().replace(/-|_/, '')) : '';\n}\n\nexport function camelToSnake(str: string | undefined): string {\n  return str ? str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`) : '';\n}\n\nconst createDeepObjectTransformer = (transform: any) => {\n  const deepTransform = (obj: any): any => {\n    if (!obj) {\n      return obj;\n    }\n\n    if (Array.isArray(obj)) {\n      return obj.map(el => {\n        if (typeof el === 'object' || Array.isArray(el)) {\n          return deepTransform(el);\n        }\n        return el;\n      });\n    }\n\n    const copy = { ...obj };\n    const keys = Object.keys(copy);\n    for (const oldName of keys) {\n      const newName = transform(oldName.toString());\n      if (newName !== oldName) {\n        copy[newName] = copy[oldName];\n        delete copy[oldName];\n      }\n      if (typeof copy[newName] === 'object') {\n        copy[newName] = deepTransform(copy[newName]);\n      }\n    }\n    return copy;\n  };\n\n  return deepTransform;\n};\n\n/**\n * Transforms camelCased objects/ arrays to snake_cased.\n * This function recursively traverses all objects and arrays of the passed value\n * camelCased keys are removed.\n */\nexport const deepCamelToSnake = createDeepObjectTransformer(camelToSnake);\n\n/**\n * Transforms snake_cased objects/ arrays to camelCased.\n * This function recursively traverses all objects and arrays of the passed value\n * camelCased keys are removed.\n */\nexport const deepSnakeToCamel = createDeepObjectTransformer(snakeToCamel);\n"], "mappings": ";AAKO,IAAMA,UAAA,GAAcC,KAAA,IAA4B;EAErD,IAAIA,KAAA,CAAMC,MAAA,IAAU,GAAG;IACrB,OAAO;EACT;EACA,IAAID,KAAA,CAAMC,MAAA,IAAU,GAAG;IACrB,OAAOD,KAAA,CAAM,CAAC;EAChB;EACA,IAAIE,QAAA,GAAWF,KAAA,CAAMG,KAAA,CAAM,GAAG,EAAE,EAAEC,IAAA,CAAK,IAAI;EAC3CF,QAAA,IAAY,QAAQF,KAAA,CAAMG,KAAA,CAAM,EAAE,CAAC;EACnC,OAAOD,QAAA;AACT;AAEA,IAAMG,mBAAA,GACJ;AAEK,SAASC,cAAcC,GAAA,EAAyC;EACrE,OAAOF,mBAAA,CAAoBG,IAAA,CAAKD,GAAA,IAAO,EAAE;AAC3C;AAEO,SAASE,SAASF,GAAA,EAAwC;EAC/D,MAAMG,CAAA,GAAIH,GAAA,IAAO;EACjB,OAAOG,CAAA,CAAEC,MAAA,CAAO,CAAC,EAAEC,WAAA,CAAY,IAAIF,CAAA,CAAEP,KAAA,CAAM,CAAC;AAC9C;AAEO,SAASU,aAAaN,GAAA,EAAiC;EAC5D,OAAOA,GAAA,GAAMA,GAAA,CAAIO,OAAA,CAAQ,gBAAgBC,KAAA,IAASA,KAAA,CAAMH,WAAA,CAAY,EAAEE,OAAA,CAAQ,OAAO,EAAE,CAAC,IAAI;AAC9F;AAEO,SAASE,aAAaT,GAAA,EAAiC;EAC5D,OAAOA,GAAA,GAAMA,GAAA,CAAIO,OAAA,CAAQ,UAAUG,MAAA,IAAU,IAAIA,MAAA,CAAOC,WAAA,CAAY,CAAC,EAAE,IAAI;AAC7E;AAEA,IAAMC,2BAAA,GAA+BC,SAAA,IAAmB;EACtD,MAAMC,aAAA,GAAiBC,GAAA,IAAkB;IACvC,IAAI,CAACA,GAAA,EAAK;MACR,OAAOA,GAAA;IACT;IAEA,IAAIC,KAAA,CAAMC,OAAA,CAAQF,GAAG,GAAG;MACtB,OAAOA,GAAA,CAAIG,GAAA,CAAIC,EAAA,IAAM;QACnB,IAAI,OAAOA,EAAA,KAAO,YAAYH,KAAA,CAAMC,OAAA,CAAQE,EAAE,GAAG;UAC/C,OAAOL,aAAA,CAAcK,EAAE;QACzB;QACA,OAAOA,EAAA;MACT,CAAC;IACH;IAEA,MAAMC,IAAA,GAAO;MAAE,GAAGL;IAAI;IACtB,MAAMM,IAAA,GAAOC,MAAA,CAAOD,IAAA,CAAKD,IAAI;IAC7B,WAAWG,OAAA,IAAWF,IAAA,EAAM;MAC1B,MAAMG,OAAA,GAAUX,SAAA,CAAUU,OAAA,CAAQE,QAAA,CAAS,CAAC;MAC5C,IAAID,OAAA,KAAYD,OAAA,EAAS;QACvBH,IAAA,CAAKI,OAAO,IAAIJ,IAAA,CAAKG,OAAO;QAC5B,OAAOH,IAAA,CAAKG,OAAO;MACrB;MACA,IAAI,OAAOH,IAAA,CAAKI,OAAO,MAAM,UAAU;QACrCJ,IAAA,CAAKI,OAAO,IAAIV,aAAA,CAAcM,IAAA,CAAKI,OAAO,CAAC;MAC7C;IACF;IACA,OAAOJ,IAAA;EACT;EAEA,OAAON,aAAA;AACT;AAOO,IAAMY,gBAAA,GAAmBd,2BAAA,CAA4BH,YAAY;AAOjE,IAAMkB,gBAAA,GAAmBf,2BAAA,CAA4BN,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}