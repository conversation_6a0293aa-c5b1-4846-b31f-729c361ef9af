{"ast": null, "code": "import { getRequestUrl, isHttpOrHttps, isProxyUrlRelative, isValidProxyUrl, proxyUrlToAbsoluteURL } from \"./chunk-MHVPBPEZ.mjs\";\nimport \"./chunk-IC4FGZI3.mjs\";\nimport \"./chunk-NDCDZYN6.mjs\";\nexport { getRequestUrl, isHttpOrHttps, isProxyUrlRelative, isValidProxyUrl, proxyUrlToAbsoluteURL };", "map": {"version": 3, "names": [], "sources": [], "sourcesContent": ["import {\n  getRequestUrl,\n  isHttpOrHttps,\n  isProxyUrlRelative,\n  isValidProxyUrl,\n  proxyUrlToAbsoluteURL\n} from \"./chunk-MHVPBPEZ.mjs\";\nimport \"./chunk-IC4FGZI3.mjs\";\nimport \"./chunk-NDCDZYN6.mjs\";\nexport {\n  getRequestUrl,\n  isHttpOrHttps,\n  isProxyUrlRelative,\n  isValidProxyUrl,\n  proxyUrlToAbsoluteURL\n};\n"], "mappings": "", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}