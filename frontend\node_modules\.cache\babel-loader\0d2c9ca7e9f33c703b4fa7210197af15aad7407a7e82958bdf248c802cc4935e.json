{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport React from \"react\";\nconst counts = /* @__PURE__ */new Map();\nfunction useMaxAllowedInstancesGuard(name, error, maxCount = 1) {\n  React.useEffect(() => {\n    const count = counts.get(name) || 0;\n    if (count == maxCount) {\n      throw new Error(error);\n    }\n    counts.set(name, count + 1);\n    return () => {\n      counts.set(name, (counts.get(name) || 1) - 1);\n    };\n  }, []);\n}\nfunction withMaxAllowedInstancesGuard(WrappedComponent, name, error) {\n  const displayName = WrappedComponent.displayName || WrappedComponent.name || name || \"Component\";\n  const Hoc = props => {\n    useMaxAllowedInstancesGuard(name, error);\n    return /* @__PURE__ */React.createElement(WrappedComponent, {\n      ...props\n    });\n  };\n  Hoc.displayName = `withMaxAllowedInstancesGuard(${displayName})`;\n  return Hoc;\n}\nexport { useMaxAllowedInstancesGuard, withMaxAllowedInstancesGuard };", "map": {"version": 3, "names": ["React", "counts", "Map", "useMaxAllowedInstancesGuard", "name", "error", "maxCount", "useEffect", "count", "get", "Error", "set", "withMaxAllowedInstancesGuard", "WrappedComponent", "displayName", "Hoc", "props", "createElement"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\utils\\useMaxAllowedInstancesGuard.tsx"], "sourcesContent": ["import React from 'react';\n\nconst counts = new Map<string, number>();\n\nexport function useMaxAllowedInstancesGuard(name: string, error: string, maxCount = 1): void {\n  React.useEffect(() => {\n    const count = counts.get(name) || 0;\n    if (count == maxCount) {\n      throw new Error(error);\n    }\n    counts.set(name, count + 1);\n\n    return () => {\n      counts.set(name, (counts.get(name) || 1) - 1);\n    };\n  }, []);\n}\n\nexport function withMaxAllowedInstancesGuard<P>(\n  WrappedComponent: React.ComponentType<P>,\n  name: string,\n  error: string,\n): React.ComponentType<P> {\n  const displayName = WrappedComponent.displayName || WrappedComponent.name || name || 'Component';\n  const Hoc = (props: P) => {\n    useMaxAllowedInstancesGuard(name, error);\n    return <WrappedComponent {...(props as any)} />;\n  };\n  Hoc.displayName = `withMaxAllowedInstancesGuard(${displayName})`;\n  return Hoc;\n}\n"], "mappings": ";AAAA,OAAOA,KAAA,MAAW;AAElB,MAAMC,MAAA,GAAS,mBAAIC,GAAA,CAAoB;AAEhC,SAASC,4BAA4BC,IAAA,EAAcC,KAAA,EAAeC,QAAA,GAAW,GAAS;EAC3FN,KAAA,CAAMO,SAAA,CAAU,MAAM;IACpB,MAAMC,KAAA,GAAQP,MAAA,CAAOQ,GAAA,CAAIL,IAAI,KAAK;IAClC,IAAII,KAAA,IAASF,QAAA,EAAU;MACrB,MAAM,IAAII,KAAA,CAAML,KAAK;IACvB;IACAJ,MAAA,CAAOU,GAAA,CAAIP,IAAA,EAAMI,KAAA,GAAQ,CAAC;IAE1B,OAAO,MAAM;MACXP,MAAA,CAAOU,GAAA,CAAIP,IAAA,GAAOH,MAAA,CAAOQ,GAAA,CAAIL,IAAI,KAAK,KAAK,CAAC;IAC9C;EACF,GAAG,EAAE;AACP;AAEO,SAASQ,6BACdC,gBAAA,EACAT,IAAA,EACAC,KAAA,EACwB;EACxB,MAAMS,WAAA,GAAcD,gBAAA,CAAiBC,WAAA,IAAeD,gBAAA,CAAiBT,IAAA,IAAQA,IAAA,IAAQ;EACrF,MAAMW,GAAA,GAAOC,KAAA,IAAa;IACxBb,2BAAA,CAA4BC,IAAA,EAAMC,KAAK;IACvC,OAAO,eAAAL,KAAA,CAAAiB,aAAA,CAACJ,gBAAA;MAAkB,GAAIG;IAAA,CAAe;EAC/C;EACAD,GAAA,CAAID,WAAA,GAAc,gCAAgCA,WAAW;EAC7D,OAAOC,GAAA;AACT", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}