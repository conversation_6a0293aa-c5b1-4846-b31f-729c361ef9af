{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport { isLegacyFrontendApiKey, isPublishableKey } from \"@clerk/shared/keys\";\nimport React from \"react\";\nimport { multipleClerkProvidersError } from \"../errors\";\nimport { __internal__setErrorThrowerOptions, errorThrower, withMaxAllowedInstancesGuard } from \"../utils\";\nimport { ClerkContextProvider } from \"./ClerkContextProvider\";\nimport { StructureContext, StructureContextStates } from \"./StructureContext\";\n__internal__setErrorThrowerOptions({\n  packageName: \"@clerk/clerk-react\"\n});\nfunction ClerkProviderBase(props) {\n  const {\n    initialState,\n    children,\n    ...restIsomorphicClerkOptions\n  } = props;\n  const {\n    frontendApi = \"\",\n    publishableKey = \"\",\n    Clerk: userInitialisedClerk\n  } = restIsomorphicClerkOptions;\n  if (!userInitialisedClerk) {\n    if (!publishableKey && !frontendApi) {\n      errorThrower.throwMissingPublishableKeyError();\n    } else if (publishableKey && !isPublishableKey(publishableKey)) {\n      errorThrower.throwInvalidPublishableKeyError({\n        key: publishableKey\n      });\n    } else if (!publishableKey && frontendApi && !isLegacyFrontendApiKey(frontendApi)) {\n      errorThrower.throwInvalidFrontendApiError({\n        key: frontendApi\n      });\n    }\n  }\n  return /* @__PURE__ */React.createElement(StructureContext.Provider, {\n    value: StructureContextStates.noGuarantees\n  }, /* @__PURE__ */React.createElement(ClerkContextProvider, {\n    initialState,\n    isomorphicClerkOptions: restIsomorphicClerkOptions\n  }, children));\n}\nconst ClerkProvider = withMaxAllowedInstancesGuard(ClerkProviderBase, \"ClerkProvider\", multipleClerkProvidersError);\nClerkProvider.displayName = \"ClerkProvider\";\nexport { ClerkProvider, __internal__setErrorThrowerOptions };", "map": {"version": 3, "names": ["isLegacyFrontendApiKey", "isPublishableKey", "React", "multipleClerkProvidersError", "__internal__setErrorThrowerOptions", "errorThrower", "withMaxAllowedInstancesGuard", "Clerk<PERSON><PERSON><PERSON><PERSON><PERSON>rov<PERSON>", "StructureContext", "StructureContextStates", "packageName", "ClerkProviderBase", "props", "initialState", "children", "restIsomorphicClerkOptions", "frontendApi", "publishableKey", "Clerk", "userInitialisedClerk", "throwMissingPublishableKeyError", "throwInvalidPublishableKeyError", "key", "throwInvalidFrontendApiError", "createElement", "Provider", "value", "noGuarantees", "isomorphicClerkOptions", "<PERSON><PERSON><PERSON><PERSON>", "displayName"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\contexts\\ClerkProvider.tsx"], "sourcesContent": ["import { isLegacyFrontendApiKey, isPublishableKey } from '@clerk/shared/keys';\nimport type { InitialState } from '@clerk/types';\nimport React from 'react';\n\nimport { multipleClerkProvidersError } from '../errors';\nimport type { IsomorphicClerkOptions } from '../types';\nimport { __internal__setErrorThrowerOptions, errorThrower, withMaxAllowedInstancesGuard } from '../utils';\nimport { ClerkContextProvider } from './ClerkContextProvider';\nimport { StructureContext, StructureContextStates } from './StructureContext';\n\n__internal__setErrorThrowerOptions({\n  packageName: '@clerk/clerk-react',\n});\n\nexport type ClerkProviderProps = IsomorphicClerkOptions & {\n  children: React.ReactNode;\n  initialState?: InitialState;\n};\n\nfunction ClerkProviderBase(props: ClerkProviderProps): JSX.Element {\n  const { initialState, children, ...restIsomorphicClerkOptions } = props;\n  const { frontendApi = '', publishableKey = '', Clerk: userInitialisedClerk } = restIsomorphicClerkOptions;\n\n  if (!userInitialisedClerk) {\n    if (!publishableKey && !frontendApi) {\n      errorThrower.throwMissingPublishableKeyError();\n    } else if (publishableKey && !isPublishableKey(publishableKey)) {\n      errorThrower.throwInvalidPublishableKeyError({ key: publishableKey });\n    } else if (!publishableKey && frontendApi && !isLegacyFrontendApiKey(frontendApi)) {\n      errorThrower.throwInvalidFrontendApiError({ key: frontendApi });\n    }\n  }\n\n  return (\n    <StructureContext.Provider value={StructureContextStates.noGuarantees}>\n      <ClerkContextProvider\n        initialState={initialState}\n        isomorphicClerkOptions={restIsomorphicClerkOptions}\n      >\n        {children}\n      </ClerkContextProvider>\n    </StructureContext.Provider>\n  );\n}\n\nconst ClerkProvider = withMaxAllowedInstancesGuard(ClerkProviderBase, 'ClerkProvider', multipleClerkProvidersError);\n\nClerkProvider.displayName = 'ClerkProvider';\n\nexport { ClerkProvider, __internal__setErrorThrowerOptions };\n"], "mappings": ";AAAA,SAASA,sBAAA,EAAwBC,gBAAA,QAAwB;AAEzD,OAAOC,KAAA,MAAW;AAElB,SAASC,2BAAA,QAAmC;AAE5C,SAASC,kCAAA,EAAoCC,YAAA,EAAcC,4BAAA,QAAoC;AAC/F,SAASC,oBAAA,QAA4B;AACrC,SAASC,gBAAA,EAAkBC,sBAAA,QAA8B;AAEzDL,kCAAA,CAAmC;EACjCM,WAAA,EAAa;AACf,CAAC;AAOD,SAASC,kBAAkBC,KAAA,EAAwC;EACjE,MAAM;IAAEC,YAAA;IAAcC,QAAA;IAAU,GAAGC;EAA2B,IAAIH,KAAA;EAClE,MAAM;IAAEI,WAAA,GAAc;IAAIC,cAAA,GAAiB;IAAIC,KAAA,EAAOC;EAAqB,IAAIJ,0BAAA;EAE/E,IAAI,CAACI,oBAAA,EAAsB;IACzB,IAAI,CAACF,cAAA,IAAkB,CAACD,WAAA,EAAa;MACnCX,YAAA,CAAae,+BAAA,CAAgC;IAC/C,WAAWH,cAAA,IAAkB,CAAChB,gBAAA,CAAiBgB,cAAc,GAAG;MAC9DZ,YAAA,CAAagB,+BAAA,CAAgC;QAAEC,GAAA,EAAKL;MAAe,CAAC;IACtE,WAAW,CAACA,cAAA,IAAkBD,WAAA,IAAe,CAAChB,sBAAA,CAAuBgB,WAAW,GAAG;MACjFX,YAAA,CAAakB,4BAAA,CAA6B;QAAED,GAAA,EAAKN;MAAY,CAAC;IAChE;EACF;EAEA,OACE,eAAAd,KAAA,CAAAsB,aAAA,CAAChB,gBAAA,CAAiBiB,QAAA,EAAjB;IAA0BC,KAAA,EAAOjB,sBAAA,CAAuBkB;EAAA,GACvD,eAAAzB,KAAA,CAAAsB,aAAA,CAACjB,oBAAA;IACCM,YAAA;IACAe,sBAAA,EAAwBb;EAAA,GAEvBD,QACH,CACF;AAEJ;AAEA,MAAMe,aAAA,GAAgBvB,4BAAA,CAA6BK,iBAAA,EAAmB,iBAAiBR,2BAA2B;AAElH0B,aAAA,CAAcC,WAAA,GAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}