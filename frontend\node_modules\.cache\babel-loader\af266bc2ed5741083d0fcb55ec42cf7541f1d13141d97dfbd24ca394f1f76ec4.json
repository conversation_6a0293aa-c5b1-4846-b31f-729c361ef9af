{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport { useCallback } from \"react\";\nimport { useAuthContext } from \"../contexts/AuthContext\";\nimport { useIsomorphicClerkContext } from \"../contexts/IsomorphicClerkContext\";\nimport { invalidStateError, useAuthHasRequiresRoleOrPermission } from \"../errors\";\nimport { createGetToken, createSignOut } from \"./utils\";\nconst useAuth = () => {\n  const {\n    sessionId,\n    userId,\n    actor,\n    orgId,\n    orgRole,\n    orgSlug,\n    orgPermissions\n  } = useAuthContext();\n  const isomorphicClerk = useIsomorphicClerkContext();\n  const getToken = useCallback(createGetToken(isomorphicClerk), [isomorphicClerk]);\n  const signOut = useCallback(createSignOut(isomorphicClerk), [isomorphicClerk]);\n  const has = useCallback(params => {\n    if (!(params == null ? void 0 : params.permission) && !(params == null ? void 0 : params.role)) {\n      throw new Error(useAuthHasRequiresRoleOrPermission);\n    }\n    if (!orgId || !userId || !orgRole || !orgPermissions) {\n      return false;\n    }\n    if (params.permission) {\n      return orgPermissions.includes(params.permission);\n    }\n    if (params.role) {\n      return orgRole === params.role;\n    }\n    return false;\n  }, [orgId, orgRole, userId, orgPermissions]);\n  if (sessionId === void 0 && userId === void 0) {\n    return {\n      isLoaded: false,\n      isSignedIn: void 0,\n      sessionId,\n      userId,\n      actor: void 0,\n      orgId: void 0,\n      orgRole: void 0,\n      orgSlug: void 0,\n      has: void 0,\n      signOut,\n      getToken\n    };\n  }\n  if (sessionId === null && userId === null) {\n    return {\n      isLoaded: true,\n      isSignedIn: false,\n      sessionId,\n      userId,\n      actor: null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken\n    };\n  }\n  if (!!sessionId && !!userId && !!orgId && !!orgRole) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      userId,\n      actor: actor || null,\n      orgId,\n      orgRole,\n      orgSlug: orgSlug || null,\n      has,\n      signOut,\n      getToken\n    };\n  }\n  if (!!sessionId && !!userId && !orgId) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      userId,\n      actor: actor || null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken\n    };\n  }\n  throw new Error(invalidStateError);\n};\nexport { useAuth };", "map": {"version": 3, "names": ["useCallback", "useAuthContext", "useIsomorphicClerkContext", "invalidStateError", "useAuthHasRequiresRoleOrPermission", "createGetToken", "createSignOut", "useAuth", "sessionId", "userId", "actor", "orgId", "orgRole", "orgSlug", "orgPermissions", "isomorphicClerk", "getToken", "signOut", "has", "params", "permission", "role", "Error", "includes", "isLoaded", "isSignedIn"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\hooks\\useAuth.ts"], "sourcesContent": ["import type {\n  ActJWTClaim,\n  CheckAuthorizationWithCustomPermissions,\n  GetToken,\n  MembershipRole,\n  SignOut,\n} from '@clerk/types';\nimport { useCallback } from 'react';\n\nimport { useAuthContext } from '../contexts/AuthContext';\nimport { useIsomorphicClerkContext } from '../contexts/IsomorphicClerkContext';\nimport { invalidStateError, useAuthHasRequiresRoleOrPermission } from '../errors';\nimport type IsomorphicClerk from '../isomorphicClerk';\nimport { createGetToken, createSignOut } from './utils';\n\ntype CheckAuthorizationSignedOut = undefined;\ntype CheckAuthorizationWithoutOrgOrUser = (params?: Parameters<CheckAuthorizationWithCustomPermissions>[0]) => false;\n\ntype UseAuthReturn =\n  | {\n      isLoaded: false;\n      isSignedIn: undefined;\n      userId: undefined;\n      sessionId: undefined;\n      actor: undefined;\n      orgId: undefined;\n      orgRole: undefined;\n      orgSlug: undefined;\n      has: CheckAuthorizationSignedOut;\n      signOut: SignOut;\n      getToken: GetToken;\n    }\n  | {\n      isLoaded: true;\n      isSignedIn: false;\n      userId: null;\n      sessionId: null;\n      actor: null;\n      orgId: null;\n      orgRole: null;\n      orgSlug: null;\n      has: CheckAuthorizationWithoutOrgOrUser;\n      signOut: SignOut;\n      getToken: GetToken;\n    }\n  | {\n      isLoaded: true;\n      isSignedIn: true;\n      userId: string;\n      sessionId: string;\n      actor: ActJWTClaim | null;\n      orgId: null;\n      orgRole: null;\n      orgSlug: null;\n      has: CheckAuthorizationWithoutOrgOrUser;\n      signOut: SignOut;\n      getToken: GetToken;\n    }\n  | {\n      isLoaded: true;\n      isSignedIn: true;\n      userId: string;\n      sessionId: string;\n      actor: ActJWTClaim | null;\n      orgId: string;\n      orgRole: MembershipRole;\n      orgSlug: string | null;\n      has: CheckAuthorizationWithCustomPermissions;\n      signOut: SignOut;\n      getToken: GetToken;\n    };\n\ntype UseAuth = () => UseAuthReturn;\n\n/**\n * Returns the current auth state, the user and session ids and the `getToken`\n * that can be used to retrieve the given template or the default Clerk token.\n *\n * Until Clerk loads, `isLoaded` will be set to `false`.\n * Once Clerk loads, `isLoaded` will be set to `true`, and you can\n * safely access the `userId` and `sessionId` variables.\n *\n * For projects using NextJs or Remix, you can have immediate access to this data  during SSR\n * simply by using the `withServerSideAuth` helper.\n *\n * @example\n * A simple example:\n *\n * import { useAuth } from '@clerk/clerk-react'\n *\n * function Hello() {\n *   const { isSignedIn, sessionId, userId } = useAuth();\n *   if(isSignedIn) {\n *     return null;\n *   }\n *   console.log(sessionId, userId)\n *   return <div>...</div>\n * }\n *\n * @example\n * Basic example in a NextJs app. This page will be fully rendered during SSR:\n *\n * import { useAuth } from '@clerk/nextjs'\n * import { withServerSideAuth } from '@clerk/nextjs/api'\n *\n * export getServerSideProps = withServerSideAuth();\n *\n * export HelloPage = () => {\n *   const { isSignedIn, sessionId, userId } = useAuth();\n *   console.log(isSignedIn, sessionId, userId)\n *   return <div>...</div>\n * }\n */\nexport const useAuth: UseAuth = () => {\n  const { sessionId, userId, actor, orgId, orgRole, orgSlug, orgPermissions } = useAuthContext();\n  const isomorphicClerk = useIsomorphicClerkContext() as unknown as IsomorphicClerk;\n\n  const getToken: GetToken = useCallback(createGetToken(isomorphicClerk), [isomorphicClerk]);\n  const signOut: SignOut = useCallback(createSignOut(isomorphicClerk), [isomorphicClerk]);\n\n  const has = useCallback(\n    (params: Parameters<CheckAuthorizationWithCustomPermissions>[0]) => {\n      if (!params?.permission && !params?.role) {\n        throw new Error(useAuthHasRequiresRoleOrPermission);\n      }\n\n      if (!orgId || !userId || !orgRole || !orgPermissions) {\n        return false;\n      }\n\n      if (params.permission) {\n        return orgPermissions.includes(params.permission);\n      }\n\n      if (params.role) {\n        return orgRole === params.role;\n      }\n\n      return false;\n    },\n    [orgId, orgRole, userId, orgPermissions],\n  );\n\n  if (sessionId === undefined && userId === undefined) {\n    return {\n      isLoaded: false,\n      isSignedIn: undefined,\n      sessionId,\n      userId,\n      actor: undefined,\n      orgId: undefined,\n      orgRole: undefined,\n      orgSlug: undefined,\n      has: undefined,\n      signOut,\n      getToken,\n    };\n  }\n\n  if (sessionId === null && userId === null) {\n    return {\n      isLoaded: true,\n      isSignedIn: false,\n      sessionId,\n      userId,\n      actor: null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken,\n    };\n  }\n\n  if (!!sessionId && !!userId && !!orgId && !!orgRole) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      userId,\n      actor: actor || null,\n      orgId,\n      orgRole,\n      orgSlug: orgSlug || null,\n      has,\n      signOut,\n      getToken,\n    };\n  }\n\n  if (!!sessionId && !!userId && !orgId) {\n    return {\n      isLoaded: true,\n      isSignedIn: true,\n      sessionId,\n      userId,\n      actor: actor || null,\n      orgId: null,\n      orgRole: null,\n      orgSlug: null,\n      has: () => false,\n      signOut,\n      getToken,\n    };\n  }\n\n  throw new Error(invalidStateError);\n};\n"], "mappings": ";AAOA,SAASA,WAAA,QAAmB;AAE5B,SAASC,cAAA,QAAsB;AAC/B,SAASC,yBAAA,QAAiC;AAC1C,SAASC,iBAAA,EAAmBC,kCAAA,QAA0C;AAEtE,SAASC,cAAA,EAAgBC,aAAA,QAAqB;AAoGvC,MAAMC,OAAA,GAAmBA,CAAA,KAAM;EACpC,MAAM;IAAEC,SAAA;IAAWC,MAAA;IAAQC,KAAA;IAAOC,KAAA;IAAOC,OAAA;IAASC,OAAA;IAASC;EAAe,IAAIb,cAAA,CAAe;EAC7F,MAAMc,eAAA,GAAkBb,yBAAA,CAA0B;EAElD,MAAMc,QAAA,GAAqBhB,WAAA,CAAYK,cAAA,CAAeU,eAAe,GAAG,CAACA,eAAe,CAAC;EACzF,MAAME,OAAA,GAAmBjB,WAAA,CAAYM,aAAA,CAAcS,eAAe,GAAG,CAACA,eAAe,CAAC;EAEtF,MAAMG,GAAA,GAAMlB,WAAA,CACTmB,MAAA,IAAmE;IAClE,IAAI,EAACA,MAAA,oBAAAA,MAAA,CAAQC,UAAA,KAAc,EAACD,MAAA,oBAAAA,MAAA,CAAQE,IAAA,GAAM;MACxC,MAAM,IAAIC,KAAA,CAAMlB,kCAAkC;IACpD;IAEA,IAAI,CAACO,KAAA,IAAS,CAACF,MAAA,IAAU,CAACG,OAAA,IAAW,CAACE,cAAA,EAAgB;MACpD,OAAO;IACT;IAEA,IAAIK,MAAA,CAAOC,UAAA,EAAY;MACrB,OAAON,cAAA,CAAeS,QAAA,CAASJ,MAAA,CAAOC,UAAU;IAClD;IAEA,IAAID,MAAA,CAAOE,IAAA,EAAM;MACf,OAAOT,OAAA,KAAYO,MAAA,CAAOE,IAAA;IAC5B;IAEA,OAAO;EACT,GACA,CAACV,KAAA,EAAOC,OAAA,EAASH,MAAA,EAAQK,cAAc,CACzC;EAEA,IAAIN,SAAA,KAAc,UAAaC,MAAA,KAAW,QAAW;IACnD,OAAO;MACLe,QAAA,EAAU;MACVC,UAAA,EAAY;MACZjB,SAAA;MACAC,MAAA;MACAC,KAAA,EAAO;MACPC,KAAA,EAAO;MACPC,OAAA,EAAS;MACTC,OAAA,EAAS;MACTK,GAAA,EAAK;MACLD,OAAA;MACAD;IACF;EACF;EAEA,IAAIR,SAAA,KAAc,QAAQC,MAAA,KAAW,MAAM;IACzC,OAAO;MACLe,QAAA,EAAU;MACVC,UAAA,EAAY;MACZjB,SAAA;MACAC,MAAA;MACAC,KAAA,EAAO;MACPC,KAAA,EAAO;MACPC,OAAA,EAAS;MACTC,OAAA,EAAS;MACTK,GAAA,EAAKA,CAAA,KAAM;MACXD,OAAA;MACAD;IACF;EACF;EAEA,IAAI,CAAC,CAACR,SAAA,IAAa,CAAC,CAACC,MAAA,IAAU,CAAC,CAACE,KAAA,IAAS,CAAC,CAACC,OAAA,EAAS;IACnD,OAAO;MACLY,QAAA,EAAU;MACVC,UAAA,EAAY;MACZjB,SAAA;MACAC,MAAA;MACAC,KAAA,EAAOA,KAAA,IAAS;MAChBC,KAAA;MACAC,OAAA;MACAC,OAAA,EAASA,OAAA,IAAW;MACpBK,GAAA;MACAD,OAAA;MACAD;IACF;EACF;EAEA,IAAI,CAAC,CAACR,SAAA,IAAa,CAAC,CAACC,MAAA,IAAU,CAACE,KAAA,EAAO;IACrC,OAAO;MACLa,QAAA,EAAU;MACVC,UAAA,EAAY;MACZjB,SAAA;MACAC,MAAA;MACAC,KAAA,EAAOA,KAAA,IAAS;MAChBC,KAAA,EAAO;MACPC,OAAA,EAAS;MACTC,OAAA,EAAS;MACTK,GAAA,EAAKA,CAAA,KAAM;MACXD,OAAA;MACAD;IACF;EACF;EAEA,MAAM,IAAIM,KAAA,CAAMnB,iBAAiB;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}