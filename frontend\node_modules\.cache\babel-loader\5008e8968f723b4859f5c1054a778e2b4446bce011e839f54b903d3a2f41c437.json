{"ast": null, "code": "import { __privateAdd, __privateGet, __privateMethod, __privateSet } from \"./chunk-XTU7I5IS.js\";\nvar _loaded, _domain, _proxyUrl, _frontendApi, _publishableKey, _instance, _waitForClerkJS, waitForClerkJS_fn;\nimport { inBrowser } from \"@clerk/shared/browser\";\nimport { deprecated } from \"@clerk/shared/deprecated\";\nimport { handleValueOrFn } from \"@clerk/shared/handleValueOrFn\";\nimport { unsupportedNonBrowserDomainOrProxyUrlFunction } from \"./errors\";\nimport { isConstructor, loadClerkJsScript } from \"./utils\";\nconst _IsomorphicClerk = class _IsomorphicClerk {\n  constructor(options) {\n    __privateAdd(this, _waitForClerkJS);\n    this.clerkjs = null;\n    this.preopenOneTap = null;\n    this.preopenSignIn = null;\n    this.preopenSignUp = null;\n    this.preopenUserProfile = null;\n    this.preopenOrganizationProfile = null;\n    this.preopenCreateOrganization = null;\n    this.premountSignInNodes = /* @__PURE__ */new Map();\n    this.premountSignUpNodes = /* @__PURE__ */new Map();\n    this.premountUserProfileNodes = /* @__PURE__ */new Map();\n    this.premountUserButtonNodes = /* @__PURE__ */new Map();\n    this.premountOrganizationProfileNodes = /* @__PURE__ */new Map();\n    this.premountCreateOrganizationNodes = /* @__PURE__ */new Map();\n    this.premountOrganizationSwitcherNodes = /* @__PURE__ */new Map();\n    this.premountOrganizationListNodes = /* @__PURE__ */new Map();\n    this.premountMethodCalls = /* @__PURE__ */new Map();\n    this.loadedListeners = [];\n    __privateAdd(this, _loaded, false);\n    __privateAdd(this, _domain, void 0);\n    __privateAdd(this, _proxyUrl, void 0);\n    __privateAdd(this, _frontendApi, void 0);\n    __privateAdd(this, _publishableKey, void 0);\n    this.isReady = () => {\n      var _a;\n      return Boolean((_a = this.clerkjs) == null ? void 0 : _a.isReady());\n    };\n    this.buildSignInUrl = opts => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildSignInUrl(opts)) || \"\";\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildSignInUrl\", callback);\n      }\n    };\n    this.buildSignUpUrl = opts => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildSignUpUrl(opts)) || \"\";\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildSignUpUrl\", callback);\n      }\n    };\n    this.buildUserProfileUrl = () => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildUserProfileUrl()) || \"\";\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildUserProfileUrl\", callback);\n      }\n    };\n    this.buildCreateOrganizationUrl = () => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildCreateOrganizationUrl()) || \"\";\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildCreateOrganizationUrl\", callback);\n      }\n    };\n    this.buildOrganizationProfileUrl = () => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildOrganizationProfileUrl()) || \"\";\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildOrganizationProfileUrl\", callback);\n      }\n    };\n    this.buildHomeUrl = () => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildHomeUrl()) || \"\";\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildHomeUrl\", callback);\n      }\n    };\n    this.buildUrlWithAuth = to => {\n      const callback = () => {\n        var _a;\n        return ((_a = this.clerkjs) == null ? void 0 : _a.buildUrlWithAuth(to)) || \"\";\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"buildUrlWithAuth\", callback);\n      }\n    };\n    this.handleUnauthenticated = () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.handleUnauthenticated();\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        void callback();\n      } else {\n        this.premountMethodCalls.set(\"handleUnauthenticated\", callback);\n      }\n    };\n    this.addOnLoaded = cb => {\n      this.loadedListeners.push(cb);\n      if (this.loaded) {\n        this.emitLoaded();\n      }\n    };\n    this.emitLoaded = () => {\n      this.loadedListeners.forEach(cb => cb());\n      this.loadedListeners = [];\n    };\n    this.hydrateClerkJS = clerkjs => {\n      if (!clerkjs) {\n        throw new Error(\"Failed to hydrate latest Clerk JS\");\n      }\n      this.clerkjs = clerkjs;\n      this.premountMethodCalls.forEach(cb => cb());\n      if (this.preopenSignIn !== null) {\n        clerkjs.openSignIn(this.preopenSignIn);\n      }\n      if (this.preopenSignUp !== null) {\n        clerkjs.openSignUp(this.preopenSignUp);\n      }\n      if (this.preopenUserProfile !== null) {\n        clerkjs.openUserProfile(this.preopenUserProfile);\n      }\n      if (this.preopenOneTap !== null) {\n        clerkjs.openGoogleOneTap(this.preopenOneTap);\n      }\n      if (this.preopenOrganizationProfile !== null) {\n        clerkjs.openOrganizationProfile(this.preopenOrganizationProfile);\n      }\n      if (this.preopenCreateOrganization !== null) {\n        clerkjs.openCreateOrganization(this.preopenCreateOrganization);\n      }\n      this.premountSignInNodes.forEach((props, node) => {\n        clerkjs.mountSignIn(node, props);\n      });\n      this.premountSignUpNodes.forEach((props, node) => {\n        clerkjs.mountSignUp(node, props);\n      });\n      this.premountUserProfileNodes.forEach((props, node) => {\n        clerkjs.mountUserProfile(node, props);\n      });\n      this.premountUserButtonNodes.forEach((props, node) => {\n        clerkjs.mountUserButton(node, props);\n      });\n      this.premountOrganizationListNodes.forEach((props, node) => {\n        clerkjs.mountOrganizationList(node, props);\n      });\n      __privateSet(this, _loaded, true);\n      this.emitLoaded();\n      return this.clerkjs;\n    };\n    this.__unstable__updateProps = props => {\n      if (this.clerkjs && \"__unstable__updateProps\" in this.clerkjs) {\n        this.clerkjs.__unstable__updateProps(props);\n      } else {\n        return void 0;\n      }\n    };\n    /**\n     * `setActive` can be used to set the active session and/or organization.\n     */\n    this.setActive = ({\n      session,\n      organization,\n      beforeEmit\n    }) => {\n      if (this.clerkjs) {\n        return this.clerkjs.setActive({\n          session,\n          organization,\n          beforeEmit\n        });\n      } else {\n        return Promise.reject();\n      }\n    };\n    this.setSession = (session, beforeEmit) => {\n      deprecated(\"setSession\", \"Use `Clerk.setActive` instead\");\n      return this.setActive({\n        session,\n        beforeEmit\n      });\n    };\n    this.openSignIn = props => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.openSignIn(props);\n      } else {\n        this.preopenSignIn = props;\n      }\n    };\n    this.closeSignIn = () => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.closeSignIn();\n      } else {\n        this.preopenSignIn = null;\n      }\n    };\n    this.openGoogleOneTap = props => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.openGoogleOneTap(props);\n      } else {\n        this.preopenOneTap = props;\n      }\n    };\n    this.closeGoogleOneTap = () => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.closeGoogleOneTap();\n      } else {\n        this.preopenOneTap = null;\n      }\n    };\n    this.openUserProfile = props => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.openUserProfile(props);\n      } else {\n        this.preopenUserProfile = props;\n      }\n    };\n    this.closeUserProfile = () => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.closeUserProfile();\n      } else {\n        this.preopenUserProfile = null;\n      }\n    };\n    this.openOrganizationProfile = props => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.openOrganizationProfile(props);\n      } else {\n        this.preopenOrganizationProfile = props;\n      }\n    };\n    this.closeOrganizationProfile = () => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.closeOrganizationProfile();\n      } else {\n        this.preopenOrganizationProfile = null;\n      }\n    };\n    this.openCreateOrganization = props => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.openCreateOrganization(props);\n      } else {\n        this.preopenCreateOrganization = props;\n      }\n    };\n    this.closeCreateOrganization = () => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.closeCreateOrganization();\n      } else {\n        this.preopenCreateOrganization = null;\n      }\n    };\n    this.openSignUp = props => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.openSignUp(props);\n      } else {\n        this.preopenSignUp = props;\n      }\n    };\n    this.closeSignUp = () => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.closeSignUp();\n      } else {\n        this.preopenSignUp = null;\n      }\n    };\n    this.mountSignIn = (node, props) => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.mountSignIn(node, props);\n      } else {\n        this.premountSignInNodes.set(node, props);\n      }\n    };\n    this.unmountSignIn = node => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.unmountSignIn(node);\n      } else {\n        this.premountSignInNodes.delete(node);\n      }\n    };\n    this.mountSignUp = (node, props) => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.mountSignUp(node, props);\n      } else {\n        this.premountSignUpNodes.set(node, props);\n      }\n    };\n    this.unmountSignUp = node => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.unmountSignUp(node);\n      } else {\n        this.premountSignUpNodes.delete(node);\n      }\n    };\n    this.mountUserProfile = (node, props) => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.mountUserProfile(node, props);\n      } else {\n        this.premountUserProfileNodes.set(node, props);\n      }\n    };\n    this.unmountUserProfile = node => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.unmountUserProfile(node);\n      } else {\n        this.premountUserProfileNodes.delete(node);\n      }\n    };\n    this.mountOrganizationProfile = (node, props) => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.mountOrganizationProfile(node, props);\n      } else {\n        this.premountOrganizationProfileNodes.set(node, props);\n      }\n    };\n    this.unmountOrganizationProfile = node => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.unmountOrganizationProfile(node);\n      } else {\n        this.premountOrganizationProfileNodes.delete(node);\n      }\n    };\n    this.mountCreateOrganization = (node, props) => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.mountCreateOrganization(node, props);\n      } else {\n        this.premountCreateOrganizationNodes.set(node, props);\n      }\n    };\n    this.unmountCreateOrganization = node => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.unmountCreateOrganization(node);\n      } else {\n        this.premountCreateOrganizationNodes.delete(node);\n      }\n    };\n    this.mountOrganizationSwitcher = (node, props) => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.mountOrganizationSwitcher(node, props);\n      } else {\n        this.premountOrganizationSwitcherNodes.set(node, props);\n      }\n    };\n    this.unmountOrganizationSwitcher = node => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.unmountOrganizationSwitcher(node);\n      } else {\n        this.premountOrganizationSwitcherNodes.delete(node);\n      }\n    };\n    this.mountOrganizationList = (node, props) => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.mountOrganizationList(node, props);\n      } else {\n        this.premountOrganizationListNodes.set(node, props);\n      }\n    };\n    this.unmountOrganizationList = node => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.unmountOrganizationList(node);\n      } else {\n        this.premountOrganizationListNodes.delete(node);\n      }\n    };\n    this.mountUserButton = (node, userButtonProps) => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.mountUserButton(node, userButtonProps);\n      } else {\n        this.premountUserButtonNodes.set(node, userButtonProps);\n      }\n    };\n    this.unmountUserButton = node => {\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        this.clerkjs.unmountUserButton(node);\n      } else {\n        this.premountUserButtonNodes.delete(node);\n      }\n    };\n    this.addListener = listener => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.addListener(listener);\n      };\n      if (this.clerkjs) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"addListener\", callback);\n        return () => this.premountMethodCalls.delete(\"addListener\");\n      }\n    };\n    this.navigate = to => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.navigate(to);\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        void callback();\n      } else {\n        this.premountMethodCalls.set(\"navigate\", callback);\n      }\n    };\n    this.redirectWithAuth = (...args) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectWithAuth(...args);\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        void callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectWithAuth\", callback);\n      }\n    };\n    this.redirectToSignIn = opts => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToSignIn(opts);\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        void callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToSignIn\", callback);\n      }\n    };\n    this.redirectToSignUp = opts => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToSignUp(opts);\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        void callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToSignUp\", callback);\n      }\n    };\n    this.redirectToUserProfile = () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToUserProfile();\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToUserProfile\", callback);\n      }\n    };\n    this.redirectToHome = () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToHome();\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToHome\", callback);\n      }\n    };\n    this.redirectToOrganizationProfile = () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToOrganizationProfile();\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToOrganizationProfile\", callback);\n      }\n    };\n    this.redirectToCreateOrganization = () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.redirectToCreateOrganization();\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        callback();\n      } else {\n        this.premountMethodCalls.set(\"redirectToCreateOrganization\", callback);\n      }\n    };\n    this.handleRedirectCallback = params => {\n      var _a;\n      const callback = () => {\n        var _a2;\n        return (_a2 = this.clerkjs) == null ? void 0 : _a2.handleRedirectCallback(params);\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        void ((_a = callback()) == null ? void 0 : _a.catch(() => {}));\n      } else {\n        this.premountMethodCalls.set(\"handleRedirectCallback\", callback);\n      }\n    };\n    /**\n     * @deprecated Use `handleEmailLinkVerification` instead.\n     */\n    this.handleMagicLinkVerification = async params => {\n      deprecated(\"handleMagicLinkVerification\", \"Use `handleEmailLinkVerification` instead.\");\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.handleMagicLinkVerification(params);\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"handleMagicLinkVerification\", callback);\n      }\n    };\n    this.handleGoogleOneTapCallback = (signInOrUp, params) => {\n      var _a;\n      const callback = () => {\n        var _a2;\n        return (_a2 = this.clerkjs) == null ? void 0 : _a2.handleGoogleOneTapCallback(signInOrUp, params);\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        void ((_a = callback()) == null ? void 0 : _a.catch(() => {}));\n      } else {\n        this.premountMethodCalls.set(\"handleGoogleOneTapCallback\", callback);\n      }\n    };\n    this.handleEmailLinkVerification = async params => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.handleEmailLinkVerification(params);\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"handleEmailLinkVerification\", callback);\n      }\n    };\n    this.authenticateWithMetamask = async params => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.authenticateWithMetamask(params);\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"authenticateWithMetamask\", callback);\n      }\n    };\n    this.authenticateWithGoogleOneTap = async params => {\n      const clerkjs = await __privateMethod(this, _waitForClerkJS, waitForClerkJS_fn).call(this);\n      return clerkjs.authenticateWithGoogleOneTap(params);\n    };\n    this.createOrganization = async params => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.createOrganization(params);\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"createOrganization\", callback);\n      }\n    };\n    this.getOrganizationMemberships = async () => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.getOrganizationMemberships();\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"getOrganizationMemberships\", callback);\n      }\n    };\n    this.getOrganization = async organizationId => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.getOrganization(organizationId);\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"getOrganization\", callback);\n      }\n    };\n    this.signOut = async (signOutCallbackOrOptions, options) => {\n      const callback = () => {\n        var _a;\n        return (_a = this.clerkjs) == null ? void 0 : _a.signOut(signOutCallbackOrOptions, options);\n      };\n      if (this.clerkjs && __privateGet(this, _loaded)) {\n        return callback();\n      } else {\n        this.premountMethodCalls.set(\"signOut\", callback);\n      }\n    };\n    const {\n      Clerk = null,\n      frontendApi,\n      publishableKey\n    } = options || {};\n    __privateSet(this, _frontendApi, frontendApi);\n    __privateSet(this, _publishableKey, publishableKey);\n    __privateSet(this, _proxyUrl, options == null ? void 0 : options.proxyUrl);\n    __privateSet(this, _domain, options == null ? void 0 : options.domain);\n    this.options = options;\n    this.Clerk = Clerk;\n    this.mode = inBrowser() ? \"browser\" : \"server\";\n    void this.loadClerkJS();\n  }\n  get publishableKey() {\n    return __privateGet(this, _publishableKey);\n  }\n  get loaded() {\n    return __privateGet(this, _loaded);\n  }\n  static getOrCreateInstance(options) {\n    if (!inBrowser() || !__privateGet(this, _instance) || options.Clerk && __privateGet(this, _instance).Clerk !== options.Clerk) {\n      __privateSet(this, _instance, new _IsomorphicClerk(options));\n    }\n    return __privateGet(this, _instance);\n  }\n  static clearInstance() {\n    __privateSet(this, _instance, null);\n  }\n  get domain() {\n    if (typeof window !== \"undefined\" && window.location) {\n      return handleValueOrFn(__privateGet(this, _domain), new URL(window.location.href), \"\");\n    }\n    if (typeof __privateGet(this, _domain) === \"function\") {\n      throw new Error(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return __privateGet(this, _domain) || \"\";\n  }\n  get proxyUrl() {\n    if (typeof window !== \"undefined\" && window.location) {\n      return handleValueOrFn(__privateGet(this, _proxyUrl), new URL(window.location.href), \"\");\n    }\n    if (typeof __privateGet(this, _proxyUrl) === \"function\") {\n      throw new Error(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return __privateGet(this, _proxyUrl) || \"\";\n  }\n  get sdkMetadata() {\n    var _a;\n    return ((_a = this.clerkjs) == null ? void 0 : _a.sdkMetadata) || this.options.sdkMetadata || void 0;\n  }\n  get instanceType() {\n    var _a;\n    return (_a = this.clerkjs) == null ? void 0 : _a.instanceType;\n  }\n  get frontendApi() {\n    var _a;\n    return ((_a = this.clerkjs) == null ? void 0 : _a.frontendApi) || __privateGet(this, _frontendApi) || \"\";\n  }\n  get isStandardBrowser() {\n    var _a;\n    return ((_a = this.clerkjs) == null ? void 0 : _a.isStandardBrowser) || this.options.standardBrowser || false;\n  }\n  get isSatellite() {\n    if (typeof window !== \"undefined\" && window.location) {\n      return handleValueOrFn(this.options.isSatellite, new URL(window.location.href), false);\n    }\n    if (typeof this.options.isSatellite === \"function\") {\n      throw new Error(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return false;\n  }\n  async loadClerkJS() {\n    var _a, _b, _c;\n    if (this.mode !== \"browser\" || __privateGet(this, _loaded)) {\n      return;\n    }\n    if (typeof window !== \"undefined\") {\n      window.__clerk_frontend_api = this.frontendApi;\n      window.__clerk_publishable_key = this.publishableKey;\n      window.__clerk_proxy_url = this.proxyUrl;\n      window.__clerk_domain = this.domain;\n    }\n    try {\n      if (this.Clerk) {\n        let c;\n        if (isConstructor(this.Clerk)) {\n          c = new this.Clerk(this.publishableKey || this.frontendApi || \"\", {\n            proxyUrl: this.proxyUrl,\n            domain: this.domain\n          });\n          await c.load(this.options);\n        } else {\n          c = this.Clerk;\n          if (!c.isReady()) {\n            await c.load(this.options);\n          }\n        }\n        global.Clerk = c;\n      } else {\n        if (!global.Clerk) {\n          await loadClerkJsScript({\n            ...this.options,\n            frontendApi: this.frontendApi,\n            publishableKey: this.publishableKey,\n            proxyUrl: this.proxyUrl,\n            domain: this.domain\n          });\n        }\n        if (!global.Clerk) {\n          throw new Error(\"Failed to download latest ClerkJS. Contact <EMAIL>.\");\n        }\n        await global.Clerk.load(this.options);\n      }\n      global.Clerk.sdkMetadata = (_a = this.options.sdkMetadata) != null ? _a : {\n        name: \"@clerk/clerk-react\",\n        version: \"4.32.5\"\n      };\n      if (((_b = global.Clerk) == null ? void 0 : _b.loaded) || ((_c = global.Clerk) == null ? void 0 : _c.isReady())) {\n        return this.hydrateClerkJS(global.Clerk);\n      }\n      return;\n    } catch (err) {\n      const error = err;\n      if (process.env.NODE_ENV === \"production\") {\n        console.error(error.stack || error.message || error);\n      } else {\n        throw err;\n      }\n      return;\n    }\n  }\n  get version() {\n    var _a;\n    return (_a = this.clerkjs) == null ? void 0 : _a.version;\n  }\n  get client() {\n    if (this.clerkjs) {\n      return this.clerkjs.client;\n    } else {\n      return void 0;\n    }\n  }\n  get session() {\n    if (this.clerkjs) {\n      return this.clerkjs.session;\n    } else {\n      return void 0;\n    }\n  }\n  get user() {\n    if (this.clerkjs) {\n      return this.clerkjs.user;\n    } else {\n      return void 0;\n    }\n  }\n  get organization() {\n    if (this.clerkjs) {\n      return this.clerkjs.organization;\n    } else {\n      return void 0;\n    }\n  }\n  get __unstable__environment() {\n    if (this.clerkjs) {\n      return this.clerkjs.__unstable__environment;\n    } else {\n      return void 0;\n    }\n  }\n  __unstable__setEnvironment(...args) {\n    if (this.clerkjs && \"__unstable__setEnvironment\" in this.clerkjs) {\n      this.clerkjs.__unstable__setEnvironment(args);\n    } else {\n      return void 0;\n    }\n  }\n};\n_loaded = new WeakMap();\n_domain = new WeakMap();\n_proxyUrl = new WeakMap();\n_frontendApi = new WeakMap();\n_publishableKey = new WeakMap();\n_instance = new WeakMap();\n_waitForClerkJS = new WeakSet();\nwaitForClerkJS_fn = function () {\n  return new Promise(resolve => {\n    if (__privateGet(this, _loaded)) {\n      resolve(this.clerkjs);\n    }\n    this.addOnLoaded(() => resolve(this.clerkjs));\n  });\n};\n__privateAdd(_IsomorphicClerk, _instance, void 0);\nlet IsomorphicClerk = _IsomorphicClerk;\nexport { IsomorphicClerk as default };", "map": {"version": 3, "names": ["_loaded", "_domain", "_proxyUrl", "_frontendApi", "_publishableKey", "_instance", "_waitForClerkJS", "waitForClerkJS_fn", "inBrowser", "deprecated", "handleValueOrFn", "unsupportedNonBrowserDomainOrProxyUrlFunction", "isConstructor", "loadClerkJsScript", "_IsomorphicClerk", "constructor", "options", "__privateAdd", "clerkjs", "preopenOneTap", "preopenSignIn", "preopenSignUp", "preopenUserProfile", "preopenOrganizationProfile", "preopenCreateOrganization", "premountSignInNodes", "Map", "premountSignUpNodes", "premountUserProfileNodes", "premountUserButtonNodes", "premountOrganizationProfileNodes", "premountCreateOrganizationNodes", "premountOrganizationSwitcherNodes", "premountOrganizationListNodes", "premountMethodCalls", "loadedListeners", "isReady", "_a", "Boolean", "buildSignInUrl", "opts", "callback", "__privateGet", "set", "buildSignUpUrl", "buildUserProfileUrl", "buildCreateOrganizationUrl", "buildOrganizationProfileUrl", "buildHomeUrl", "buildUrlWithAuth", "to", "handleUnauthenticated", "addOnLoaded", "cb", "push", "loaded", "emitLoaded", "for<PERSON>ach", "hydrateClerkJS", "Error", "openSignIn", "openSignUp", "openUserProfile", "openGoogleOneTap", "openOrganizationProfile", "openCreateOrganization", "props", "node", "mountSignIn", "mountSignUp", "mountUserProfile", "mountUserButton", "mountOrganizationList", "__privateSet", "__unstable__updateProps", "setActive", "session", "organization", "beforeEmit", "Promise", "reject", "setSession", "closeSignIn", "closeGoogleOneTap", "closeUserProfile", "closeOrganizationProfile", "closeCreateOrganization", "closeSignUp", "unmountSignIn", "delete", "unmountSignUp", "unmountUserProfile", "mountOrganizationProfile", "unmountOrganizationProfile", "mountCreateOrganization", "unmountCreateOrganization", "mountOrganizationSwitcher", "unmountOrganizationSwitcher", "unmountOrganizationList", "userButtonProps", "unmountUser<PERSON><PERSON><PERSON>", "addListener", "listener", "navigate", "redirectWithAuth", "args", "redirectToSignIn", "redirectToSignUp", "redirectToUserProfile", "redirectToHome", "redirectToOrganizationProfile", "redirectToCreateOrganization", "handleRedirectCallback", "params", "_a2", "catch", "handleMagicLinkVerification", "handleGoogleOneTapCallback", "signInOrUp", "handleEmailLinkVerification", "authenticateWithMetamask", "authenticateWithGoogleOneTap", "__privateMethod", "call", "createOrganization", "getOrganizationMemberships", "getOrganization", "organizationId", "signOut", "signOutCallbackOrOptions", "Clerk", "frontendApi", "publishableKey", "proxyUrl", "domain", "mode", "loadClerkJS", "getOrCreateInstance", "clearInstance", "window", "location", "URL", "href", "sdkMetadata", "instanceType", "is<PERSON><PERSON><PERSON>dB<PERSON><PERSON>", "standardBrowser", "isSatellite", "_b", "_c", "__clerk_frontend_api", "__clerk_publishable_key", "__clerk_proxy_url", "__clerk_domain", "c", "load", "global", "name", "version", "err", "error", "process", "env", "NODE_ENV", "console", "stack", "message", "client", "user", "__unstable__environment", "__unstable__setEnvironment", "WeakMap", "WeakSet", "resolve", "IsomorphicClerk"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\isomorphicClerk.ts"], "sourcesContent": ["import { inBrowser } from '@clerk/shared/browser';\nimport { deprecated } from '@clerk/shared/deprecated';\nimport { handleValueOrFn } from '@clerk/shared/handleValueOrFn';\nimport type {\n  ActiveSessionResource,\n  AuthenticateWithGoogleOneTapParams,\n  AuthenticateWithMetamaskParams,\n  BeforeEmitCallback,\n  Clerk,\n  ClientResource,\n  CreateOrganizationParams,\n  CreateOrganizationProps,\n  DomainOrProxyUrl,\n  GoogleOneTapProps,\n  HandleEmailLinkVerificationParams,\n  HandleMagicLinkVerificationParams,\n  HandleOAuthCallbackParams,\n  InstanceType,\n  ListenerCallback,\n  LoadedClerk,\n  OrganizationListProps,\n  OrganizationMembershipResource,\n  OrganizationProfileProps,\n  OrganizationResource,\n  OrganizationSwitcherProps,\n  RedirectOptions,\n  SDKMetadata,\n  SetActiveParams,\n  SignInProps,\n  SignInRedirectOptions,\n  SignInResource,\n  SignOut,\n  SignOutCallback,\n  SignOutOptions,\n  SignUpProps,\n  SignUpRedirectOptions,\n  SignUpResource,\n  UnsubscribeCallback,\n  UserButtonProps,\n  UserProfileProps,\n  UserResource,\n} from '@clerk/types';\n\nimport { unsupportedNonBrowserDomainOrProxyUrlFunction } from './errors';\nimport type {\n  BrowserClerk,\n  BrowserClerkConstructor,\n  ClerkProp,\n  HeadlessBrowserClerk,\n  HeadlessBrowserClerkConstrutor,\n  IsomorphicClerkOptions,\n} from './types';\nimport { isConstructor, loadClerkJsScript } from './utils';\n\nexport interface Global {\n  Clerk?: HeadlessBrowserClerk | BrowserClerk;\n}\n\ndeclare const global: Global;\n\ntype GenericFunction<TArgs = never> = (...args: TArgs[]) => unknown;\n\ntype MethodName<T> = {\n  [P in keyof T]: T[P] extends GenericFunction ? P : never;\n}[keyof T];\n\ntype MethodCallback = () => Promise<unknown> | unknown;\n\ntype IsomorphicLoadedClerk = Omit<\n  LoadedClerk,\n  /**\n   * Override ClerkJS methods in order to support premountMethodCalls\n   */\n  | 'buildSignInUrl'\n  | 'buildSignUpUrl'\n  | 'buildUserProfileUrl'\n  | 'buildCreateOrganizationUrl'\n  | 'buildOrganizationProfileUrl'\n  | 'buildHomeUrl'\n  | 'buildUrlWithAuth'\n  | 'redirectWithAuth'\n  | 'redirectToSignIn'\n  | 'redirectToSignUp'\n  | 'handleRedirectCallback'\n  | 'handleGoogleOneTapCallback'\n  | 'handleUnauthenticated'\n  | 'authenticateWithMetamask'\n  | 'authenticateWithGoogleOneTap'\n  | 'createOrganization'\n  | 'getOrganization'\n  | 'mountUserButton'\n  | 'mountOrganizationList'\n  | 'mountOrganizationSwitcher'\n  | 'mountOrganizationProfile'\n  | 'mountCreateOrganization'\n  | 'mountSignUp'\n  | 'mountSignIn'\n  | 'mountUserProfile'\n  | 'client'\n  | 'getOrganizationMemberships'\n> & {\n  // TODO: Align return type\n  redirectWithAuth: (...args: Parameters<Clerk['redirectWithAuth']>) => void;\n  // TODO: Align return type\n  redirectToSignIn: (options: SignInRedirectOptions) => void;\n  // TODO: Align return type\n  redirectToSignUp: (options: SignUpRedirectOptions) => void;\n  // TODO: Align return type and parms\n  handleRedirectCallback: (params: HandleOAuthCallbackParams) => void;\n  handleGoogleOneTapCallback: (signInOrUp: SignInResource | SignUpResource, params: HandleOAuthCallbackParams) => void;\n  handleUnauthenticated: () => void;\n  // TODO: Align Promise unknown\n  authenticateWithMetamask: (params: AuthenticateWithMetamaskParams) => Promise<void>;\n  authenticateWithGoogleOneTap: (\n    params: AuthenticateWithGoogleOneTapParams,\n  ) => Promise<SignInResource | SignUpResource>;\n  // TODO: Align return type (maybe not possible or correct)\n  createOrganization: (params: CreateOrganizationParams) => Promise<OrganizationResource | void>;\n  // TODO: Align return type (maybe not possible or correct)\n  getOrganization: (organizationId: string) => Promise<OrganizationResource | void>;\n\n  // TODO: Align return type\n  buildSignInUrl: (opts?: RedirectOptions) => string | void;\n  // TODO: Align return type\n  buildSignUpUrl: (opts?: RedirectOptions) => string | void;\n  // TODO: Align return type\n  buildUserProfileUrl: () => string | void;\n  // TODO: Align return type\n  buildCreateOrganizationUrl: () => string | void;\n  // TODO: Align return type\n  buildOrganizationProfileUrl: () => string | void;\n  // TODO: Align return type\n  buildHomeUrl: () => string | void;\n  // TODO: Align return type\n  buildUrlWithAuth: (to: string) => string | void;\n\n  // TODO: Align optional props\n  mountUserButton: (node: HTMLDivElement, props: UserButtonProps) => void;\n  mountOrganizationList: (node: HTMLDivElement, props: OrganizationListProps) => void;\n  mountOrganizationSwitcher: (node: HTMLDivElement, props: OrganizationSwitcherProps) => void;\n  mountOrganizationProfile: (node: HTMLDivElement, props: OrganizationProfileProps) => void;\n  mountCreateOrganization: (node: HTMLDivElement, props: CreateOrganizationProps) => void;\n  mountSignUp: (node: HTMLDivElement, props: SignUpProps) => void;\n  mountSignIn: (node: HTMLDivElement, props: SignInProps) => void;\n  mountUserProfile: (node: HTMLDivElement, props: UserProfileProps) => void;\n  client: ClientResource | undefined;\n\n  getOrganizationMemberships: () => Promise<OrganizationMembershipResource[] | void>;\n};\n\nexport default class IsomorphicClerk implements IsomorphicLoadedClerk {\n  private readonly mode: 'browser' | 'server';\n  private readonly options: IsomorphicClerkOptions;\n  private readonly Clerk: ClerkProp;\n  private clerkjs: BrowserClerk | HeadlessBrowserClerk | null = null;\n  private preopenOneTap?: null | GoogleOneTapProps = null;\n  private preopenSignIn?: null | SignInProps = null;\n  private preopenSignUp?: null | SignUpProps = null;\n  private preopenUserProfile?: null | UserProfileProps = null;\n  private preopenOrganizationProfile?: null | OrganizationProfileProps = null;\n  private preopenCreateOrganization?: null | CreateOrganizationProps = null;\n  private premountSignInNodes = new Map<HTMLDivElement, SignInProps>();\n  private premountSignUpNodes = new Map<HTMLDivElement, SignUpProps>();\n  private premountUserProfileNodes = new Map<HTMLDivElement, UserProfileProps>();\n  private premountUserButtonNodes = new Map<HTMLDivElement, UserButtonProps>();\n  private premountOrganizationProfileNodes = new Map<HTMLDivElement, OrganizationProfileProps>();\n  private premountCreateOrganizationNodes = new Map<HTMLDivElement, CreateOrganizationProps>();\n  private premountOrganizationSwitcherNodes = new Map<HTMLDivElement, OrganizationSwitcherProps>();\n  private premountOrganizationListNodes = new Map<HTMLDivElement, OrganizationListProps>();\n  private premountMethodCalls = new Map<MethodName<BrowserClerk>, MethodCallback>();\n  private loadedListeners: Array<() => void> = [];\n\n  #loaded = false;\n  #domain: DomainOrProxyUrl['domain'];\n  #proxyUrl: DomainOrProxyUrl['proxyUrl'];\n  #frontendApi: string | undefined;\n  #publishableKey: string | undefined;\n\n  get publishableKey(): string | undefined {\n    return this.#publishableKey;\n  }\n\n  get loaded(): boolean {\n    return this.#loaded;\n  }\n\n  static #instance: IsomorphicClerk | null | undefined;\n\n  static getOrCreateInstance(options: IsomorphicClerkOptions) {\n    // During SSR: a new instance should be created for every request\n    // During CSR: use the cached instance for the whole lifetime of the app\n    // Also will recreate the instance if the provided Clerk instance changes\n    // This method should be idempotent in both scenarios\n    if (!inBrowser() || !this.#instance || (options.Clerk && this.#instance.Clerk !== options.Clerk)) {\n      this.#instance = new IsomorphicClerk(options);\n    }\n    return this.#instance;\n  }\n\n  static clearInstance() {\n    this.#instance = null;\n  }\n\n  get domain(): string {\n    // This getter can run in environments where window is not available.\n    // In those cases we should expect and use domain as a string\n    if (typeof window !== 'undefined' && window.location) {\n      return handleValueOrFn(this.#domain, new URL(window.location.href), '');\n    }\n    if (typeof this.#domain === 'function') {\n      throw new Error(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return this.#domain || '';\n  }\n\n  get proxyUrl(): string {\n    // This getter can run in environments where window is not available.\n    // In those cases we should expect and use proxy as a string\n    if (typeof window !== 'undefined' && window.location) {\n      return handleValueOrFn(this.#proxyUrl, new URL(window.location.href), '');\n    }\n    if (typeof this.#proxyUrl === 'function') {\n      throw new Error(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return this.#proxyUrl || '';\n  }\n\n  constructor(options: IsomorphicClerkOptions) {\n    const { Clerk = null, frontendApi, publishableKey } = options || {};\n    this.#frontendApi = frontendApi;\n    this.#publishableKey = publishableKey;\n    this.#proxyUrl = options?.proxyUrl;\n    this.#domain = options?.domain;\n    this.options = options;\n    this.Clerk = Clerk;\n    this.mode = inBrowser() ? 'browser' : 'server';\n    void this.loadClerkJS();\n  }\n\n  get sdkMetadata(): SDKMetadata | undefined {\n    return this.clerkjs?.sdkMetadata || this.options.sdkMetadata || undefined;\n  }\n\n  get instanceType(): InstanceType | undefined {\n    return this.clerkjs?.instanceType;\n  }\n\n  get frontendApi(): string {\n    return this.clerkjs?.frontendApi || this.#frontendApi || '';\n  }\n\n  get isStandardBrowser(): boolean {\n    return this.clerkjs?.isStandardBrowser || this.options.standardBrowser || false;\n  }\n\n  get isSatellite(): boolean {\n    // This getter can run in environments where window is not available.\n    // In those cases we should expect and use domain as a string\n    if (typeof window !== 'undefined' && window.location) {\n      return handleValueOrFn(this.options.isSatellite, new URL(window.location.href), false);\n    }\n    if (typeof this.options.isSatellite === 'function') {\n      throw new Error(unsupportedNonBrowserDomainOrProxyUrlFunction);\n    }\n    return false;\n  }\n\n  isReady = (): boolean => Boolean(this.clerkjs?.isReady());\n\n  buildSignInUrl = (opts?: RedirectOptions): string | void => {\n    const callback = () => this.clerkjs?.buildSignInUrl(opts) || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildSignInUrl', callback);\n    }\n  };\n\n  buildSignUpUrl = (opts?: RedirectOptions): string | void => {\n    const callback = () => this.clerkjs?.buildSignUpUrl(opts) || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildSignUpUrl', callback);\n    }\n  };\n\n  buildUserProfileUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildUserProfileUrl() || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildUserProfileUrl', callback);\n    }\n  };\n\n  buildCreateOrganizationUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildCreateOrganizationUrl() || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildCreateOrganizationUrl', callback);\n    }\n  };\n\n  buildOrganizationProfileUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildOrganizationProfileUrl() || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildOrganizationProfileUrl', callback);\n    }\n  };\n\n  buildHomeUrl = (): string | void => {\n    const callback = () => this.clerkjs?.buildHomeUrl() || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildHomeUrl', callback);\n    }\n  };\n\n  buildUrlWithAuth = (to: string): string | void => {\n    const callback = () => this.clerkjs?.buildUrlWithAuth(to) || '';\n    if (this.clerkjs && this.#loaded) {\n      return callback();\n    } else {\n      this.premountMethodCalls.set('buildUrlWithAuth', callback);\n    }\n  };\n\n  handleUnauthenticated = (): void => {\n    const callback = () => this.clerkjs?.handleUnauthenticated();\n    if (this.clerkjs && this.#loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('handleUnauthenticated', callback);\n    }\n  };\n\n  #waitForClerkJS(): Promise<HeadlessBrowserClerk | BrowserClerk> {\n    return new Promise<HeadlessBrowserClerk | BrowserClerk>(resolve => {\n      if (this.#loaded) {\n        resolve(this.clerkjs!);\n      }\n      this.addOnLoaded(() => resolve(this.clerkjs!));\n    });\n  }\n\n  async loadClerkJS(): Promise<HeadlessBrowserClerk | BrowserClerk | undefined> {\n    if (this.mode !== 'browser' || this.#loaded) {\n      return;\n    }\n\n    // Store frontendAPI value on window as a fallback. This value can be used as a\n    // fallback during ClerkJS hot loading in case ClerkJS fails to find the\n    // \"data-clerk-frontend-api\" attribute on its script tag.\n\n    // This can happen when the DOM is altered completely during client rehydration.\n    // For example, in Remix with React 18 the document changes completely via `hydrateRoot(document)`.\n\n    // For more information refer to:\n    // - https://github.com/remix-run/remix/issues/2947\n    // - https://github.com/facebook/react/issues/24430\n    if (typeof window !== 'undefined') {\n      window.__clerk_frontend_api = this.frontendApi;\n      window.__clerk_publishable_key = this.publishableKey;\n      window.__clerk_proxy_url = this.proxyUrl;\n      window.__clerk_domain = this.domain;\n    }\n\n    try {\n      if (this.Clerk) {\n        // Set a fixed Clerk version\n        let c: ClerkProp;\n\n        if (isConstructor<BrowserClerkConstructor | HeadlessBrowserClerkConstrutor>(this.Clerk)) {\n          // Construct a new Clerk object if a constructor is passed\n          c = new this.Clerk(this.publishableKey || this.frontendApi || '', {\n            proxyUrl: this.proxyUrl,\n            domain: this.domain,\n          } as any);\n          await c.load(this.options);\n        } else {\n          // Otherwise use the instantiated Clerk object\n          c = this.Clerk;\n\n          if (!c.isReady()) {\n            await c.load(this.options);\n          }\n        }\n\n        global.Clerk = c;\n      } else {\n        // Hot-load latest ClerkJS from Clerk CDN\n        if (!global.Clerk) {\n          await loadClerkJsScript({\n            ...this.options,\n            frontendApi: this.frontendApi,\n            publishableKey: this.publishableKey,\n            proxyUrl: this.proxyUrl,\n            domain: this.domain,\n          });\n        }\n\n        if (!global.Clerk) {\n          throw new Error('Failed to download latest ClerkJS. Contact <EMAIL>.');\n        }\n\n        await global.Clerk.load(this.options);\n      }\n\n      global.Clerk.sdkMetadata = this.options.sdkMetadata ?? { name: PACKAGE_NAME, version: PACKAGE_VERSION };\n\n      if (global.Clerk?.loaded || global.Clerk?.isReady()) {\n        return this.hydrateClerkJS(global.Clerk);\n      }\n      return;\n    } catch (err) {\n      const error = err as Error;\n      // In Next.js we can throw a full screen error in development mode.\n      // However, in production throwing an error results in an infinite loop.\n      // More info at: https://github.com/vercel/next.js/issues/6973\n      if (process.env.NODE_ENV === 'production') {\n        console.error(error.stack || error.message || error);\n      } else {\n        throw err;\n      }\n      return;\n    }\n  }\n\n  public addOnLoaded = (cb: () => void) => {\n    this.loadedListeners.push(cb);\n    /**\n     * When IsomorphicClerk is loaded execute the callback directly\n     */\n    if (this.loaded) {\n      this.emitLoaded();\n    }\n  };\n\n  public emitLoaded = () => {\n    this.loadedListeners.forEach(cb => cb());\n    this.loadedListeners = [];\n  };\n\n  private hydrateClerkJS = (clerkjs: BrowserClerk | HeadlessBrowserClerk | undefined) => {\n    if (!clerkjs) {\n      throw new Error('Failed to hydrate latest Clerk JS');\n    }\n\n    this.clerkjs = clerkjs;\n\n    this.premountMethodCalls.forEach(cb => cb());\n\n    if (this.preopenSignIn !== null) {\n      clerkjs.openSignIn(this.preopenSignIn);\n    }\n\n    if (this.preopenSignUp !== null) {\n      clerkjs.openSignUp(this.preopenSignUp);\n    }\n\n    if (this.preopenUserProfile !== null) {\n      clerkjs.openUserProfile(this.preopenUserProfile);\n    }\n\n    if (this.preopenOneTap !== null) {\n      clerkjs.openGoogleOneTap(this.preopenOneTap);\n    }\n\n    if (this.preopenOrganizationProfile !== null) {\n      clerkjs.openOrganizationProfile(this.preopenOrganizationProfile);\n    }\n\n    if (this.preopenCreateOrganization !== null) {\n      clerkjs.openCreateOrganization(this.preopenCreateOrganization);\n    }\n\n    this.premountSignInNodes.forEach((props: SignInProps, node: HTMLDivElement) => {\n      clerkjs.mountSignIn(node, props);\n    });\n\n    this.premountSignUpNodes.forEach((props: SignUpProps, node: HTMLDivElement) => {\n      clerkjs.mountSignUp(node, props);\n    });\n\n    this.premountUserProfileNodes.forEach((props: UserProfileProps, node: HTMLDivElement) => {\n      clerkjs.mountUserProfile(node, props);\n    });\n\n    this.premountUserButtonNodes.forEach((props: UserButtonProps, node: HTMLDivElement) => {\n      clerkjs.mountUserButton(node, props);\n    });\n\n    this.premountOrganizationListNodes.forEach((props: OrganizationListProps, node: HTMLDivElement) => {\n      clerkjs.mountOrganizationList(node, props);\n    });\n\n    this.#loaded = true;\n    this.emitLoaded();\n    return this.clerkjs;\n  };\n\n  get version(): string | undefined {\n    return this.clerkjs?.version;\n  }\n\n  get client(): ClientResource | undefined {\n    if (this.clerkjs) {\n      return this.clerkjs.client;\n      // TODO: add ssr condition\n    } else {\n      return undefined;\n    }\n  }\n\n  get session(): ActiveSessionResource | undefined | null {\n    if (this.clerkjs) {\n      return this.clerkjs.session;\n    } else {\n      return undefined;\n    }\n  }\n\n  get user(): UserResource | undefined | null {\n    if (this.clerkjs) {\n      return this.clerkjs.user;\n    } else {\n      return undefined;\n    }\n  }\n\n  get organization(): OrganizationResource | undefined | null {\n    if (this.clerkjs) {\n      return this.clerkjs.organization;\n    } else {\n      return undefined;\n    }\n  }\n\n  get __unstable__environment(): any {\n    if (this.clerkjs) {\n      return (this.clerkjs as any).__unstable__environment;\n      // TODO: add ssr condition\n    } else {\n      return undefined;\n    }\n  }\n\n  __unstable__setEnvironment(...args: any): void {\n    if (this.clerkjs && '__unstable__setEnvironment' in this.clerkjs) {\n      (this.clerkjs as any).__unstable__setEnvironment(args);\n    } else {\n      return undefined;\n    }\n  }\n\n  __unstable__updateProps = (props: any): any => {\n    // Handle case where accounts has clerk-react@4 installed, but clerk-js@3 is manually loaded\n    if (this.clerkjs && '__unstable__updateProps' in this.clerkjs) {\n      (this.clerkjs as any).__unstable__updateProps(props);\n    } else {\n      return undefined;\n    }\n  };\n\n  /**\n   * `setActive` can be used to set the active session and/or organization.\n   */\n  setActive = ({ session, organization, beforeEmit }: SetActiveParams): Promise<void> => {\n    if (this.clerkjs) {\n      return this.clerkjs.setActive({ session, organization, beforeEmit });\n    } else {\n      return Promise.reject();\n    }\n  };\n\n  setSession = (session: ActiveSessionResource | string | null, beforeEmit?: BeforeEmitCallback): Promise<void> => {\n    deprecated('setSession', 'Use `Clerk.setActive` instead');\n    return this.setActive({ session, beforeEmit });\n  };\n\n  openSignIn = (props?: SignInProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.openSignIn(props);\n    } else {\n      this.preopenSignIn = props;\n    }\n  };\n\n  closeSignIn = (): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.closeSignIn();\n    } else {\n      this.preopenSignIn = null;\n    }\n  };\n\n  openGoogleOneTap = (props?: GoogleOneTapProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.openGoogleOneTap(props);\n    } else {\n      this.preopenOneTap = props;\n    }\n  };\n\n  closeGoogleOneTap = (): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.closeGoogleOneTap();\n    } else {\n      this.preopenOneTap = null;\n    }\n  };\n\n  openUserProfile = (props?: UserProfileProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.openUserProfile(props);\n    } else {\n      this.preopenUserProfile = props;\n    }\n  };\n\n  closeUserProfile = (): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.closeUserProfile();\n    } else {\n      this.preopenUserProfile = null;\n    }\n  };\n\n  openOrganizationProfile = (props?: OrganizationProfileProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.openOrganizationProfile(props);\n    } else {\n      this.preopenOrganizationProfile = props;\n    }\n  };\n\n  closeOrganizationProfile = (): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.closeOrganizationProfile();\n    } else {\n      this.preopenOrganizationProfile = null;\n    }\n  };\n\n  openCreateOrganization = (props?: CreateOrganizationProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.openCreateOrganization(props);\n    } else {\n      this.preopenCreateOrganization = props;\n    }\n  };\n\n  closeCreateOrganization = (): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.closeCreateOrganization();\n    } else {\n      this.preopenCreateOrganization = null;\n    }\n  };\n\n  openSignUp = (props?: SignUpProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.openSignUp(props);\n    } else {\n      this.preopenSignUp = props;\n    }\n  };\n\n  closeSignUp = (): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.closeSignUp();\n    } else {\n      this.preopenSignUp = null;\n    }\n  };\n\n  mountSignIn = (node: HTMLDivElement, props: SignInProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountSignIn(node, props);\n    } else {\n      this.premountSignInNodes.set(node, props);\n    }\n  };\n\n  unmountSignIn = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountSignIn(node);\n    } else {\n      this.premountSignInNodes.delete(node);\n    }\n  };\n\n  mountSignUp = (node: HTMLDivElement, props: SignUpProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountSignUp(node, props);\n    } else {\n      this.premountSignUpNodes.set(node, props);\n    }\n  };\n\n  unmountSignUp = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountSignUp(node);\n    } else {\n      this.premountSignUpNodes.delete(node);\n    }\n  };\n\n  mountUserProfile = (node: HTMLDivElement, props: UserProfileProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountUserProfile(node, props);\n    } else {\n      this.premountUserProfileNodes.set(node, props);\n    }\n  };\n\n  unmountUserProfile = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountUserProfile(node);\n    } else {\n      this.premountUserProfileNodes.delete(node);\n    }\n  };\n\n  mountOrganizationProfile = (node: HTMLDivElement, props: OrganizationProfileProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountOrganizationProfile(node, props);\n    } else {\n      this.premountOrganizationProfileNodes.set(node, props);\n    }\n  };\n\n  unmountOrganizationProfile = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountOrganizationProfile(node);\n    } else {\n      this.premountOrganizationProfileNodes.delete(node);\n    }\n  };\n\n  mountCreateOrganization = (node: HTMLDivElement, props: CreateOrganizationProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountCreateOrganization(node, props);\n    } else {\n      this.premountCreateOrganizationNodes.set(node, props);\n    }\n  };\n\n  unmountCreateOrganization = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountCreateOrganization(node);\n    } else {\n      this.premountCreateOrganizationNodes.delete(node);\n    }\n  };\n\n  mountOrganizationSwitcher = (node: HTMLDivElement, props: OrganizationSwitcherProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountOrganizationSwitcher(node, props);\n    } else {\n      this.premountOrganizationSwitcherNodes.set(node, props);\n    }\n  };\n\n  unmountOrganizationSwitcher = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountOrganizationSwitcher(node);\n    } else {\n      this.premountOrganizationSwitcherNodes.delete(node);\n    }\n  };\n\n  mountOrganizationList = (node: HTMLDivElement, props: OrganizationListProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountOrganizationList(node, props);\n    } else {\n      this.premountOrganizationListNodes.set(node, props);\n    }\n  };\n\n  unmountOrganizationList = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountOrganizationList(node);\n    } else {\n      this.premountOrganizationListNodes.delete(node);\n    }\n  };\n\n  mountUserButton = (node: HTMLDivElement, userButtonProps: UserButtonProps): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.mountUserButton(node, userButtonProps);\n    } else {\n      this.premountUserButtonNodes.set(node, userButtonProps);\n    }\n  };\n\n  unmountUserButton = (node: HTMLDivElement): void => {\n    if (this.clerkjs && this.#loaded) {\n      this.clerkjs.unmountUserButton(node);\n    } else {\n      this.premountUserButtonNodes.delete(node);\n    }\n  };\n\n  addListener = (listener: ListenerCallback): UnsubscribeCallback => {\n    const callback = () => this.clerkjs?.addListener(listener);\n\n    if (this.clerkjs) {\n      return callback() as UnsubscribeCallback;\n    } else {\n      this.premountMethodCalls.set('addListener', callback);\n      return () => this.premountMethodCalls.delete('addListener');\n    }\n  };\n\n  navigate = (to: string): void => {\n    const callback = () => this.clerkjs?.navigate(to);\n    if (this.clerkjs && this.#loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('navigate', callback);\n    }\n  };\n\n  redirectWithAuth = (...args: Parameters<Clerk['redirectWithAuth']>): void => {\n    const callback = () => this.clerkjs?.redirectWithAuth(...args);\n    if (this.clerkjs && this.#loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('redirectWithAuth', callback);\n    }\n  };\n\n  redirectToSignIn = (opts: SignInRedirectOptions): void => {\n    const callback = () => this.clerkjs?.redirectToSignIn(opts as any);\n    if (this.clerkjs && this.#loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('redirectToSignIn', callback);\n    }\n  };\n\n  redirectToSignUp = (opts: SignUpRedirectOptions): void => {\n    const callback = () => this.clerkjs?.redirectToSignUp(opts as any);\n    if (this.clerkjs && this.#loaded) {\n      void callback();\n    } else {\n      this.premountMethodCalls.set('redirectToSignUp', callback);\n    }\n  };\n\n  redirectToUserProfile = (): void => {\n    const callback = () => this.clerkjs?.redirectToUserProfile();\n    if (this.clerkjs && this.#loaded) {\n      callback();\n    } else {\n      this.premountMethodCalls.set('redirectToUserProfile', callback);\n    }\n  };\n\n  redirectToHome = (): void => {\n    const callback = () => this.clerkjs?.redirectToHome();\n    if (this.clerkjs && this.#loaded) {\n      callback();\n    } else {\n      this.premountMethodCalls.set('redirectToHome', callback);\n    }\n  };\n\n  redirectToOrganizationProfile = (): void => {\n    const callback = () => this.clerkjs?.redirectToOrganizationProfile();\n    if (this.clerkjs && this.#loaded) {\n      callback();\n    } else {\n      this.premountMethodCalls.set('redirectToOrganizationProfile', callback);\n    }\n  };\n\n  redirectToCreateOrganization = (): void => {\n    const callback = () => this.clerkjs?.redirectToCreateOrganization();\n    if (this.clerkjs && this.#loaded) {\n      callback();\n    } else {\n      this.premountMethodCalls.set('redirectToCreateOrganization', callback);\n    }\n  };\n\n  handleRedirectCallback = (params: HandleOAuthCallbackParams): void => {\n    const callback = () => this.clerkjs?.handleRedirectCallback(params);\n    if (this.clerkjs && this.#loaded) {\n      void callback()?.catch(() => {\n        // This error is caused when the host app is using React18\n        // and strictMode is enabled. This useEffects runs twice because\n        // the clerk-react ui components mounts, unmounts and mounts again\n        // so the clerk-js component loses its state because of the custom\n        // unmount callback we're using.\n        // This needs to be solved by tweaking the logic in uiComponents.tsx\n        // or by making handleRedirectCallback idempotent\n      });\n    } else {\n      this.premountMethodCalls.set('handleRedirectCallback', callback);\n    }\n  };\n  /**\n   * @deprecated Use `handleEmailLinkVerification` instead.\n   */\n  handleMagicLinkVerification = async (params: HandleMagicLinkVerificationParams): Promise<void> => {\n    deprecated('handleMagicLinkVerification', 'Use `handleEmailLinkVerification` instead.');\n    const callback = () => this.clerkjs?.handleMagicLinkVerification(params);\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('handleMagicLinkVerification', callback);\n    }\n  };\n\n  handleGoogleOneTapCallback = (\n    signInOrUp: SignInResource | SignUpResource,\n    params: HandleOAuthCallbackParams,\n  ): void => {\n    const callback = () => this.clerkjs?.handleGoogleOneTapCallback(signInOrUp, params);\n    if (this.clerkjs && this.#loaded) {\n      void callback()?.catch(() => {\n        // This error is caused when the host app is using React18\n        // and strictMode is enabled. This useEffects runs twice because\n        // the clerk-react ui components mounts, unmounts and mounts again\n        // so the clerk-js component loses its state because of the custom\n        // unmount callback we're using.\n        // This needs to be solved by tweaking the logic in uiComponents.tsx\n        // or by making handleRedirectCallback idempotent\n      });\n    } else {\n      this.premountMethodCalls.set('handleGoogleOneTapCallback', callback);\n    }\n  };\n\n  handleEmailLinkVerification = async (params: HandleEmailLinkVerificationParams): Promise<void> => {\n    const callback = () => this.clerkjs?.handleEmailLinkVerification(params);\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('handleEmailLinkVerification', callback);\n    }\n  };\n\n  authenticateWithMetamask = async (params: AuthenticateWithMetamaskParams): Promise<void> => {\n    const callback = () => this.clerkjs?.authenticateWithMetamask(params);\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('authenticateWithMetamask', callback);\n    }\n  };\n\n  authenticateWithGoogleOneTap = async (\n    params: AuthenticateWithGoogleOneTapParams,\n  ): Promise<SignInResource | SignUpResource> => {\n    const clerkjs = await this.#waitForClerkJS();\n    return clerkjs.authenticateWithGoogleOneTap(params);\n  };\n\n  createOrganization = async (params: CreateOrganizationParams): Promise<OrganizationResource | void> => {\n    const callback = () => this.clerkjs?.createOrganization(params);\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<OrganizationResource>;\n    } else {\n      this.premountMethodCalls.set('createOrganization', callback);\n    }\n  };\n\n  getOrganizationMemberships = async (): Promise<OrganizationMembershipResource[] | void> => {\n    const callback = () => this.clerkjs?.getOrganizationMemberships();\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<OrganizationMembershipResource[]>;\n    } else {\n      this.premountMethodCalls.set('getOrganizationMemberships', callback);\n    }\n  };\n\n  getOrganization = async (organizationId: string): Promise<OrganizationResource | void> => {\n    const callback = () => this.clerkjs?.getOrganization(organizationId);\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<OrganizationResource>;\n    } else {\n      this.premountMethodCalls.set('getOrganization', callback);\n    }\n  };\n\n  signOut: SignOut = async (\n    signOutCallbackOrOptions?: SignOutCallback | SignOutOptions,\n    options?: SignOutOptions,\n  ): Promise<void> => {\n    const callback = () => this.clerkjs?.signOut(signOutCallbackOrOptions as any, options);\n    if (this.clerkjs && this.#loaded) {\n      return callback() as Promise<void>;\n    } else {\n      this.premountMethodCalls.set('signOut', callback);\n    }\n  };\n}\n"], "mappings": ";AAAA,IAAAA,OAAA,EAAAC,OAAA,EAAAC,SAAA,EAAAC,YAAA,EAAAC,eAAA,EAAAC,SAAA,EAAAC,eAAA,EAAAC,iBAAA;AAAA,SAASC,SAAA,QAAiB;AAC1B,SAASC,UAAA,QAAkB;AAC3B,SAASC,eAAA,QAAuB;AAyChC,SAASC,6CAAA,QAAqD;AAS9D,SAASC,aAAA,EAAeC,iBAAA,QAAyB;AAkGjD,MAAqBC,gBAAA,GAArB,MAAqBA,gBAAA,CAAiD;EA6EpEC,YAAYC,OAAA,EAAiC;IAkH7CC,YAAA,OAAAX,eAAA;IA3LA,KAAQY,OAAA,GAAsD;IAC9D,KAAQC,aAAA,GAA2C;IACnD,KAAQC,aAAA,GAAqC;IAC7C,KAAQC,aAAA,GAAqC;IAC7C,KAAQC,kBAAA,GAA+C;IACvD,KAAQC,0BAAA,GAA+D;IACvE,KAAQC,yBAAA,GAA6D;IACrE,KAAQC,mBAAA,GAAsB,mBAAIC,GAAA,CAAiC;IACnE,KAAQC,mBAAA,GAAsB,mBAAID,GAAA,CAAiC;IACnE,KAAQE,wBAAA,GAA2B,mBAAIF,GAAA,CAAsC;IAC7E,KAAQG,uBAAA,GAA0B,mBAAIH,GAAA,CAAqC;IAC3E,KAAQI,gCAAA,GAAmC,mBAAIJ,GAAA,CAA8C;IAC7F,KAAQK,+BAAA,GAAkC,mBAAIL,GAAA,CAA6C;IAC3F,KAAQM,iCAAA,GAAoC,mBAAIN,GAAA,CAA+C;IAC/F,KAAQO,6BAAA,GAAgC,mBAAIP,GAAA,CAA2C;IACvF,KAAQQ,mBAAA,GAAsB,mBAAIR,GAAA,CAA8C;IAChF,KAAQS,eAAA,GAAqC,EAAC;IAE9ClB,YAAA,OAAAjB,OAAA,EAAU;IACViB,YAAA,OAAAhB,OAAA;IACAgB,YAAA,OAAAf,SAAA;IACAe,YAAA,OAAAd,YAAA;IACAc,YAAA,OAAAb,eAAA;IA2FA,KAAAgC,OAAA,GAAU,MAAY;MA3QxB,IAAAC,EAAA;MA2Q2B,OAAAC,OAAA,EAAQD,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcD,OAAA,EAAS;IAAA;IAExD,KAAAG,cAAA,GAAkBC,IAAA,IAA0C;MAC1D,MAAMC,QAAA,GAAWA,CAAA,KAAG;QA9QxB,IAAAJ,EAAA;QA8Q2B,SAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcE,cAAA,CAAeC,IAAA,MAAS;MAAA;MAC7D,IAAI,KAAKtB,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,OAAOyC,QAAA,CAAS;MAClB,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,kBAAkBF,QAAQ;MACzD;IACF;IAEA,KAAAG,cAAA,GAAkBJ,IAAA,IAA0C;MAC1D,MAAMC,QAAA,GAAWA,CAAA,KAAG;QAvRxB,IAAAJ,EAAA;QAuR2B,SAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcO,cAAA,CAAeJ,IAAA,MAAS;MAAA;MAC7D,IAAI,KAAKtB,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,OAAOyC,QAAA,CAAS;MAClB,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,kBAAkBF,QAAQ;MACzD;IACF;IAEA,KAAAI,mBAAA,GAAsB,MAAqB;MACzC,MAAMJ,QAAA,GAAWA,CAAA,KAAG;QAhSxB,IAAAJ,EAAA;QAgS2B,SAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcQ,mBAAA,OAAyB;MAAA;MAC9D,IAAI,KAAK3B,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,OAAOyC,QAAA,CAAS;MAClB,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,uBAAuBF,QAAQ;MAC9D;IACF;IAEA,KAAAK,0BAAA,GAA6B,MAAqB;MAChD,MAAML,QAAA,GAAWA,CAAA,KAAG;QAzSxB,IAAAJ,EAAA;QAyS2B,SAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcS,0BAAA,OAAgC;MAAA;MACrE,IAAI,KAAK5B,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,OAAOyC,QAAA,CAAS;MAClB,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,8BAA8BF,QAAQ;MACrE;IACF;IAEA,KAAAM,2BAAA,GAA8B,MAAqB;MACjD,MAAMN,QAAA,GAAWA,CAAA,KAAG;QAlTxB,IAAAJ,EAAA;QAkT2B,SAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcU,2BAAA,OAAiC;MAAA;MACtE,IAAI,KAAK7B,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,OAAOyC,QAAA,CAAS;MAClB,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,+BAA+BF,QAAQ;MACtE;IACF;IAEA,KAAAO,YAAA,GAAe,MAAqB;MAClC,MAAMP,QAAA,GAAWA,CAAA,KAAG;QA3TxB,IAAAJ,EAAA;QA2T2B,SAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcW,YAAA,OAAkB;MAAA;MACvD,IAAI,KAAK9B,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,OAAOyC,QAAA,CAAS;MAClB,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,gBAAgBF,QAAQ;MACvD;IACF;IAEA,KAAAQ,gBAAA,GAAoBC,EAAA,IAA8B;MAChD,MAAMT,QAAA,GAAWA,CAAA,KAAG;QApUxB,IAAAJ,EAAA;QAoU2B,SAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcY,gBAAA,CAAiBC,EAAA,MAAO;MAAA;MAC7D,IAAI,KAAKhC,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,OAAOyC,QAAA,CAAS;MAClB,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,oBAAoBF,QAAQ;MAC3D;IACF;IAEA,KAAAU,qBAAA,GAAwB,MAAY;MAClC,MAAMV,QAAA,GAAWA,CAAA,KAAG;QA7UxB,IAAAJ,EAAA;QA6U2B,QAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcc,qBAAA;MAAA;MACrC,IAAI,KAAKjC,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKyC,QAAA,CAAS;MAChB,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,yBAAyBF,QAAQ;MAChE;IACF;IA8FA,KAAOW,WAAA,GAAeC,EAAA,IAAmB;MACvC,KAAKlB,eAAA,CAAgBmB,IAAA,CAAKD,EAAE;MAI5B,IAAI,KAAKE,MAAA,EAAQ;QACf,KAAKC,UAAA,CAAW;MAClB;IACF;IAEA,KAAOA,UAAA,GAAa,MAAM;MACxB,KAAKrB,eAAA,CAAgBsB,OAAA,CAAQJ,EAAA,IAAMA,EAAA,CAAG,CAAC;MACvC,KAAKlB,eAAA,GAAkB,EAAC;IAC1B;IAEA,KAAQuB,cAAA,GAAkBxC,OAAA,IAA6D;MACrF,IAAI,CAACA,OAAA,EAAS;QACZ,MAAM,IAAIyC,KAAA,CAAM,mCAAmC;MACrD;MAEA,KAAKzC,OAAA,GAAUA,OAAA;MAEf,KAAKgB,mBAAA,CAAoBuB,OAAA,CAAQJ,EAAA,IAAMA,EAAA,CAAG,CAAC;MAE3C,IAAI,KAAKjC,aAAA,KAAkB,MAAM;QAC/BF,OAAA,CAAQ0C,UAAA,CAAW,KAAKxC,aAAa;MACvC;MAEA,IAAI,KAAKC,aAAA,KAAkB,MAAM;QAC/BH,OAAA,CAAQ2C,UAAA,CAAW,KAAKxC,aAAa;MACvC;MAEA,IAAI,KAAKC,kBAAA,KAAuB,MAAM;QACpCJ,OAAA,CAAQ4C,eAAA,CAAgB,KAAKxC,kBAAkB;MACjD;MAEA,IAAI,KAAKH,aAAA,KAAkB,MAAM;QAC/BD,OAAA,CAAQ6C,gBAAA,CAAiB,KAAK5C,aAAa;MAC7C;MAEA,IAAI,KAAKI,0BAAA,KAA+B,MAAM;QAC5CL,OAAA,CAAQ8C,uBAAA,CAAwB,KAAKzC,0BAA0B;MACjE;MAEA,IAAI,KAAKC,yBAAA,KAA8B,MAAM;QAC3CN,OAAA,CAAQ+C,sBAAA,CAAuB,KAAKzC,yBAAyB;MAC/D;MAEA,KAAKC,mBAAA,CAAoBgC,OAAA,CAAQ,CAACS,KAAA,EAAoBC,IAAA,KAAyB;QAC7EjD,OAAA,CAAQkD,WAAA,CAAYD,IAAA,EAAMD,KAAK;MACjC,CAAC;MAED,KAAKvC,mBAAA,CAAoB8B,OAAA,CAAQ,CAACS,KAAA,EAAoBC,IAAA,KAAyB;QAC7EjD,OAAA,CAAQmD,WAAA,CAAYF,IAAA,EAAMD,KAAK;MACjC,CAAC;MAED,KAAKtC,wBAAA,CAAyB6B,OAAA,CAAQ,CAACS,KAAA,EAAyBC,IAAA,KAAyB;QACvFjD,OAAA,CAAQoD,gBAAA,CAAiBH,IAAA,EAAMD,KAAK;MACtC,CAAC;MAED,KAAKrC,uBAAA,CAAwB4B,OAAA,CAAQ,CAACS,KAAA,EAAwBC,IAAA,KAAyB;QACrFjD,OAAA,CAAQqD,eAAA,CAAgBJ,IAAA,EAAMD,KAAK;MACrC,CAAC;MAED,KAAKjC,6BAAA,CAA8BwB,OAAA,CAAQ,CAACS,KAAA,EAA8BC,IAAA,KAAyB;QACjGjD,OAAA,CAAQsD,qBAAA,CAAsBL,IAAA,EAAMD,KAAK;MAC3C,CAAC;MAEDO,YAAA,OAAKzE,OAAA,EAAU;MACf,KAAKwD,UAAA,CAAW;MAChB,OAAO,KAAKtC,OAAA;IACd;IAwDA,KAAAwD,uBAAA,GAA2BR,KAAA,IAAoB;MAE7C,IAAI,KAAKhD,OAAA,IAAW,6BAA6B,KAAKA,OAAA,EAAS;QAC5D,KAAKA,OAAA,CAAgBwD,uBAAA,CAAwBR,KAAK;MACrD,OAAO;QACL,OAAO;MACT;IACF;IAKA;AAAA;AAAA;IAAA,KAAAS,SAAA,GAAY,CAAC;MAAEC,OAAA;MAASC,YAAA;MAAcC;IAAW,MAAsC;MACrF,IAAI,KAAK5D,OAAA,EAAS;QAChB,OAAO,KAAKA,OAAA,CAAQyD,SAAA,CAAU;UAAEC,OAAA;UAASC,YAAA;UAAcC;QAAW,CAAC;MACrE,OAAO;QACL,OAAOC,OAAA,CAAQC,MAAA,CAAO;MACxB;IACF;IAEA,KAAAC,UAAA,GAAa,CAACL,OAAA,EAAgDE,UAAA,KAAmD;MAC/GrE,UAAA,CAAW,cAAc,+BAA+B;MACxD,OAAO,KAAKkE,SAAA,CAAU;QAAEC,OAAA;QAASE;MAAW,CAAC;IAC/C;IAEA,KAAAlB,UAAA,GAAcM,KAAA,IAA8B;MAC1C,IAAI,KAAKhD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQ0C,UAAA,CAAWM,KAAK;MAC/B,OAAO;QACL,KAAK9C,aAAA,GAAgB8C,KAAA;MACvB;IACF;IAEA,KAAAgB,WAAA,GAAc,MAAY;MACxB,IAAI,KAAKhE,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQgE,WAAA,CAAY;MAC3B,OAAO;QACL,KAAK9D,aAAA,GAAgB;MACvB;IACF;IAEA,KAAA2C,gBAAA,GAAoBG,KAAA,IAAoC;MACtD,IAAI,KAAKhD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQ6C,gBAAA,CAAiBG,KAAK;MACrC,OAAO;QACL,KAAK/C,aAAA,GAAgB+C,KAAA;MACvB;IACF;IAEA,KAAAiB,iBAAA,GAAoB,MAAY;MAC9B,IAAI,KAAKjE,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQiE,iBAAA,CAAkB;MACjC,OAAO;QACL,KAAKhE,aAAA,GAAgB;MACvB;IACF;IAEA,KAAA2C,eAAA,GAAmBI,KAAA,IAAmC;MACpD,IAAI,KAAKhD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQ4C,eAAA,CAAgBI,KAAK;MACpC,OAAO;QACL,KAAK5C,kBAAA,GAAqB4C,KAAA;MAC5B;IACF;IAEA,KAAAkB,gBAAA,GAAmB,MAAY;MAC7B,IAAI,KAAKlE,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQkE,gBAAA,CAAiB;MAChC,OAAO;QACL,KAAK9D,kBAAA,GAAqB;MAC5B;IACF;IAEA,KAAA0C,uBAAA,GAA2BE,KAAA,IAA2C;MACpE,IAAI,KAAKhD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQ8C,uBAAA,CAAwBE,KAAK;MAC5C,OAAO;QACL,KAAK3C,0BAAA,GAA6B2C,KAAA;MACpC;IACF;IAEA,KAAAmB,wBAAA,GAA2B,MAAY;MACrC,IAAI,KAAKnE,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQmE,wBAAA,CAAyB;MACxC,OAAO;QACL,KAAK9D,0BAAA,GAA6B;MACpC;IACF;IAEA,KAAA0C,sBAAA,GAA0BC,KAAA,IAA0C;MAClE,IAAI,KAAKhD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQ+C,sBAAA,CAAuBC,KAAK;MAC3C,OAAO;QACL,KAAK1C,yBAAA,GAA4B0C,KAAA;MACnC;IACF;IAEA,KAAAoB,uBAAA,GAA0B,MAAY;MACpC,IAAI,KAAKpE,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQoE,uBAAA,CAAwB;MACvC,OAAO;QACL,KAAK9D,yBAAA,GAA4B;MACnC;IACF;IAEA,KAAAqC,UAAA,GAAcK,KAAA,IAA8B;MAC1C,IAAI,KAAKhD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQ2C,UAAA,CAAWK,KAAK;MAC/B,OAAO;QACL,KAAK7C,aAAA,GAAgB6C,KAAA;MACvB;IACF;IAEA,KAAAqB,WAAA,GAAc,MAAY;MACxB,IAAI,KAAKrE,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQqE,WAAA,CAAY;MAC3B,OAAO;QACL,KAAKlE,aAAA,GAAgB;MACvB;IACF;IAEA,KAAA+C,WAAA,GAAc,CAACD,IAAA,EAAsBD,KAAA,KAA6B;MAChE,IAAI,KAAKhD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQkD,WAAA,CAAYD,IAAA,EAAMD,KAAK;MACtC,OAAO;QACL,KAAKzC,mBAAA,CAAoBkB,GAAA,CAAIwB,IAAA,EAAMD,KAAK;MAC1C;IACF;IAEA,KAAAsB,aAAA,GAAiBrB,IAAA,IAA+B;MAC9C,IAAI,KAAKjD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQsE,aAAA,CAAcrB,IAAI;MACjC,OAAO;QACL,KAAK1C,mBAAA,CAAoBgE,MAAA,CAAOtB,IAAI;MACtC;IACF;IAEA,KAAAE,WAAA,GAAc,CAACF,IAAA,EAAsBD,KAAA,KAA6B;MAChE,IAAI,KAAKhD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQmD,WAAA,CAAYF,IAAA,EAAMD,KAAK;MACtC,OAAO;QACL,KAAKvC,mBAAA,CAAoBgB,GAAA,CAAIwB,IAAA,EAAMD,KAAK;MAC1C;IACF;IAEA,KAAAwB,aAAA,GAAiBvB,IAAA,IAA+B;MAC9C,IAAI,KAAKjD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQwE,aAAA,CAAcvB,IAAI;MACjC,OAAO;QACL,KAAKxC,mBAAA,CAAoB8D,MAAA,CAAOtB,IAAI;MACtC;IACF;IAEA,KAAAG,gBAAA,GAAmB,CAACH,IAAA,EAAsBD,KAAA,KAAkC;MAC1E,IAAI,KAAKhD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQoD,gBAAA,CAAiBH,IAAA,EAAMD,KAAK;MAC3C,OAAO;QACL,KAAKtC,wBAAA,CAAyBe,GAAA,CAAIwB,IAAA,EAAMD,KAAK;MAC/C;IACF;IAEA,KAAAyB,kBAAA,GAAsBxB,IAAA,IAA+B;MACnD,IAAI,KAAKjD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQyE,kBAAA,CAAmBxB,IAAI;MACtC,OAAO;QACL,KAAKvC,wBAAA,CAAyB6D,MAAA,CAAOtB,IAAI;MAC3C;IACF;IAEA,KAAAyB,wBAAA,GAA2B,CAACzB,IAAA,EAAsBD,KAAA,KAA0C;MAC1F,IAAI,KAAKhD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQ0E,wBAAA,CAAyBzB,IAAA,EAAMD,KAAK;MACnD,OAAO;QACL,KAAKpC,gCAAA,CAAiCa,GAAA,CAAIwB,IAAA,EAAMD,KAAK;MACvD;IACF;IAEA,KAAA2B,0BAAA,GAA8B1B,IAAA,IAA+B;MAC3D,IAAI,KAAKjD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQ2E,0BAAA,CAA2B1B,IAAI;MAC9C,OAAO;QACL,KAAKrC,gCAAA,CAAiC2D,MAAA,CAAOtB,IAAI;MACnD;IACF;IAEA,KAAA2B,uBAAA,GAA0B,CAAC3B,IAAA,EAAsBD,KAAA,KAAyC;MACxF,IAAI,KAAKhD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQ4E,uBAAA,CAAwB3B,IAAA,EAAMD,KAAK;MAClD,OAAO;QACL,KAAKnC,+BAAA,CAAgCY,GAAA,CAAIwB,IAAA,EAAMD,KAAK;MACtD;IACF;IAEA,KAAA6B,yBAAA,GAA6B5B,IAAA,IAA+B;MAC1D,IAAI,KAAKjD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQ6E,yBAAA,CAA0B5B,IAAI;MAC7C,OAAO;QACL,KAAKpC,+BAAA,CAAgC0D,MAAA,CAAOtB,IAAI;MAClD;IACF;IAEA,KAAA6B,yBAAA,GAA4B,CAAC7B,IAAA,EAAsBD,KAAA,KAA2C;MAC5F,IAAI,KAAKhD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQ8E,yBAAA,CAA0B7B,IAAA,EAAMD,KAAK;MACpD,OAAO;QACL,KAAKlC,iCAAA,CAAkCW,GAAA,CAAIwB,IAAA,EAAMD,KAAK;MACxD;IACF;IAEA,KAAA+B,2BAAA,GAA+B9B,IAAA,IAA+B;MAC5D,IAAI,KAAKjD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQ+E,2BAAA,CAA4B9B,IAAI;MAC/C,OAAO;QACL,KAAKnC,iCAAA,CAAkCyD,MAAA,CAAOtB,IAAI;MACpD;IACF;IAEA,KAAAK,qBAAA,GAAwB,CAACL,IAAA,EAAsBD,KAAA,KAAuC;MACpF,IAAI,KAAKhD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQsD,qBAAA,CAAsBL,IAAA,EAAMD,KAAK;MAChD,OAAO;QACL,KAAKjC,6BAAA,CAA8BU,GAAA,CAAIwB,IAAA,EAAMD,KAAK;MACpD;IACF;IAEA,KAAAgC,uBAAA,GAA2B/B,IAAA,IAA+B;MACxD,IAAI,KAAKjD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQgF,uBAAA,CAAwB/B,IAAI;MAC3C,OAAO;QACL,KAAKlC,6BAAA,CAA8BwD,MAAA,CAAOtB,IAAI;MAChD;IACF;IAEA,KAAAI,eAAA,GAAkB,CAACJ,IAAA,EAAsBgC,eAAA,KAA2C;MAClF,IAAI,KAAKjF,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQqD,eAAA,CAAgBJ,IAAA,EAAMgC,eAAe;MACpD,OAAO;QACL,KAAKtE,uBAAA,CAAwBc,GAAA,CAAIwB,IAAA,EAAMgC,eAAe;MACxD;IACF;IAEA,KAAAC,iBAAA,GAAqBjC,IAAA,IAA+B;MAClD,IAAI,KAAKjD,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKkB,OAAA,CAAQkF,iBAAA,CAAkBjC,IAAI;MACrC,OAAO;QACL,KAAKtC,uBAAA,CAAwB4D,MAAA,CAAOtB,IAAI;MAC1C;IACF;IAEA,KAAAkC,WAAA,GAAeC,QAAA,IAAoD;MACjE,MAAM7D,QAAA,GAAWA,CAAA,KAAG;QA1yBxB,IAAAJ,EAAA;QA0yB2B,QAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcgE,WAAA,CAAYC,QAAA;MAAA;MAEjD,IAAI,KAAKpF,OAAA,EAAS;QAChB,OAAOuB,QAAA,CAAS;MAClB,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,eAAeF,QAAQ;QACpD,OAAO,MAAM,KAAKP,mBAAA,CAAoBuD,MAAA,CAAO,aAAa;MAC5D;IACF;IAEA,KAAAc,QAAA,GAAYrD,EAAA,IAAqB;MAC/B,MAAMT,QAAA,GAAWA,CAAA,KAAG;QArzBxB,IAAAJ,EAAA;QAqzB2B,QAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAckE,QAAA,CAASrD,EAAA;MAAA;MAC9C,IAAI,KAAKhC,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKyC,QAAA,CAAS;MAChB,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,YAAYF,QAAQ;MACnD;IACF;IAEA,KAAA+D,gBAAA,GAAmB,IAAIC,IAAA,KAAsD;MAC3E,MAAMhE,QAAA,GAAWA,CAAA,KAAG;QA9zBxB,IAAAJ,EAAA;QA8zB2B,QAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcmE,gBAAA,CAAiB,GAAGC,IAAA;MAAA;MACzD,IAAI,KAAKvF,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKyC,QAAA,CAAS;MAChB,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,oBAAoBF,QAAQ;MAC3D;IACF;IAEA,KAAAiE,gBAAA,GAAoBlE,IAAA,IAAsC;MACxD,MAAMC,QAAA,GAAWA,CAAA,KAAG;QAv0BxB,IAAAJ,EAAA;QAu0B2B,QAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcqE,gBAAA,CAAiBlE,IAAA;MAAA;MACtD,IAAI,KAAKtB,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKyC,QAAA,CAAS;MAChB,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,oBAAoBF,QAAQ;MAC3D;IACF;IAEA,KAAAkE,gBAAA,GAAoBnE,IAAA,IAAsC;MACxD,MAAMC,QAAA,GAAWA,CAAA,KAAG;QAh1BxB,IAAAJ,EAAA;QAg1B2B,QAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcsE,gBAAA,CAAiBnE,IAAA;MAAA;MACtD,IAAI,KAAKtB,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,KAAKyC,QAAA,CAAS;MAChB,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,oBAAoBF,QAAQ;MAC3D;IACF;IAEA,KAAAmE,qBAAA,GAAwB,MAAY;MAClC,MAAMnE,QAAA,GAAWA,CAAA,KAAG;QAz1BxB,IAAAJ,EAAA;QAy1B2B,QAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcuE,qBAAA;MAAA;MACrC,IAAI,KAAK1F,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChCyC,QAAA,CAAS;MACX,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,yBAAyBF,QAAQ;MAChE;IACF;IAEA,KAAAoE,cAAA,GAAiB,MAAY;MAC3B,MAAMpE,QAAA,GAAWA,CAAA,KAAG;QAl2BxB,IAAAJ,EAAA;QAk2B2B,QAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcwE,cAAA;MAAA;MACrC,IAAI,KAAK3F,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChCyC,QAAA,CAAS;MACX,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,kBAAkBF,QAAQ;MACzD;IACF;IAEA,KAAAqE,6BAAA,GAAgC,MAAY;MAC1C,MAAMrE,QAAA,GAAWA,CAAA,KAAG;QA32BxB,IAAAJ,EAAA;QA22B2B,QAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcyE,6BAAA;MAAA;MACrC,IAAI,KAAK5F,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChCyC,QAAA,CAAS;MACX,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,iCAAiCF,QAAQ;MACxE;IACF;IAEA,KAAAsE,4BAAA,GAA+B,MAAY;MACzC,MAAMtE,QAAA,GAAWA,CAAA,KAAG;QAp3BxB,IAAAJ,EAAA;QAo3B2B,QAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAc0E,4BAAA;MAAA;MACrC,IAAI,KAAK7F,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChCyC,QAAA,CAAS;MACX,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,gCAAgCF,QAAQ;MACvE;IACF;IAEA,KAAAuE,sBAAA,GAA0BC,MAAA,IAA4C;MA53BxE,IAAA5E,EAAA;MA63BI,MAAMI,QAAA,GAAWA,CAAA,KAAG;QA73BxB,IAAAyE,GAAA;QA63B2B,QAAAA,GAAA,QAAKhG,OAAA,KAAL,gBAAAgG,GAAA,CAAcF,sBAAA,CAAuBC,MAAA;MAAA;MAC5D,IAAI,KAAK/F,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,OAAKqC,EAAA,GAAAI,QAAA,CAAS,MAAT,gBAAAJ,EAAA,CAAY8E,KAAA,CAAM,MAAM,CAQ7B;MACF,OAAO;QACL,KAAKjF,mBAAA,CAAoBS,GAAA,CAAI,0BAA0BF,QAAQ;MACjE;IACF;IAIA;AAAA;AAAA;IAAA,KAAA2E,2BAAA,GAA8B,MAAOH,MAAA,IAA6D;MAChGxG,UAAA,CAAW,+BAA+B,4CAA4C;MACtF,MAAMgC,QAAA,GAAWA,CAAA,KAAG;QAj5BxB,IAAAJ,EAAA;QAi5B2B,QAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAc+E,2BAAA,CAA4BH,MAAA;MAAA;MACjE,IAAI,KAAK/F,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,OAAOyC,QAAA,CAAS;MAClB,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,+BAA+BF,QAAQ;MACtE;IACF;IAEA,KAAA4E,0BAAA,GAA6B,CAC3BC,UAAA,EACAL,MAAA,KACS;MA55Bb,IAAA5E,EAAA;MA65BI,MAAMI,QAAA,GAAWA,CAAA,KAAG;QA75BxB,IAAAyE,GAAA;QA65B2B,QAAAA,GAAA,QAAKhG,OAAA,KAAL,gBAAAgG,GAAA,CAAcG,0BAAA,CAA2BC,UAAA,EAAYL,MAAA;MAAA;MAC5E,IAAI,KAAK/F,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,OAAKqC,EAAA,GAAAI,QAAA,CAAS,MAAT,gBAAAJ,EAAA,CAAY8E,KAAA,CAAM,MAAM,CAQ7B;MACF,OAAO;QACL,KAAKjF,mBAAA,CAAoBS,GAAA,CAAI,8BAA8BF,QAAQ;MACrE;IACF;IAEA,KAAA8E,2BAAA,GAA8B,MAAON,MAAA,IAA6D;MAChG,MAAMxE,QAAA,GAAWA,CAAA,KAAG;QA96BxB,IAAAJ,EAAA;QA86B2B,QAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAckF,2BAAA,CAA4BN,MAAA;MAAA;MACjE,IAAI,KAAK/F,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,OAAOyC,QAAA,CAAS;MAClB,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,+BAA+BF,QAAQ;MACtE;IACF;IAEA,KAAA+E,wBAAA,GAA2B,MAAOP,MAAA,IAA0D;MAC1F,MAAMxE,QAAA,GAAWA,CAAA,KAAG;QAv7BxB,IAAAJ,EAAA;QAu7B2B,QAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcmF,wBAAA,CAAyBP,MAAA;MAAA;MAC9D,IAAI,KAAK/F,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,OAAOyC,QAAA,CAAS;MAClB,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,4BAA4BF,QAAQ;MACnE;IACF;IAEA,KAAAgF,4BAAA,GAA+B,MAC7BR,MAAA,IAC6C;MAC7C,MAAM/F,OAAA,GAAU,MAAMwG,eAAA,OAAKpH,eAAA,EAAAC,iBAAA,EAALoH,IAAA;MACtB,OAAOzG,OAAA,CAAQuG,4BAAA,CAA6BR,MAAM;IACpD;IAEA,KAAAW,kBAAA,GAAqB,MAAOX,MAAA,IAA2E;MACrG,MAAMxE,QAAA,GAAWA,CAAA,KAAG;QAv8BxB,IAAAJ,EAAA;QAu8B2B,QAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcuF,kBAAA,CAAmBX,MAAA;MAAA;MACxD,IAAI,KAAK/F,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,OAAOyC,QAAA,CAAS;MAClB,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,sBAAsBF,QAAQ;MAC7D;IACF;IAEA,KAAAoF,0BAAA,GAA6B,YAA8D;MACzF,MAAMpF,QAAA,GAAWA,CAAA,KAAG;QAh9BxB,IAAAJ,EAAA;QAg9B2B,QAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcwF,0BAAA;MAAA;MACrC,IAAI,KAAK3G,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,OAAOyC,QAAA,CAAS;MAClB,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,8BAA8BF,QAAQ;MACrE;IACF;IAEA,KAAAqF,eAAA,GAAkB,MAAOC,cAAA,IAAiE;MACxF,MAAMtF,QAAA,GAAWA,CAAA,KAAG;QAz9BxB,IAAAJ,EAAA;QAy9B2B,QAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcyF,eAAA,CAAgBC,cAAA;MAAA;MACrD,IAAI,KAAK7G,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,OAAOyC,QAAA,CAAS;MAClB,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,mBAAmBF,QAAQ;MAC1D;IACF;IAEA,KAAAuF,OAAA,GAAmB,OACjBC,wBAAA,EACAjH,OAAA,KACkB;MAClB,MAAMyB,QAAA,GAAWA,CAAA,KAAG;QAr+BxB,IAAAJ,EAAA;QAq+B2B,QAAAA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAc2F,OAAA,CAAQC,wBAAA,EAAiCjH,OAAA;MAAA;MAC9E,IAAI,KAAKE,OAAA,IAAWwB,YAAA,OAAK1C,OAAA,GAAS;QAChC,OAAOyC,QAAA,CAAS;MAClB,OAAO;QACL,KAAKP,mBAAA,CAAoBS,GAAA,CAAI,WAAWF,QAAQ;MAClD;IACF;IAvwBE,MAAM;MAAEyF,KAAA,GAAQ;MAAMC,WAAA;MAAaC;IAAe,IAAIpH,OAAA,IAAW,CAAC;IAClEyD,YAAA,OAAKtE,YAAA,EAAegI,WAAA;IACpB1D,YAAA,OAAKrE,eAAA,EAAkBgI,cAAA;IACvB3D,YAAA,OAAKvE,SAAA,EAAYc,OAAA,oBAAAA,OAAA,CAASqH,QAAA;IAC1B5D,YAAA,OAAKxE,OAAA,EAAUe,OAAA,oBAAAA,OAAA,CAASsH,MAAA;IACxB,KAAKtH,OAAA,GAAUA,OAAA;IACf,KAAKkH,KAAA,GAAQA,KAAA;IACb,KAAKK,IAAA,GAAO/H,SAAA,CAAU,IAAI,YAAY;IACtC,KAAK,KAAKgI,WAAA,CAAY;EACxB;EA3DA,IAAIJ,eAAA,EAAqC;IACvC,OAAO1F,YAAA,OAAKtC,eAAA;EACd;EAEA,IAAImD,OAAA,EAAkB;IACpB,OAAOb,YAAA,OAAK1C,OAAA;EACd;EAIA,OAAOyI,oBAAoBzH,OAAA,EAAiC;IAK1D,IAAI,CAACR,SAAA,CAAU,KAAK,CAACkC,YAAA,OAAKrC,SAAA,KAAcW,OAAA,CAAQkH,KAAA,IAASxF,YAAA,OAAKrC,SAAA,EAAU6H,KAAA,KAAUlH,OAAA,CAAQkH,KAAA,EAAQ;MAChGzD,YAAA,OAAKpE,SAAA,EAAY,IAAIS,gBAAA,CAAgBE,OAAO;IAC9C;IACA,OAAO0B,YAAA,OAAKrC,SAAA;EACd;EAEA,OAAOqI,cAAA,EAAgB;IACrBjE,YAAA,OAAKpE,SAAA,EAAY;EACnB;EAEA,IAAIiI,OAAA,EAAiB;IAGnB,IAAI,OAAOK,MAAA,KAAW,eAAeA,MAAA,CAAOC,QAAA,EAAU;MACpD,OAAOlI,eAAA,CAAgBgC,YAAA,OAAKzC,OAAA,GAAS,IAAI4I,GAAA,CAAIF,MAAA,CAAOC,QAAA,CAASE,IAAI,GAAG,EAAE;IACxE;IACA,IAAI,OAAOpG,YAAA,OAAKzC,OAAA,MAAY,YAAY;MACtC,MAAM,IAAI0D,KAAA,CAAMhD,6CAA6C;IAC/D;IACA,OAAO+B,YAAA,OAAKzC,OAAA,KAAW;EACzB;EAEA,IAAIoI,SAAA,EAAmB;IAGrB,IAAI,OAAOM,MAAA,KAAW,eAAeA,MAAA,CAAOC,QAAA,EAAU;MACpD,OAAOlI,eAAA,CAAgBgC,YAAA,OAAKxC,SAAA,GAAW,IAAI2I,GAAA,CAAIF,MAAA,CAAOC,QAAA,CAASE,IAAI,GAAG,EAAE;IAC1E;IACA,IAAI,OAAOpG,YAAA,OAAKxC,SAAA,MAAc,YAAY;MACxC,MAAM,IAAIyD,KAAA,CAAMhD,6CAA6C;IAC/D;IACA,OAAO+B,YAAA,OAAKxC,SAAA,KAAa;EAC3B;EAcA,IAAI6I,YAAA,EAAuC;IA/O7C,IAAA1G,EAAA;IAgPI,SAAOA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAc0G,WAAA,KAAe,KAAK/H,OAAA,CAAQ+H,WAAA,IAAe;EAClE;EAEA,IAAIC,aAAA,EAAyC;IAnP/C,IAAA3G,EAAA;IAoPI,QAAOA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAc2G,YAAA;EACvB;EAEA,IAAIb,YAAA,EAAsB;IAvP5B,IAAA9F,EAAA;IAwPI,SAAOA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAc8F,WAAA,KAAezF,YAAA,OAAKvC,YAAA,KAAgB;EAC3D;EAEA,IAAI8I,kBAAA,EAA6B;IA3PnC,IAAA5G,EAAA;IA4PI,SAAOA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAc4G,iBAAA,KAAqB,KAAKjI,OAAA,CAAQkI,eAAA,IAAmB;EAC5E;EAEA,IAAIC,YAAA,EAAuB;IAGzB,IAAI,OAAOR,MAAA,KAAW,eAAeA,MAAA,CAAOC,QAAA,EAAU;MACpD,OAAOlI,eAAA,CAAgB,KAAKM,OAAA,CAAQmI,WAAA,EAAa,IAAIN,GAAA,CAAIF,MAAA,CAAOC,QAAA,CAASE,IAAI,GAAG,KAAK;IACvF;IACA,IAAI,OAAO,KAAK9H,OAAA,CAAQmI,WAAA,KAAgB,YAAY;MAClD,MAAM,IAAIxF,KAAA,CAAMhD,6CAA6C;IAC/D;IACA,OAAO;EACT;EAqFA,MAAM6H,YAAA,EAAwE;IA9VhF,IAAAnG,EAAA,EAAA+G,EAAA,EAAAC,EAAA;IA+VI,IAAI,KAAKd,IAAA,KAAS,aAAa7F,YAAA,OAAK1C,OAAA,GAAS;MAC3C;IACF;IAYA,IAAI,OAAO2I,MAAA,KAAW,aAAa;MACjCA,MAAA,CAAOW,oBAAA,GAAuB,KAAKnB,WAAA;MACnCQ,MAAA,CAAOY,uBAAA,GAA0B,KAAKnB,cAAA;MACtCO,MAAA,CAAOa,iBAAA,GAAoB,KAAKnB,QAAA;MAChCM,MAAA,CAAOc,cAAA,GAAiB,KAAKnB,MAAA;IAC/B;IAEA,IAAI;MACF,IAAI,KAAKJ,KAAA,EAAO;QAEd,IAAIwB,CAAA;QAEJ,IAAI9I,aAAA,CAAwE,KAAKsH,KAAK,GAAG;UAEvFwB,CAAA,GAAI,IAAI,KAAKxB,KAAA,CAAM,KAAKE,cAAA,IAAkB,KAAKD,WAAA,IAAe,IAAI;YAChEE,QAAA,EAAU,KAAKA,QAAA;YACfC,MAAA,EAAQ,KAAKA;UACf,CAAQ;UACR,MAAMoB,CAAA,CAAEC,IAAA,CAAK,KAAK3I,OAAO;QAC3B,OAAO;UAEL0I,CAAA,GAAI,KAAKxB,KAAA;UAET,IAAI,CAACwB,CAAA,CAAEtH,OAAA,CAAQ,GAAG;YAChB,MAAMsH,CAAA,CAAEC,IAAA,CAAK,KAAK3I,OAAO;UAC3B;QACF;QAEA4I,MAAA,CAAO1B,KAAA,GAAQwB,CAAA;MACjB,OAAO;QAEL,IAAI,CAACE,MAAA,CAAO1B,KAAA,EAAO;UACjB,MAAMrH,iBAAA,CAAkB;YACtB,GAAG,KAAKG,OAAA;YACRmH,WAAA,EAAa,KAAKA,WAAA;YAClBC,cAAA,EAAgB,KAAKA,cAAA;YACrBC,QAAA,EAAU,KAAKA,QAAA;YACfC,MAAA,EAAQ,KAAKA;UACf,CAAC;QACH;QAEA,IAAI,CAACsB,MAAA,CAAO1B,KAAA,EAAO;UACjB,MAAM,IAAIvE,KAAA,CAAM,+DAA+D;QACjF;QAEA,MAAMiG,MAAA,CAAO1B,KAAA,CAAMyB,IAAA,CAAK,KAAK3I,OAAO;MACtC;MAEA4I,MAAA,CAAO1B,KAAA,CAAMa,WAAA,IAAc1G,EAAA,QAAKrB,OAAA,CAAQ+H,WAAA,KAAb,OAAA1G,EAAA,GAA4B;QAAEwH,IAAA,EAAM;QAAcC,OAAA,EAAS;MAAgB;MAEtG,MAAIV,EAAA,GAAAQ,MAAA,CAAO1B,KAAA,KAAP,gBAAAkB,EAAA,CAAc7F,MAAA,OAAU8F,EAAA,GAAAO,MAAA,CAAO1B,KAAA,KAAP,gBAAAmB,EAAA,CAAcjH,OAAA,KAAW;QACnD,OAAO,KAAKsB,cAAA,CAAekG,MAAA,CAAO1B,KAAK;MACzC;MACA;IACF,SAAS6B,GAAA,EAAK;MACZ,MAAMC,KAAA,GAAQD,GAAA;MAId,IAAIE,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa,cAAc;QACzCC,OAAA,CAAQJ,KAAA,CAAMA,KAAA,CAAMK,KAAA,IAASL,KAAA,CAAMM,OAAA,IAAWN,KAAK;MACrD,OAAO;QACL,MAAMD,GAAA;MACR;MACA;IACF;EACF;EA2EA,IAAID,QAAA,EAA8B;IA1fpC,IAAAzH,EAAA;IA2fI,QAAOA,EAAA,QAAKnB,OAAA,KAAL,gBAAAmB,EAAA,CAAcyH,OAAA;EACvB;EAEA,IAAIS,OAAA,EAAqC;IACvC,IAAI,KAAKrJ,OAAA,EAAS;MAChB,OAAO,KAAKA,OAAA,CAAQqJ,MAAA;IAEtB,OAAO;MACL,OAAO;IACT;EACF;EAEA,IAAI3F,QAAA,EAAoD;IACtD,IAAI,KAAK1D,OAAA,EAAS;MAChB,OAAO,KAAKA,OAAA,CAAQ0D,OAAA;IACtB,OAAO;MACL,OAAO;IACT;EACF;EAEA,IAAI4F,KAAA,EAAwC;IAC1C,IAAI,KAAKtJ,OAAA,EAAS;MAChB,OAAO,KAAKA,OAAA,CAAQsJ,IAAA;IACtB,OAAO;MACL,OAAO;IACT;EACF;EAEA,IAAI3F,aAAA,EAAwD;IAC1D,IAAI,KAAK3D,OAAA,EAAS;MAChB,OAAO,KAAKA,OAAA,CAAQ2D,YAAA;IACtB,OAAO;MACL,OAAO;IACT;EACF;EAEA,IAAI4F,wBAAA,EAA+B;IACjC,IAAI,KAAKvJ,OAAA,EAAS;MAChB,OAAQ,KAAKA,OAAA,CAAgBuJ,uBAAA;IAE/B,OAAO;MACL,OAAO;IACT;EACF;EAEAC,2BAAA,GAA8BjE,IAAA,EAAiB;IAC7C,IAAI,KAAKvF,OAAA,IAAW,gCAAgC,KAAKA,OAAA,EAAS;MAC/D,KAAKA,OAAA,CAAgBwJ,0BAAA,CAA2BjE,IAAI;IACvD,OAAO;MACL,OAAO;IACT;EACF;AA8bF;AAh0BEzG,OAAA,OAAA2K,OAAA;AACA1K,OAAA,OAAA0K,OAAA;AACAzK,SAAA,OAAAyK,OAAA;AACAxK,YAAA,OAAAwK,OAAA;AACAvK,eAAA,OAAAuK,OAAA;AAUOtK,SAAA,OAAAsK,OAAA;AA2JPrK,eAAA,OAAAsK,OAAA;AAAArK,iBAAA,GAAe,SAAAA,CAAA,EAAiD;EAC9D,OAAO,IAAIwE,OAAA,CAA6C8F,OAAA,IAAW;IACjE,IAAInI,YAAA,OAAK1C,OAAA,GAAS;MAChB6K,OAAA,CAAQ,KAAK3J,OAAQ;IACvB;IACA,KAAKkC,WAAA,CAAY,MAAMyH,OAAA,CAAQ,KAAK3J,OAAQ,CAAC;EAC/C,CAAC;AACH;AAlKAD,YAAA,CApCmBH,gBAAA,EAoCZT,SAAA,EAAP;AApCF,IAAqByK,eAAA,GAArBhK,gBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}