{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport { OrganizationProvider, OrganizationContext, useOrganizationContext } from \"@clerk/shared/react\";\nexport { OrganizationContext, OrganizationProvider, useOrganizationContext };", "map": {"version": 3, "names": ["OrganizationProvider", "OrganizationContext", "useOrganizationContext"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\contexts\\OrganizationContext.tsx"], "sourcesContent": ["export { OrganizationProvider, OrganizationContext, useOrganizationContext } from '@clerk/shared/react';\n"], "mappings": ";AAAA,SAASA,oBAAA,EAAsBC,mBAAA,EAAqBC,sBAAA,QAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}