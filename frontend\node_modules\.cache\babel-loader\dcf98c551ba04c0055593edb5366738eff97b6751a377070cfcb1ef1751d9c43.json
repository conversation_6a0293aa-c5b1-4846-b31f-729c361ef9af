{"ast": null, "code": "import React, { useEffect, useLayoutEffect, createContext, useContext, useMemo, useRef, createElement, useState, useCallback } from 'react';\n\n// Shared state between server components and client components\nconst noop = () => {};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/noop();\nconst OBJECT = Object;\nconst isUndefined = v => v === UNDEFINED;\nconst isFunction = v => typeof v == 'function';\nconst mergeObjects = (a, b) => ({\n  ...a,\n  ...b\n});\nconst isPromiseLike = x => isFunction(x.then);\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = arg => {\n  const type = typeof arg;\n  const constructor = arg && arg.constructor;\n  const isDate = constructor == Date;\n  let result;\n  let index;\n  if (OBJECT(arg) === arg && !isDate && constructor != RegExp) {\n    // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n    // If it's already hashed, directly return the result.\n    result = table.get(arg);\n    if (result) return result;\n    // Store the hash first for circular reference detection before entering the\n    // recursive `stableHash` calls.\n    // For other objects like set and map, we use this id directly as the hash.\n    result = ++counter + '~';\n    table.set(arg, result);\n    if (constructor == Array) {\n      // Array.\n      result = '@';\n      for (index = 0; index < arg.length; index++) {\n        result += stableHash(arg[index]) + ',';\n      }\n      table.set(arg, result);\n    }\n    if (constructor == OBJECT) {\n      // Object, sort keys.\n      result = '#';\n      const keys = OBJECT.keys(arg).sort();\n      while (!isUndefined(index = keys.pop())) {\n        if (!isUndefined(arg[index])) {\n          result += index + ':' + stableHash(arg[index]) + ',';\n        }\n      }\n      table.set(arg, result);\n    }\n  } else {\n    result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n  }\n  return result;\n};\n\n// Global state used to deduplicate requests and store listeners\nconst SWRGlobalState = new WeakMap();\nconst EMPTY_CACHE = {};\nconst INITIAL_CACHE = {};\nconst STR_UNDEFINED = 'undefined';\n// NOTE: Use the function to guarantee it's re-evaluated between jsdom and node runtime for tests.\nconst isWindowDefined = typeof window != STR_UNDEFINED;\nconst isDocumentDefined = typeof document != STR_UNDEFINED;\nconst hasRequestAnimationFrame = () => isWindowDefined && typeof window['requestAnimationFrame'] != STR_UNDEFINED;\nconst createCacheHelper = (cache, key) => {\n  const state = SWRGlobalState.get(cache);\n  return [\n  // Getter\n  () => !isUndefined(key) && cache.get(key) || EMPTY_CACHE,\n  // Setter\n  info => {\n    if (!isUndefined(key)) {\n      const prev = cache.get(key);\n      // Before writing to the store, we keep the value in the initial cache\n      // if it's not there yet.\n      if (!(key in INITIAL_CACHE)) {\n        INITIAL_CACHE[key] = prev;\n      }\n      state[5](key, mergeObjects(prev, info), prev || EMPTY_CACHE);\n    }\n  },\n  // Subscriber\n  state[6],\n  // Get server cache snapshot\n  () => {\n    if (!isUndefined(key)) {\n      // If the cache was updated on the client, we return the stored initial value.\n      if (key in INITIAL_CACHE) return INITIAL_CACHE[key];\n    }\n    // If we haven't done any client-side updates, we return the current value.\n    return !isUndefined(key) && cache.get(key) || EMPTY_CACHE;\n  }];\n} // export { UNDEFINED, OBJECT, isUndefined, isFunction, mergeObjects, isPromiseLike }\n;\n\n/**\n * Due to the bug https://bugs.chromium.org/p/chromium/issues/detail?id=678075,\n * it's not reliable to detect if the browser is currently online or offline\n * based on `navigator.onLine`.\n * As a workaround, we always assume it's online on the first load, and change\n * the status upon `online` or `offline` events.\n */\nlet online = true;\nconst isOnline = () => online;\n// For node and React Native, `add/removeEventListener` doesn't exist on window.\nconst [onWindowEvent, offWindowEvent] = isWindowDefined && window.addEventListener ? [window.addEventListener.bind(window), window.removeEventListener.bind(window)] : [noop, noop];\nconst isVisible = () => {\n  const visibilityState = isDocumentDefined && document.visibilityState;\n  return isUndefined(visibilityState) || visibilityState !== 'hidden';\n};\nconst initFocus = callback => {\n  // focus revalidate\n  if (isDocumentDefined) {\n    document.addEventListener('visibilitychange', callback);\n  }\n  onWindowEvent('focus', callback);\n  return () => {\n    if (isDocumentDefined) {\n      document.removeEventListener('visibilitychange', callback);\n    }\n    offWindowEvent('focus', callback);\n  };\n};\nconst initReconnect = callback => {\n  // revalidate on reconnected\n  const onOnline = () => {\n    online = true;\n    callback();\n  };\n  // nothing to revalidate, just update the status\n  const onOffline = () => {\n    online = false;\n  };\n  onWindowEvent('online', onOnline);\n  onWindowEvent('offline', onOffline);\n  return () => {\n    offWindowEvent('online', onOnline);\n    offWindowEvent('offline', onOffline);\n  };\n};\nconst preset = {\n  isOnline,\n  isVisible\n};\nconst defaultConfigOptions = {\n  initFocus,\n  initReconnect\n};\nconst IS_REACT_LEGACY = !React.useId;\nconst IS_SERVER = !isWindowDefined || 'Deno' in window;\n// Polyfill requestAnimationFrame\nconst rAF = f => hasRequestAnimationFrame() ? window['requestAnimationFrame'](f) : setTimeout(f, 1);\n// React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\nconst useIsomorphicLayoutEffect = IS_SERVER ? useEffect : useLayoutEffect;\n// This assignment is to extend the Navigator type to use effectiveType.\nconst navigatorConnection = typeof navigator !== 'undefined' && navigator.connection;\n// Adjust the config based on slow connection status (<= 70Kbps).\nconst slowConnection = !IS_SERVER && navigatorConnection && (['slow-2g', '2g'].includes(navigatorConnection.effectiveType) || navigatorConnection.saveData);\nconst serialize = key => {\n  if (isFunction(key)) {\n    try {\n      key = key();\n    } catch (err) {\n      // dependencies not ready\n      key = '';\n    }\n  }\n  // Use the original key as the argument of fetcher. This can be a string or an\n  // array of values.\n  const args = key;\n  // If key is not falsy, or not an empty array, hash it.\n  key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n  return [key, args];\n};\n\n// Global timestamp.\nlet __timestamp = 0;\nconst getTimestamp = () => ++__timestamp;\nconst FOCUS_EVENT = 0;\nconst RECONNECT_EVENT = 1;\nconst MUTATE_EVENT = 2;\nconst ERROR_REVALIDATE_EVENT = 3;\nvar constants = {\n  __proto__: null,\n  ERROR_REVALIDATE_EVENT: ERROR_REVALIDATE_EVENT,\n  FOCUS_EVENT: FOCUS_EVENT,\n  MUTATE_EVENT: MUTATE_EVENT,\n  RECONNECT_EVENT: RECONNECT_EVENT\n};\nasync function internalMutate(...args) {\n  const [cache, _key, _data, _opts] = args;\n  // When passing as a boolean, it's explicitly used to disable/enable\n  // revalidation.\n  const options = mergeObjects({\n    populateCache: true,\n    throwOnError: true\n  }, typeof _opts === 'boolean' ? {\n    revalidate: _opts\n  } : _opts || {});\n  let populateCache = options.populateCache;\n  const rollbackOnErrorOption = options.rollbackOnError;\n  let optimisticData = options.optimisticData;\n  const revalidate = options.revalidate !== false;\n  const rollbackOnError = error => {\n    return typeof rollbackOnErrorOption === 'function' ? rollbackOnErrorOption(error) : rollbackOnErrorOption !== false;\n  };\n  const throwOnError = options.throwOnError;\n  // If the second argument is a key filter, return the mutation results for all\n  // filtered keys.\n  if (isFunction(_key)) {\n    const keyFilter = _key;\n    const matchedKeys = [];\n    const it = cache.keys();\n    for (const key of it) {\n      if (\n      // Skip the special useSWRInfinite and useSWRSubscription keys.\n      !/^\\$(inf|sub)\\$/.test(key) && keyFilter(cache.get(key)._k)) {\n        matchedKeys.push(key);\n      }\n    }\n    return Promise.all(matchedKeys.map(mutateByKey));\n  }\n  return mutateByKey(_key);\n  async function mutateByKey(_k) {\n    // Serialize key\n    const [key] = serialize(_k);\n    if (!key) return;\n    const [get, set] = createCacheHelper(cache, key);\n    const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n    const revalidators = EVENT_REVALIDATORS[key];\n    const startRevalidate = () => {\n      if (revalidate) {\n        // Invalidate the key by deleting the concurrent request markers so new\n        // requests will not be deduped.\n        delete FETCH[key];\n        delete PRELOAD[key];\n        if (revalidators && revalidators[0]) {\n          return revalidators[0](MUTATE_EVENT).then(() => get().data);\n        }\n      }\n      return get().data;\n    };\n    // If there is no new data provided, revalidate the key with current state.\n    if (args.length < 3) {\n      // Revalidate and broadcast state.\n      return startRevalidate();\n    }\n    let data = _data;\n    let error;\n    // Update global timestamps.\n    const beforeMutationTs = getTimestamp();\n    MUTATION[key] = [beforeMutationTs, 0];\n    const hasOptimisticData = !isUndefined(optimisticData);\n    const state = get();\n    // `displayedData` is the current value on screen. It could be the optimistic value\n    // that is going to be overridden by a `committedData`, or get reverted back.\n    // `committedData` is the validated value that comes from a fetch or mutation.\n    const displayedData = state.data;\n    const currentData = state._c;\n    const committedData = isUndefined(currentData) ? displayedData : currentData;\n    // Do optimistic data update.\n    if (hasOptimisticData) {\n      optimisticData = isFunction(optimisticData) ? optimisticData(committedData, displayedData) : optimisticData;\n      // When we set optimistic data, backup the current committedData data in `_c`.\n      set({\n        data: optimisticData,\n        _c: committedData\n      });\n    }\n    if (isFunction(data)) {\n      // `data` is a function, call it passing current cache value.\n      try {\n        data = data(committedData);\n      } catch (err) {\n        // If it throws an error synchronously, we shouldn't update the cache.\n        error = err;\n      }\n    }\n    // `data` is a promise/thenable, resolve the final data first.\n    if (data && isPromiseLike(data)) {\n      // This means that the mutation is async, we need to check timestamps to\n      // avoid race conditions.\n      data = await data.catch(err => {\n        error = err;\n      });\n      // Check if other mutations have occurred since we've started this mutation.\n      // If there's a race we don't update cache or broadcast the change,\n      // just return the data.\n      if (beforeMutationTs !== MUTATION[key][0]) {\n        if (error) throw error;\n        return data;\n      } else if (error && hasOptimisticData && rollbackOnError(error)) {\n        // Rollback. Always populate the cache in this case but without\n        // transforming the data.\n        populateCache = true;\n        data = committedData;\n        // Reset data to be the latest committed data, and clear the `_c` value.\n        set({\n          data,\n          _c: UNDEFINED\n        });\n      }\n    }\n    // If we should write back the cache after request.\n    if (populateCache) {\n      if (!error) {\n        // Transform the result into data.\n        if (isFunction(populateCache)) {\n          data = populateCache(data, committedData);\n        }\n        // Only update cached data and reset the error if there's no error. Data can be `undefined` here.\n        set({\n          data,\n          error: UNDEFINED,\n          _c: UNDEFINED\n        });\n      }\n    }\n    // Reset the timestamp to mark the mutation has ended.\n    MUTATION[key][1] = getTimestamp();\n    // Update existing SWR Hooks' internal states:\n    const res = await startRevalidate();\n    // The mutation and revalidation are ended, we can clear it since the data is\n    // not an optimistic value anymore.\n    set({\n      _c: UNDEFINED\n    });\n    // Throw error or return data\n    if (error) {\n      if (throwOnError) throw error;\n      return;\n    }\n    return populateCache ? res : data;\n  }\n}\nconst revalidateAllKeys = (revalidators, type) => {\n  for (const key in revalidators) {\n    if (revalidators[key][0]) revalidators[key][0](type);\n  }\n};\nconst initCache = (provider, options) => {\n  // The global state for a specific provider will be used to deduplicate\n  // requests and store listeners. As well as a mutate function that is bound to\n  // the cache.\n  // The provider's global state might be already initialized. Let's try to get the\n  // global state associated with the provider first.\n  if (!SWRGlobalState.has(provider)) {\n    const opts = mergeObjects(defaultConfigOptions, options);\n    // If there's no global state bound to the provider, create a new one with the\n    // new mutate function.\n    const EVENT_REVALIDATORS = {};\n    const mutate = internalMutate.bind(UNDEFINED, provider);\n    let unmount = noop;\n    const subscriptions = {};\n    const subscribe = (key, callback) => {\n      const subs = subscriptions[key] || [];\n      subscriptions[key] = subs;\n      subs.push(callback);\n      return () => subs.splice(subs.indexOf(callback), 1);\n    };\n    const setter = (key, value, prev) => {\n      provider.set(key, value);\n      const subs = subscriptions[key];\n      if (subs) {\n        for (const fn of subs) {\n          fn(value, prev);\n        }\n      }\n    };\n    const initProvider = () => {\n      if (!SWRGlobalState.has(provider)) {\n        // Update the state if it's new, or if the provider has been extended.\n        SWRGlobalState.set(provider, [EVENT_REVALIDATORS, {}, {}, {}, mutate, setter, subscribe]);\n        if (!IS_SERVER) {\n          // When listening to the native events for auto revalidations,\n          // we intentionally put a delay (setTimeout) here to make sure they are\n          // fired after immediate JavaScript executions, which can be\n          // React's state updates.\n          // This avoids some unnecessary revalidations such as\n          // https://github.com/vercel/swr/issues/1680.\n          const releaseFocus = opts.initFocus(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, FOCUS_EVENT)));\n          const releaseReconnect = opts.initReconnect(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, RECONNECT_EVENT)));\n          unmount = () => {\n            releaseFocus && releaseFocus();\n            releaseReconnect && releaseReconnect();\n            // When un-mounting, we need to remove the cache provider from the state\n            // storage too because it's a side-effect. Otherwise, when re-mounting we\n            // will not re-register those event listeners.\n            SWRGlobalState.delete(provider);\n          };\n        }\n      }\n    };\n    initProvider();\n    // This is a new provider, we need to initialize it and setup DOM events\n    // listeners for `focus` and `reconnect` actions.\n    // We might want to inject an extra layer on top of `provider` in the future,\n    // such as key serialization, auto GC, etc.\n    // For now, it's just a `Map` interface without any modifications.\n    return [provider, mutate, initProvider, unmount];\n  }\n  return [provider, SWRGlobalState.get(provider)[4]];\n};\n\n// error retry\nconst onErrorRetry = (_, __, config, revalidate, opts) => {\n  const maxRetryCount = config.errorRetryCount;\n  const currentRetryCount = opts.retryCount;\n  // Exponential backoff\n  const timeout = ~~((Math.random() + 0.5) * (1 << (currentRetryCount < 8 ? currentRetryCount : 8))) * config.errorRetryInterval;\n  if (!isUndefined(maxRetryCount) && currentRetryCount > maxRetryCount) {\n    return;\n  }\n  setTimeout(revalidate, timeout, opts);\n};\nconst compare = (currentData, newData) => stableHash(currentData) == stableHash(newData);\n// Default cache provider\nconst [cache, mutate] = initCache(new Map());\n// Default config\nconst defaultConfig = mergeObjects({\n  // events\n  onLoadingSlow: noop,\n  onSuccess: noop,\n  onError: noop,\n  onErrorRetry,\n  onDiscarded: noop,\n  // switches\n  revalidateOnFocus: true,\n  revalidateOnReconnect: true,\n  revalidateIfStale: true,\n  shouldRetryOnError: true,\n  // timeouts\n  errorRetryInterval: slowConnection ? 10000 : 5000,\n  focusThrottleInterval: 5 * 1000,\n  dedupingInterval: 2 * 1000,\n  loadingTimeout: slowConnection ? 5000 : 3000,\n  // providers\n  compare,\n  isPaused: () => false,\n  cache,\n  mutate,\n  fallback: {}\n},\n// use web preset by default\npreset);\nconst mergeConfigs = (a, b) => {\n  // Need to create a new object to avoid mutating the original here.\n  const v = mergeObjects(a, b);\n  // If two configs are provided, merge their `use` and `fallback` options.\n  if (b) {\n    const {\n      use: u1,\n      fallback: f1\n    } = a;\n    const {\n      use: u2,\n      fallback: f2\n    } = b;\n    if (u1 && u2) {\n      v.use = u1.concat(u2);\n    }\n    if (f1 && f2) {\n      v.fallback = mergeObjects(f1, f2);\n    }\n  }\n  return v;\n};\nconst SWRConfigContext = createContext({});\nconst SWRConfig = props => {\n  const {\n    value\n  } = props;\n  const parentConfig = useContext(SWRConfigContext);\n  const isFunctionalConfig = isFunction(value);\n  const config = useMemo(() => isFunctionalConfig ? value(parentConfig) : value, [isFunctionalConfig, parentConfig, value]);\n  // Extend parent context values and middleware.\n  const extendedConfig = useMemo(() => isFunctionalConfig ? config : mergeConfigs(parentConfig, config), [isFunctionalConfig, parentConfig, config]);\n  // Should not use the inherited provider.\n  const provider = config && config.provider;\n  // initialize the cache only on first access.\n  const cacheContextRef = useRef(UNDEFINED);\n  if (provider && !cacheContextRef.current) {\n    cacheContextRef.current = initCache(provider(extendedConfig.cache || cache), config);\n  }\n  const cacheContext = cacheContextRef.current;\n  // Override the cache if a new provider is given.\n  if (cacheContext) {\n    extendedConfig.cache = cacheContext[0];\n    extendedConfig.mutate = cacheContext[1];\n  }\n  // Unsubscribe events.\n  useIsomorphicLayoutEffect(() => {\n    if (cacheContext) {\n      cacheContext[2] && cacheContext[2]();\n      return cacheContext[3];\n    }\n  }, []);\n  return createElement(SWRConfigContext.Provider, mergeObjects(props, {\n    value: extendedConfig\n  }));\n};\n\n// @ts-expect-error\nconst enableDevtools = isWindowDefined && window.__SWR_DEVTOOLS_USE__;\nconst use = enableDevtools ? window.__SWR_DEVTOOLS_USE__ : [];\nconst setupDevTools = () => {\n  if (enableDevtools) {\n    // @ts-expect-error\n    window.__SWR_DEVTOOLS_REACT__ = React;\n  }\n};\nconst normalize = args => {\n  return isFunction(args[1]) ? [args[0], args[1], args[2] || {}] : [args[0], null, (args[1] === null ? args[2] : args[1]) || {}];\n};\nconst useSWRConfig = () => {\n  return mergeObjects(defaultConfig, useContext(SWRConfigContext));\n};\nconst preload = (key_, fetcher) => {\n  const [key, fnArg] = serialize(key_);\n  const [,,, PRELOAD] = SWRGlobalState.get(cache);\n  // Prevent preload to be called multiple times before used.\n  if (PRELOAD[key]) return PRELOAD[key];\n  const req = fetcher(fnArg);\n  PRELOAD[key] = req;\n  return req;\n};\nconst middleware = useSWRNext => (key_, fetcher_, config) => {\n  // fetcher might be a sync function, so this should not be an async function\n  const fetcher = fetcher_ && ((...args) => {\n    const [key] = serialize(key_);\n    const [,,, PRELOAD] = SWRGlobalState.get(cache);\n    const req = PRELOAD[key];\n    if (isUndefined(req)) return fetcher_(...args);\n    delete PRELOAD[key];\n    return req;\n  });\n  return useSWRNext(key_, fetcher, config);\n};\nconst BUILT_IN_MIDDLEWARE = use.concat(middleware);\n\n// It's tricky to pass generic types as parameters, so we just directly override\n// the types here.\nconst withArgs = hook => {\n  return function useSWRArgs(...args) {\n    // Get the default and inherited configuration.\n    const fallbackConfig = useSWRConfig();\n    // Normalize arguments.\n    const [key, fn, _config] = normalize(args);\n    // Merge configurations.\n    const config = mergeConfigs(fallbackConfig, _config);\n    // Apply middleware\n    let next = hook;\n    const {\n      use\n    } = config;\n    const middleware = (use || []).concat(BUILT_IN_MIDDLEWARE);\n    for (let i = middleware.length; i--;) {\n      next = middleware[i](next);\n    }\n    return next(key, fn || config.fetcher || null, config);\n  };\n};\n\n/**\n * An implementation of state with dependency-tracking.\n */\nconst useStateWithDeps = state => {\n  const rerender = useState({})[1];\n  const unmountedRef = useRef(false);\n  const stateRef = useRef(state);\n  // If a state property (data, error, or isValidating) is accessed by the render\n  // function, we mark the property as a dependency so if it is updated again\n  // in the future, we trigger a rerender.\n  // This is also known as dependency-tracking.\n  const stateDependenciesRef = useRef({\n    data: false,\n    error: false,\n    isValidating: false\n  });\n  /**\n  * @param payload To change stateRef, pass the values explicitly to setState:\n  * @example\n  * ```js\n  * setState({\n  *   isValidating: false\n  *   data: newData // set data to newData\n  *   error: undefined // set error to undefined\n  * })\n  *\n  * setState({\n  *   isValidating: false\n  *   data: undefined // set data to undefined\n  *   error: err // set error to err\n  * })\n  * ```\n  */\n  const setState = useCallback(payload => {\n    let shouldRerender = false;\n    const currentState = stateRef.current;\n    for (const _ in payload) {\n      const k = _;\n      // If the property has changed, update the state and mark rerender as\n      // needed.\n      if (currentState[k] !== payload[k]) {\n        currentState[k] = payload[k];\n        // If the property is accessed by the component, a rerender should be\n        // triggered.\n        if (stateDependenciesRef.current[k]) {\n          shouldRerender = true;\n        }\n      }\n    }\n    if (shouldRerender && !unmountedRef.current) {\n      if (IS_REACT_LEGACY) {\n        rerender({});\n      } else {\n        React.startTransition(() => rerender({}));\n      }\n    }\n  }, [rerender]);\n  useIsomorphicLayoutEffect(() => {\n    unmountedRef.current = false;\n    return () => {\n      unmountedRef.current = true;\n    };\n  });\n  return [stateRef, stateDependenciesRef.current, setState];\n};\n\n// Add a callback function to a list of keyed callback functions and return\n// the unsubscribe function.\nconst subscribeCallback = (key, callbacks, callback) => {\n  const keyedRevalidators = callbacks[key] || (callbacks[key] = []);\n  keyedRevalidators.push(callback);\n  return () => {\n    const index = keyedRevalidators.indexOf(callback);\n    if (index >= 0) {\n      // O(1): faster than splice\n      keyedRevalidators[index] = keyedRevalidators[keyedRevalidators.length - 1];\n      keyedRevalidators.pop();\n    }\n  };\n};\n\n// Create a custom hook with a middleware\nconst withMiddleware = (useSWR, middleware) => {\n  return (...args) => {\n    const [key, fn, config] = normalize(args);\n    const uses = (config.use || []).concat(middleware);\n    return useSWR(key, fn, {\n      ...config,\n      use: uses\n    });\n  };\n};\nsetupDevTools();\nexport { IS_REACT_LEGACY, IS_SERVER, OBJECT, SWRConfig, SWRGlobalState, UNDEFINED, cache, compare, createCacheHelper, defaultConfig, defaultConfigOptions, getTimestamp, hasRequestAnimationFrame, initCache, internalMutate, isDocumentDefined, isFunction, isPromiseLike, isUndefined, isWindowDefined, mergeConfigs, mergeObjects, mutate, noop, normalize, preload, preset, rAF, constants as revalidateEvents, serialize, slowConnection, stableHash, subscribeCallback, useIsomorphicLayoutEffect, useSWRConfig, useStateWithDeps, withArgs, withMiddleware };", "map": {"version": 3, "names": ["React", "useEffect", "useLayoutEffect", "createContext", "useContext", "useMemo", "useRef", "createElement", "useState", "useCallback", "noop", "UNDEFINED", "OBJECT", "Object", "isUndefined", "v", "isFunction", "mergeObjects", "a", "b", "isPromiseLike", "x", "then", "table", "WeakMap", "counter", "stableHash", "arg", "type", "constructor", "isDate", "Date", "result", "index", "RegExp", "get", "set", "Array", "length", "keys", "sort", "pop", "toJSON", "toString", "JSON", "stringify", "SWRGlobalState", "EMPTY_CACHE", "INITIAL_CACHE", "STR_UNDEFINED", "isWindowDefined", "window", "isDocumentDefined", "document", "hasRequestAnimationFrame", "createCacheHelper", "cache", "key", "state", "info", "prev", "online", "isOnline", "onWindowEvent", "offWindowEvent", "addEventListener", "bind", "removeEventListener", "isVisible", "visibilityState", "initFocus", "callback", "initReconnect", "onOnline", "onOffline", "preset", "defaultConfigOptions", "IS_REACT_LEGACY", "useId", "IS_SERVER", "rAF", "f", "setTimeout", "useIsomorphicLayoutEffect", "navigatorConnection", "navigator", "connection", "slowConnection", "includes", "effectiveType", "saveData", "serialize", "err", "args", "isArray", "__timestamp", "getTimestamp", "FOCUS_EVENT", "RECONNECT_EVENT", "MUTATE_EVENT", "ERROR_REVALIDATE_EVENT", "constants", "__proto__", "internalMutate", "_key", "_data", "_opts", "options", "populateCache", "throwOnError", "revalidate", "rollbackOnErrorOption", "rollbackOnError", "optimisticData", "error", "keyFilter", "<PERSON><PERSON><PERSON><PERSON>", "it", "test", "_k", "push", "Promise", "all", "map", "mutateByKey", "EVENT_REVALIDATORS", "MUTATION", "FETCH", "PRELOAD", "revalidators", "startRevalidate", "data", "beforeMutationTs", "hasOptimisticData", "displayedData", "currentData", "_c", "committedData", "catch", "res", "revalidateAllKeys", "initCache", "provider", "has", "opts", "mutate", "unmount", "subscriptions", "subscribe", "subs", "splice", "indexOf", "setter", "value", "fn", "initProvider", "releaseFocus", "releaseReconnect", "delete", "onErrorRetry", "_", "__", "config", "maxRetryCount", "errorRetryCount", "currentRetryCount", "retryCount", "timeout", "Math", "random", "errorRetryInterval", "compare", "newData", "Map", "defaultConfig", "onLoadingSlow", "onSuccess", "onError", "onDiscarded", "revalidateOnFocus", "revalidateOnReconnect", "revalidateIfStale", "shouldRetryOnError", "focusThrottleInterval", "dedupingInterval", "loadingTimeout", "isPaused", "fallback", "mergeConfigs", "use", "u1", "f1", "u2", "f2", "concat", "SWRConfigContext", "SWRConfig", "props", "parentConfig", "isFunctionalConfig", "extendedConfig", "cacheContextRef", "current", "cacheContext", "Provider", "enableDevtools", "__SWR_DEVTOOLS_USE__", "setupDevTools", "__SWR_DEVTOOLS_REACT__", "normalize", "useSWRConfig", "preload", "key_", "fetcher", "fnArg", "req", "middleware", "useSWRNext", "fetcher_", "BUILT_IN_MIDDLEWARE", "<PERSON><PERSON><PERSON><PERSON>", "hook", "useSWRArgs", "fallbackConfig", "_config", "next", "i", "useStateWithDeps", "rerender", "unmountedRef", "stateRef", "stateDependenciesRef", "isValidating", "setState", "payload", "<PERSON><PERSON><PERSON><PERSON>", "currentState", "k", "startTransition", "subscribeCallback", "callbacks", "keyedRevalidators", "withMiddleware", "useSWR", "uses", "revalidateEvents"], "sources": ["C:/Users/<USER>/Desktop/file/u3summer/artech/artech/frontend/node_modules/swr/_internal/dist/index.mjs"], "sourcesContent": ["import React, { useEffect, useLayoutEffect, createContext, useContext, useMemo, useRef, createElement, useState, useCallback } from 'react';\n\n// Shared state between server components and client components\nconst noop = ()=>{};\n// Using noop() as the undefined value as undefined can be replaced\n// by something else. Prettier ignore and extra parentheses are necessary here\n// to ensure that tsc doesn't remove the __NOINLINE__ comment.\n// prettier-ignore\nconst UNDEFINED = /*#__NOINLINE__*/ noop();\nconst OBJECT = Object;\nconst isUndefined = (v)=>v === UNDEFINED;\nconst isFunction = (v)=>typeof v == 'function';\nconst mergeObjects = (a, b)=>({\n        ...a,\n        ...b\n    });\nconst isPromiseLike = (x)=>isFunction(x.then);\n\n// use WeakMap to store the object->key mapping\n// so the objects can be garbage collected.\n// WeakMap uses a hashtable under the hood, so the lookup\n// complexity is almost O(1).\nconst table = new WeakMap();\n// counter of the key\nlet counter = 0;\n// A stable hash implementation that supports:\n// - Fast and ensures unique hash properties\n// - Handles unserializable values\n// - Handles object key ordering\n// - Generates short results\n//\n// This is not a serialization function, and the result is not guaranteed to be\n// parsable.\nconst stableHash = (arg)=>{\n    const type = typeof arg;\n    const constructor = arg && arg.constructor;\n    const isDate = constructor == Date;\n    let result;\n    let index;\n    if (OBJECT(arg) === arg && !isDate && constructor != RegExp) {\n        // Object/function, not null/date/regexp. Use WeakMap to store the id first.\n        // If it's already hashed, directly return the result.\n        result = table.get(arg);\n        if (result) return result;\n        // Store the hash first for circular reference detection before entering the\n        // recursive `stableHash` calls.\n        // For other objects like set and map, we use this id directly as the hash.\n        result = ++counter + '~';\n        table.set(arg, result);\n        if (constructor == Array) {\n            // Array.\n            result = '@';\n            for(index = 0; index < arg.length; index++){\n                result += stableHash(arg[index]) + ',';\n            }\n            table.set(arg, result);\n        }\n        if (constructor == OBJECT) {\n            // Object, sort keys.\n            result = '#';\n            const keys = OBJECT.keys(arg).sort();\n            while(!isUndefined(index = keys.pop())){\n                if (!isUndefined(arg[index])) {\n                    result += index + ':' + stableHash(arg[index]) + ',';\n                }\n            }\n            table.set(arg, result);\n        }\n    } else {\n        result = isDate ? arg.toJSON() : type == 'symbol' ? arg.toString() : type == 'string' ? JSON.stringify(arg) : '' + arg;\n    }\n    return result;\n};\n\n// Global state used to deduplicate requests and store listeners\nconst SWRGlobalState = new WeakMap();\n\nconst EMPTY_CACHE = {};\nconst INITIAL_CACHE = {};\nconst STR_UNDEFINED = 'undefined';\n// NOTE: Use the function to guarantee it's re-evaluated between jsdom and node runtime for tests.\nconst isWindowDefined = typeof window != STR_UNDEFINED;\nconst isDocumentDefined = typeof document != STR_UNDEFINED;\nconst hasRequestAnimationFrame = ()=>isWindowDefined && typeof window['requestAnimationFrame'] != STR_UNDEFINED;\nconst createCacheHelper = (cache, key)=>{\n    const state = SWRGlobalState.get(cache);\n    return [\n        // Getter\n        ()=>!isUndefined(key) && cache.get(key) || EMPTY_CACHE,\n        // Setter\n        (info)=>{\n            if (!isUndefined(key)) {\n                const prev = cache.get(key);\n                // Before writing to the store, we keep the value in the initial cache\n                // if it's not there yet.\n                if (!(key in INITIAL_CACHE)) {\n                    INITIAL_CACHE[key] = prev;\n                }\n                state[5](key, mergeObjects(prev, info), prev || EMPTY_CACHE);\n            }\n        },\n        // Subscriber\n        state[6],\n        // Get server cache snapshot\n        ()=>{\n            if (!isUndefined(key)) {\n                // If the cache was updated on the client, we return the stored initial value.\n                if (key in INITIAL_CACHE) return INITIAL_CACHE[key];\n            }\n            // If we haven't done any client-side updates, we return the current value.\n            return !isUndefined(key) && cache.get(key) || EMPTY_CACHE;\n        }\n    ];\n} // export { UNDEFINED, OBJECT, isUndefined, isFunction, mergeObjects, isPromiseLike }\n;\n\n/**\n * Due to the bug https://bugs.chromium.org/p/chromium/issues/detail?id=678075,\n * it's not reliable to detect if the browser is currently online or offline\n * based on `navigator.onLine`.\n * As a workaround, we always assume it's online on the first load, and change\n * the status upon `online` or `offline` events.\n */ let online = true;\nconst isOnline = ()=>online;\n// For node and React Native, `add/removeEventListener` doesn't exist on window.\nconst [onWindowEvent, offWindowEvent] = isWindowDefined && window.addEventListener ? [\n    window.addEventListener.bind(window),\n    window.removeEventListener.bind(window)\n] : [\n    noop,\n    noop\n];\nconst isVisible = ()=>{\n    const visibilityState = isDocumentDefined && document.visibilityState;\n    return isUndefined(visibilityState) || visibilityState !== 'hidden';\n};\nconst initFocus = (callback)=>{\n    // focus revalidate\n    if (isDocumentDefined) {\n        document.addEventListener('visibilitychange', callback);\n    }\n    onWindowEvent('focus', callback);\n    return ()=>{\n        if (isDocumentDefined) {\n            document.removeEventListener('visibilitychange', callback);\n        }\n        offWindowEvent('focus', callback);\n    };\n};\nconst initReconnect = (callback)=>{\n    // revalidate on reconnected\n    const onOnline = ()=>{\n        online = true;\n        callback();\n    };\n    // nothing to revalidate, just update the status\n    const onOffline = ()=>{\n        online = false;\n    };\n    onWindowEvent('online', onOnline);\n    onWindowEvent('offline', onOffline);\n    return ()=>{\n        offWindowEvent('online', onOnline);\n        offWindowEvent('offline', onOffline);\n    };\n};\nconst preset = {\n    isOnline,\n    isVisible\n};\nconst defaultConfigOptions = {\n    initFocus,\n    initReconnect\n};\n\nconst IS_REACT_LEGACY = !React.useId;\nconst IS_SERVER = !isWindowDefined || 'Deno' in window;\n// Polyfill requestAnimationFrame\nconst rAF = (f)=>hasRequestAnimationFrame() ? window['requestAnimationFrame'](f) : setTimeout(f, 1);\n// React currently throws a warning when using useLayoutEffect on the server.\n// To get around it, we can conditionally useEffect on the server (no-op) and\n// useLayoutEffect in the browser.\nconst useIsomorphicLayoutEffect = IS_SERVER ? useEffect : useLayoutEffect;\n// This assignment is to extend the Navigator type to use effectiveType.\nconst navigatorConnection = typeof navigator !== 'undefined' && navigator.connection;\n// Adjust the config based on slow connection status (<= 70Kbps).\nconst slowConnection = !IS_SERVER && navigatorConnection && ([\n    'slow-2g',\n    '2g'\n].includes(navigatorConnection.effectiveType) || navigatorConnection.saveData);\n\nconst serialize = (key)=>{\n    if (isFunction(key)) {\n        try {\n            key = key();\n        } catch (err) {\n            // dependencies not ready\n            key = '';\n        }\n    }\n    // Use the original key as the argument of fetcher. This can be a string or an\n    // array of values.\n    const args = key;\n    // If key is not falsy, or not an empty array, hash it.\n    key = typeof key == 'string' ? key : (Array.isArray(key) ? key.length : key) ? stableHash(key) : '';\n    return [\n        key,\n        args\n    ];\n};\n\n// Global timestamp.\nlet __timestamp = 0;\nconst getTimestamp = ()=>++__timestamp;\n\nconst FOCUS_EVENT = 0;\nconst RECONNECT_EVENT = 1;\nconst MUTATE_EVENT = 2;\nconst ERROR_REVALIDATE_EVENT = 3;\n\nvar constants = {\n  __proto__: null,\n  ERROR_REVALIDATE_EVENT: ERROR_REVALIDATE_EVENT,\n  FOCUS_EVENT: FOCUS_EVENT,\n  MUTATE_EVENT: MUTATE_EVENT,\n  RECONNECT_EVENT: RECONNECT_EVENT\n};\n\nasync function internalMutate(...args) {\n    const [cache, _key, _data, _opts] = args;\n    // When passing as a boolean, it's explicitly used to disable/enable\n    // revalidation.\n    const options = mergeObjects({\n        populateCache: true,\n        throwOnError: true\n    }, typeof _opts === 'boolean' ? {\n        revalidate: _opts\n    } : _opts || {});\n    let populateCache = options.populateCache;\n    const rollbackOnErrorOption = options.rollbackOnError;\n    let optimisticData = options.optimisticData;\n    const revalidate = options.revalidate !== false;\n    const rollbackOnError = (error)=>{\n        return typeof rollbackOnErrorOption === 'function' ? rollbackOnErrorOption(error) : rollbackOnErrorOption !== false;\n    };\n    const throwOnError = options.throwOnError;\n    // If the second argument is a key filter, return the mutation results for all\n    // filtered keys.\n    if (isFunction(_key)) {\n        const keyFilter = _key;\n        const matchedKeys = [];\n        const it = cache.keys();\n        for (const key of it){\n            if (// Skip the special useSWRInfinite and useSWRSubscription keys.\n            !/^\\$(inf|sub)\\$/.test(key) && keyFilter(cache.get(key)._k)) {\n                matchedKeys.push(key);\n            }\n        }\n        return Promise.all(matchedKeys.map(mutateByKey));\n    }\n    return mutateByKey(_key);\n    async function mutateByKey(_k) {\n        // Serialize key\n        const [key] = serialize(_k);\n        if (!key) return;\n        const [get, set] = createCacheHelper(cache, key);\n        const [EVENT_REVALIDATORS, MUTATION, FETCH, PRELOAD] = SWRGlobalState.get(cache);\n        const revalidators = EVENT_REVALIDATORS[key];\n        const startRevalidate = ()=>{\n            if (revalidate) {\n                // Invalidate the key by deleting the concurrent request markers so new\n                // requests will not be deduped.\n                delete FETCH[key];\n                delete PRELOAD[key];\n                if (revalidators && revalidators[0]) {\n                    return revalidators[0](MUTATE_EVENT).then(()=>get().data);\n                }\n            }\n            return get().data;\n        };\n        // If there is no new data provided, revalidate the key with current state.\n        if (args.length < 3) {\n            // Revalidate and broadcast state.\n            return startRevalidate();\n        }\n        let data = _data;\n        let error;\n        // Update global timestamps.\n        const beforeMutationTs = getTimestamp();\n        MUTATION[key] = [\n            beforeMutationTs,\n            0\n        ];\n        const hasOptimisticData = !isUndefined(optimisticData);\n        const state = get();\n        // `displayedData` is the current value on screen. It could be the optimistic value\n        // that is going to be overridden by a `committedData`, or get reverted back.\n        // `committedData` is the validated value that comes from a fetch or mutation.\n        const displayedData = state.data;\n        const currentData = state._c;\n        const committedData = isUndefined(currentData) ? displayedData : currentData;\n        // Do optimistic data update.\n        if (hasOptimisticData) {\n            optimisticData = isFunction(optimisticData) ? optimisticData(committedData, displayedData) : optimisticData;\n            // When we set optimistic data, backup the current committedData data in `_c`.\n            set({\n                data: optimisticData,\n                _c: committedData\n            });\n        }\n        if (isFunction(data)) {\n            // `data` is a function, call it passing current cache value.\n            try {\n                data = data(committedData);\n            } catch (err) {\n                // If it throws an error synchronously, we shouldn't update the cache.\n                error = err;\n            }\n        }\n        // `data` is a promise/thenable, resolve the final data first.\n        if (data && isPromiseLike(data)) {\n            // This means that the mutation is async, we need to check timestamps to\n            // avoid race conditions.\n            data = await data.catch((err)=>{\n                error = err;\n            });\n            // Check if other mutations have occurred since we've started this mutation.\n            // If there's a race we don't update cache or broadcast the change,\n            // just return the data.\n            if (beforeMutationTs !== MUTATION[key][0]) {\n                if (error) throw error;\n                return data;\n            } else if (error && hasOptimisticData && rollbackOnError(error)) {\n                // Rollback. Always populate the cache in this case but without\n                // transforming the data.\n                populateCache = true;\n                data = committedData;\n                // Reset data to be the latest committed data, and clear the `_c` value.\n                set({\n                    data,\n                    _c: UNDEFINED\n                });\n            }\n        }\n        // If we should write back the cache after request.\n        if (populateCache) {\n            if (!error) {\n                // Transform the result into data.\n                if (isFunction(populateCache)) {\n                    data = populateCache(data, committedData);\n                }\n                // Only update cached data and reset the error if there's no error. Data can be `undefined` here.\n                set({\n                    data,\n                    error: UNDEFINED,\n                    _c: UNDEFINED\n                });\n            }\n        }\n        // Reset the timestamp to mark the mutation has ended.\n        MUTATION[key][1] = getTimestamp();\n        // Update existing SWR Hooks' internal states:\n        const res = await startRevalidate();\n        // The mutation and revalidation are ended, we can clear it since the data is\n        // not an optimistic value anymore.\n        set({\n            _c: UNDEFINED\n        });\n        // Throw error or return data\n        if (error) {\n            if (throwOnError) throw error;\n            return;\n        }\n        return populateCache ? res : data;\n    }\n}\n\nconst revalidateAllKeys = (revalidators, type)=>{\n    for(const key in revalidators){\n        if (revalidators[key][0]) revalidators[key][0](type);\n    }\n};\nconst initCache = (provider, options)=>{\n    // The global state for a specific provider will be used to deduplicate\n    // requests and store listeners. As well as a mutate function that is bound to\n    // the cache.\n    // The provider's global state might be already initialized. Let's try to get the\n    // global state associated with the provider first.\n    if (!SWRGlobalState.has(provider)) {\n        const opts = mergeObjects(defaultConfigOptions, options);\n        // If there's no global state bound to the provider, create a new one with the\n        // new mutate function.\n        const EVENT_REVALIDATORS = {};\n        const mutate = internalMutate.bind(UNDEFINED, provider);\n        let unmount = noop;\n        const subscriptions = {};\n        const subscribe = (key, callback)=>{\n            const subs = subscriptions[key] || [];\n            subscriptions[key] = subs;\n            subs.push(callback);\n            return ()=>subs.splice(subs.indexOf(callback), 1);\n        };\n        const setter = (key, value, prev)=>{\n            provider.set(key, value);\n            const subs = subscriptions[key];\n            if (subs) {\n                for (const fn of subs){\n                    fn(value, prev);\n                }\n            }\n        };\n        const initProvider = ()=>{\n            if (!SWRGlobalState.has(provider)) {\n                // Update the state if it's new, or if the provider has been extended.\n                SWRGlobalState.set(provider, [\n                    EVENT_REVALIDATORS,\n                    {},\n                    {},\n                    {},\n                    mutate,\n                    setter,\n                    subscribe\n                ]);\n                if (!IS_SERVER) {\n                    // When listening to the native events for auto revalidations,\n                    // we intentionally put a delay (setTimeout) here to make sure they are\n                    // fired after immediate JavaScript executions, which can be\n                    // React's state updates.\n                    // This avoids some unnecessary revalidations such as\n                    // https://github.com/vercel/swr/issues/1680.\n                    const releaseFocus = opts.initFocus(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, FOCUS_EVENT)));\n                    const releaseReconnect = opts.initReconnect(setTimeout.bind(UNDEFINED, revalidateAllKeys.bind(UNDEFINED, EVENT_REVALIDATORS, RECONNECT_EVENT)));\n                    unmount = ()=>{\n                        releaseFocus && releaseFocus();\n                        releaseReconnect && releaseReconnect();\n                        // When un-mounting, we need to remove the cache provider from the state\n                        // storage too because it's a side-effect. Otherwise, when re-mounting we\n                        // will not re-register those event listeners.\n                        SWRGlobalState.delete(provider);\n                    };\n                }\n            }\n        };\n        initProvider();\n        // This is a new provider, we need to initialize it and setup DOM events\n        // listeners for `focus` and `reconnect` actions.\n        // We might want to inject an extra layer on top of `provider` in the future,\n        // such as key serialization, auto GC, etc.\n        // For now, it's just a `Map` interface without any modifications.\n        return [\n            provider,\n            mutate,\n            initProvider,\n            unmount\n        ];\n    }\n    return [\n        provider,\n        SWRGlobalState.get(provider)[4]\n    ];\n};\n\n// error retry\nconst onErrorRetry = (_, __, config, revalidate, opts)=>{\n    const maxRetryCount = config.errorRetryCount;\n    const currentRetryCount = opts.retryCount;\n    // Exponential backoff\n    const timeout = ~~((Math.random() + 0.5) * (1 << (currentRetryCount < 8 ? currentRetryCount : 8))) * config.errorRetryInterval;\n    if (!isUndefined(maxRetryCount) && currentRetryCount > maxRetryCount) {\n        return;\n    }\n    setTimeout(revalidate, timeout, opts);\n};\nconst compare = (currentData, newData)=>stableHash(currentData) == stableHash(newData);\n// Default cache provider\nconst [cache, mutate] = initCache(new Map());\n// Default config\nconst defaultConfig = mergeObjects({\n    // events\n    onLoadingSlow: noop,\n    onSuccess: noop,\n    onError: noop,\n    onErrorRetry,\n    onDiscarded: noop,\n    // switches\n    revalidateOnFocus: true,\n    revalidateOnReconnect: true,\n    revalidateIfStale: true,\n    shouldRetryOnError: true,\n    // timeouts\n    errorRetryInterval: slowConnection ? 10000 : 5000,\n    focusThrottleInterval: 5 * 1000,\n    dedupingInterval: 2 * 1000,\n    loadingTimeout: slowConnection ? 5000 : 3000,\n    // providers\n    compare,\n    isPaused: ()=>false,\n    cache,\n    mutate,\n    fallback: {}\n}, // use web preset by default\npreset);\n\nconst mergeConfigs = (a, b)=>{\n    // Need to create a new object to avoid mutating the original here.\n    const v = mergeObjects(a, b);\n    // If two configs are provided, merge their `use` and `fallback` options.\n    if (b) {\n        const { use: u1 , fallback: f1  } = a;\n        const { use: u2 , fallback: f2  } = b;\n        if (u1 && u2) {\n            v.use = u1.concat(u2);\n        }\n        if (f1 && f2) {\n            v.fallback = mergeObjects(f1, f2);\n        }\n    }\n    return v;\n};\n\nconst SWRConfigContext = createContext({});\nconst SWRConfig = (props)=>{\n    const { value  } = props;\n    const parentConfig = useContext(SWRConfigContext);\n    const isFunctionalConfig = isFunction(value);\n    const config = useMemo(()=>isFunctionalConfig ? value(parentConfig) : value, [\n        isFunctionalConfig,\n        parentConfig,\n        value\n    ]);\n    // Extend parent context values and middleware.\n    const extendedConfig = useMemo(()=>isFunctionalConfig ? config : mergeConfigs(parentConfig, config), [\n        isFunctionalConfig,\n        parentConfig,\n        config\n    ]);\n    // Should not use the inherited provider.\n    const provider = config && config.provider;\n    // initialize the cache only on first access.\n    const cacheContextRef = useRef(UNDEFINED);\n    if (provider && !cacheContextRef.current) {\n        cacheContextRef.current = initCache(provider(extendedConfig.cache || cache), config);\n    }\n    const cacheContext = cacheContextRef.current;\n    // Override the cache if a new provider is given.\n    if (cacheContext) {\n        extendedConfig.cache = cacheContext[0];\n        extendedConfig.mutate = cacheContext[1];\n    }\n    // Unsubscribe events.\n    useIsomorphicLayoutEffect(()=>{\n        if (cacheContext) {\n            cacheContext[2] && cacheContext[2]();\n            return cacheContext[3];\n        }\n    }, []);\n    return createElement(SWRConfigContext.Provider, mergeObjects(props, {\n        value: extendedConfig\n    }));\n};\n\n// @ts-expect-error\nconst enableDevtools = isWindowDefined && window.__SWR_DEVTOOLS_USE__;\nconst use = enableDevtools ? window.__SWR_DEVTOOLS_USE__ : [];\nconst setupDevTools = ()=>{\n    if (enableDevtools) {\n        // @ts-expect-error\n        window.__SWR_DEVTOOLS_REACT__ = React;\n    }\n};\n\nconst normalize = (args)=>{\n    return isFunction(args[1]) ? [\n        args[0],\n        args[1],\n        args[2] || {}\n    ] : [\n        args[0],\n        null,\n        (args[1] === null ? args[2] : args[1]) || {}\n    ];\n};\n\nconst useSWRConfig = ()=>{\n    return mergeObjects(defaultConfig, useContext(SWRConfigContext));\n};\n\nconst preload = (key_, fetcher)=>{\n    const [key, fnArg] = serialize(key_);\n    const [, , , PRELOAD] = SWRGlobalState.get(cache);\n    // Prevent preload to be called multiple times before used.\n    if (PRELOAD[key]) return PRELOAD[key];\n    const req = fetcher(fnArg);\n    PRELOAD[key] = req;\n    return req;\n};\nconst middleware = (useSWRNext)=>(key_, fetcher_, config)=>{\n        // fetcher might be a sync function, so this should not be an async function\n        const fetcher = fetcher_ && ((...args)=>{\n            const [key] = serialize(key_);\n            const [, , , PRELOAD] = SWRGlobalState.get(cache);\n            const req = PRELOAD[key];\n            if (isUndefined(req)) return fetcher_(...args);\n            delete PRELOAD[key];\n            return req;\n        });\n        return useSWRNext(key_, fetcher, config);\n    };\n\nconst BUILT_IN_MIDDLEWARE = use.concat(middleware);\n\n// It's tricky to pass generic types as parameters, so we just directly override\n// the types here.\nconst withArgs = (hook)=>{\n    return function useSWRArgs(...args) {\n        // Get the default and inherited configuration.\n        const fallbackConfig = useSWRConfig();\n        // Normalize arguments.\n        const [key, fn, _config] = normalize(args);\n        // Merge configurations.\n        const config = mergeConfigs(fallbackConfig, _config);\n        // Apply middleware\n        let next = hook;\n        const { use  } = config;\n        const middleware = (use || []).concat(BUILT_IN_MIDDLEWARE);\n        for(let i = middleware.length; i--;){\n            next = middleware[i](next);\n        }\n        return next(key, fn || config.fetcher || null, config);\n    };\n};\n\n/**\n * An implementation of state with dependency-tracking.\n */ const useStateWithDeps = (state)=>{\n    const rerender = useState({})[1];\n    const unmountedRef = useRef(false);\n    const stateRef = useRef(state);\n    // If a state property (data, error, or isValidating) is accessed by the render\n    // function, we mark the property as a dependency so if it is updated again\n    // in the future, we trigger a rerender.\n    // This is also known as dependency-tracking.\n    const stateDependenciesRef = useRef({\n        data: false,\n        error: false,\n        isValidating: false\n    });\n    /**\n   * @param payload To change stateRef, pass the values explicitly to setState:\n   * @example\n   * ```js\n   * setState({\n   *   isValidating: false\n   *   data: newData // set data to newData\n   *   error: undefined // set error to undefined\n   * })\n   *\n   * setState({\n   *   isValidating: false\n   *   data: undefined // set data to undefined\n   *   error: err // set error to err\n   * })\n   * ```\n   */ const setState = useCallback((payload)=>{\n        let shouldRerender = false;\n        const currentState = stateRef.current;\n        for(const _ in payload){\n            const k = _;\n            // If the property has changed, update the state and mark rerender as\n            // needed.\n            if (currentState[k] !== payload[k]) {\n                currentState[k] = payload[k];\n                // If the property is accessed by the component, a rerender should be\n                // triggered.\n                if (stateDependenciesRef.current[k]) {\n                    shouldRerender = true;\n                }\n            }\n        }\n        if (shouldRerender && !unmountedRef.current) {\n            if (IS_REACT_LEGACY) {\n                rerender({});\n            } else {\n                React.startTransition(()=>rerender({}));\n            }\n        }\n    }, [\n        rerender\n    ]);\n    useIsomorphicLayoutEffect(()=>{\n        unmountedRef.current = false;\n        return ()=>{\n            unmountedRef.current = true;\n        };\n    });\n    return [\n        stateRef,\n        stateDependenciesRef.current,\n        setState\n    ];\n};\n\n// Add a callback function to a list of keyed callback functions and return\n// the unsubscribe function.\nconst subscribeCallback = (key, callbacks, callback)=>{\n    const keyedRevalidators = callbacks[key] || (callbacks[key] = []);\n    keyedRevalidators.push(callback);\n    return ()=>{\n        const index = keyedRevalidators.indexOf(callback);\n        if (index >= 0) {\n            // O(1): faster than splice\n            keyedRevalidators[index] = keyedRevalidators[keyedRevalidators.length - 1];\n            keyedRevalidators.pop();\n        }\n    };\n};\n\n// Create a custom hook with a middleware\nconst withMiddleware = (useSWR, middleware)=>{\n    return (...args)=>{\n        const [key, fn, config] = normalize(args);\n        const uses = (config.use || []).concat(middleware);\n        return useSWR(key, fn, {\n            ...config,\n            use: uses\n        });\n    };\n};\n\nsetupDevTools();\n\nexport { IS_REACT_LEGACY, IS_SERVER, OBJECT, SWRConfig, SWRGlobalState, UNDEFINED, cache, compare, createCacheHelper, defaultConfig, defaultConfigOptions, getTimestamp, hasRequestAnimationFrame, initCache, internalMutate, isDocumentDefined, isFunction, isPromiseLike, isUndefined, isWindowDefined, mergeConfigs, mergeObjects, mutate, noop, normalize, preload, preset, rAF, constants as revalidateEvents, serialize, slowConnection, stableHash, subscribeCallback, useIsomorphicLayoutEffect, useSWRConfig, useStateWithDeps, withArgs, withMiddleware };\n"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,eAAe,EAAEC,aAAa,EAAEC,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;;AAE3I;AACA,MAAMC,IAAI,GAAGA,CAAA,KAAI,CAAC,CAAC;AACnB;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAG,iBAAkBD,IAAI,CAAC,CAAC;AAC1C,MAAME,MAAM,GAAGC,MAAM;AACrB,MAAMC,WAAW,GAAIC,CAAC,IAAGA,CAAC,KAAKJ,SAAS;AACxC,MAAMK,UAAU,GAAID,CAAC,IAAG,OAAOA,CAAC,IAAI,UAAU;AAC9C,MAAME,YAAY,GAAGA,CAACC,CAAC,EAAEC,CAAC,MAAI;EACtB,GAAGD,CAAC;EACJ,GAAGC;AACP,CAAC,CAAC;AACN,MAAMC,aAAa,GAAIC,CAAC,IAAGL,UAAU,CAACK,CAAC,CAACC,IAAI,CAAC;;AAE7C;AACA;AACA;AACA;AACA,MAAMC,KAAK,GAAG,IAAIC,OAAO,CAAC,CAAC;AAC3B;AACA,IAAIC,OAAO,GAAG,CAAC;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAIC,GAAG,IAAG;EACtB,MAAMC,IAAI,GAAG,OAAOD,GAAG;EACvB,MAAME,WAAW,GAAGF,GAAG,IAAIA,GAAG,CAACE,WAAW;EAC1C,MAAMC,MAAM,GAAGD,WAAW,IAAIE,IAAI;EAClC,IAAIC,MAAM;EACV,IAAIC,KAAK;EACT,IAAIrB,MAAM,CAACe,GAAG,CAAC,KAAKA,GAAG,IAAI,CAACG,MAAM,IAAID,WAAW,IAAIK,MAAM,EAAE;IACzD;IACA;IACAF,MAAM,GAAGT,KAAK,CAACY,GAAG,CAACR,GAAG,CAAC;IACvB,IAAIK,MAAM,EAAE,OAAOA,MAAM;IACzB;IACA;IACA;IACAA,MAAM,GAAG,EAAEP,OAAO,GAAG,GAAG;IACxBF,KAAK,CAACa,GAAG,CAACT,GAAG,EAAEK,MAAM,CAAC;IACtB,IAAIH,WAAW,IAAIQ,KAAK,EAAE;MACtB;MACAL,MAAM,GAAG,GAAG;MACZ,KAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGN,GAAG,CAACW,MAAM,EAAEL,KAAK,EAAE,EAAC;QACvCD,MAAM,IAAIN,UAAU,CAACC,GAAG,CAACM,KAAK,CAAC,CAAC,GAAG,GAAG;MAC1C;MACAV,KAAK,CAACa,GAAG,CAACT,GAAG,EAAEK,MAAM,CAAC;IAC1B;IACA,IAAIH,WAAW,IAAIjB,MAAM,EAAE;MACvB;MACAoB,MAAM,GAAG,GAAG;MACZ,MAAMO,IAAI,GAAG3B,MAAM,CAAC2B,IAAI,CAACZ,GAAG,CAAC,CAACa,IAAI,CAAC,CAAC;MACpC,OAAM,CAAC1B,WAAW,CAACmB,KAAK,GAAGM,IAAI,CAACE,GAAG,CAAC,CAAC,CAAC,EAAC;QACnC,IAAI,CAAC3B,WAAW,CAACa,GAAG,CAACM,KAAK,CAAC,CAAC,EAAE;UAC1BD,MAAM,IAAIC,KAAK,GAAG,GAAG,GAAGP,UAAU,CAACC,GAAG,CAACM,KAAK,CAAC,CAAC,GAAG,GAAG;QACxD;MACJ;MACAV,KAAK,CAACa,GAAG,CAACT,GAAG,EAAEK,MAAM,CAAC;IAC1B;EACJ,CAAC,MAAM;IACHA,MAAM,GAAGF,MAAM,GAAGH,GAAG,CAACe,MAAM,CAAC,CAAC,GAAGd,IAAI,IAAI,QAAQ,GAAGD,GAAG,CAACgB,QAAQ,CAAC,CAAC,GAAGf,IAAI,IAAI,QAAQ,GAAGgB,IAAI,CAACC,SAAS,CAAClB,GAAG,CAAC,GAAG,EAAE,GAAGA,GAAG;EAC1H;EACA,OAAOK,MAAM;AACjB,CAAC;;AAED;AACA,MAAMc,cAAc,GAAG,IAAItB,OAAO,CAAC,CAAC;AAEpC,MAAMuB,WAAW,GAAG,CAAC,CAAC;AACtB,MAAMC,aAAa,GAAG,CAAC,CAAC;AACxB,MAAMC,aAAa,GAAG,WAAW;AACjC;AACA,MAAMC,eAAe,GAAG,OAAOC,MAAM,IAAIF,aAAa;AACtD,MAAMG,iBAAiB,GAAG,OAAOC,QAAQ,IAAIJ,aAAa;AAC1D,MAAMK,wBAAwB,GAAGA,CAAA,KAAIJ,eAAe,IAAI,OAAOC,MAAM,CAAC,uBAAuB,CAAC,IAAIF,aAAa;AAC/G,MAAMM,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,GAAG,KAAG;EACpC,MAAMC,KAAK,GAAGZ,cAAc,CAACX,GAAG,CAACqB,KAAK,CAAC;EACvC,OAAO;EACH;EACA,MAAI,CAAC1C,WAAW,CAAC2C,GAAG,CAAC,IAAID,KAAK,CAACrB,GAAG,CAACsB,GAAG,CAAC,IAAIV,WAAW;EACtD;EACCY,IAAI,IAAG;IACJ,IAAI,CAAC7C,WAAW,CAAC2C,GAAG,CAAC,EAAE;MACnB,MAAMG,IAAI,GAAGJ,KAAK,CAACrB,GAAG,CAACsB,GAAG,CAAC;MAC3B;MACA;MACA,IAAI,EAAEA,GAAG,IAAIT,aAAa,CAAC,EAAE;QACzBA,aAAa,CAACS,GAAG,CAAC,GAAGG,IAAI;MAC7B;MACAF,KAAK,CAAC,CAAC,CAAC,CAACD,GAAG,EAAExC,YAAY,CAAC2C,IAAI,EAAED,IAAI,CAAC,EAAEC,IAAI,IAAIb,WAAW,CAAC;IAChE;EACJ,CAAC;EACD;EACAW,KAAK,CAAC,CAAC,CAAC;EACR;EACA,MAAI;IACA,IAAI,CAAC5C,WAAW,CAAC2C,GAAG,CAAC,EAAE;MACnB;MACA,IAAIA,GAAG,IAAIT,aAAa,EAAE,OAAOA,aAAa,CAACS,GAAG,CAAC;IACvD;IACA;IACA,OAAO,CAAC3C,WAAW,CAAC2C,GAAG,CAAC,IAAID,KAAK,CAACrB,GAAG,CAACsB,GAAG,CAAC,IAAIV,WAAW;EAC7D,CAAC,CACJ;AACL,CAAC,CAAC;AAAA;;AAGF;AACA;AACA;AACA;AACA;AACA;AACA;AAAI,IAAIc,MAAM,GAAG,IAAI;AACrB,MAAMC,QAAQ,GAAGA,CAAA,KAAID,MAAM;AAC3B;AACA,MAAM,CAACE,aAAa,EAAEC,cAAc,CAAC,GAAGd,eAAe,IAAIC,MAAM,CAACc,gBAAgB,GAAG,CACjFd,MAAM,CAACc,gBAAgB,CAACC,IAAI,CAACf,MAAM,CAAC,EACpCA,MAAM,CAACgB,mBAAmB,CAACD,IAAI,CAACf,MAAM,CAAC,CAC1C,GAAG,CACAzC,IAAI,EACJA,IAAI,CACP;AACD,MAAM0D,SAAS,GAAGA,CAAA,KAAI;EAClB,MAAMC,eAAe,GAAGjB,iBAAiB,IAAIC,QAAQ,CAACgB,eAAe;EACrE,OAAOvD,WAAW,CAACuD,eAAe,CAAC,IAAIA,eAAe,KAAK,QAAQ;AACvE,CAAC;AACD,MAAMC,SAAS,GAAIC,QAAQ,IAAG;EAC1B;EACA,IAAInB,iBAAiB,EAAE;IACnBC,QAAQ,CAACY,gBAAgB,CAAC,kBAAkB,EAAEM,QAAQ,CAAC;EAC3D;EACAR,aAAa,CAAC,OAAO,EAAEQ,QAAQ,CAAC;EAChC,OAAO,MAAI;IACP,IAAInB,iBAAiB,EAAE;MACnBC,QAAQ,CAACc,mBAAmB,CAAC,kBAAkB,EAAEI,QAAQ,CAAC;IAC9D;IACAP,cAAc,CAAC,OAAO,EAAEO,QAAQ,CAAC;EACrC,CAAC;AACL,CAAC;AACD,MAAMC,aAAa,GAAID,QAAQ,IAAG;EAC9B;EACA,MAAME,QAAQ,GAAGA,CAAA,KAAI;IACjBZ,MAAM,GAAG,IAAI;IACbU,QAAQ,CAAC,CAAC;EACd,CAAC;EACD;EACA,MAAMG,SAAS,GAAGA,CAAA,KAAI;IAClBb,MAAM,GAAG,KAAK;EAClB,CAAC;EACDE,aAAa,CAAC,QAAQ,EAAEU,QAAQ,CAAC;EACjCV,aAAa,CAAC,SAAS,EAAEW,SAAS,CAAC;EACnC,OAAO,MAAI;IACPV,cAAc,CAAC,QAAQ,EAAES,QAAQ,CAAC;IAClCT,cAAc,CAAC,SAAS,EAAEU,SAAS,CAAC;EACxC,CAAC;AACL,CAAC;AACD,MAAMC,MAAM,GAAG;EACXb,QAAQ;EACRM;AACJ,CAAC;AACD,MAAMQ,oBAAoB,GAAG;EACzBN,SAAS;EACTE;AACJ,CAAC;AAED,MAAMK,eAAe,GAAG,CAAC7E,KAAK,CAAC8E,KAAK;AACpC,MAAMC,SAAS,GAAG,CAAC7B,eAAe,IAAI,MAAM,IAAIC,MAAM;AACtD;AACA,MAAM6B,GAAG,GAAIC,CAAC,IAAG3B,wBAAwB,CAAC,CAAC,GAAGH,MAAM,CAAC,uBAAuB,CAAC,CAAC8B,CAAC,CAAC,GAAGC,UAAU,CAACD,CAAC,EAAE,CAAC,CAAC;AACnG;AACA;AACA;AACA,MAAME,yBAAyB,GAAGJ,SAAS,GAAG9E,SAAS,GAAGC,eAAe;AACzE;AACA,MAAMkF,mBAAmB,GAAG,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAACC,UAAU;AACpF;AACA,MAAMC,cAAc,GAAG,CAACR,SAAS,IAAIK,mBAAmB,KAAK,CACzD,SAAS,EACT,IAAI,CACP,CAACI,QAAQ,CAACJ,mBAAmB,CAACK,aAAa,CAAC,IAAIL,mBAAmB,CAACM,QAAQ,CAAC;AAE9E,MAAMC,SAAS,GAAIlC,GAAG,IAAG;EACrB,IAAIzC,UAAU,CAACyC,GAAG,CAAC,EAAE;IACjB,IAAI;MACAA,GAAG,GAAGA,GAAG,CAAC,CAAC;IACf,CAAC,CAAC,OAAOmC,GAAG,EAAE;MACV;MACAnC,GAAG,GAAG,EAAE;IACZ;EACJ;EACA;EACA;EACA,MAAMoC,IAAI,GAAGpC,GAAG;EAChB;EACAA,GAAG,GAAG,OAAOA,GAAG,IAAI,QAAQ,GAAGA,GAAG,GAAG,CAACpB,KAAK,CAACyD,OAAO,CAACrC,GAAG,CAAC,GAAGA,GAAG,CAACnB,MAAM,GAAGmB,GAAG,IAAI/B,UAAU,CAAC+B,GAAG,CAAC,GAAG,EAAE;EACnG,OAAO,CACHA,GAAG,EACHoC,IAAI,CACP;AACL,CAAC;;AAED;AACA,IAAIE,WAAW,GAAG,CAAC;AACnB,MAAMC,YAAY,GAAGA,CAAA,KAAI,EAAED,WAAW;AAEtC,MAAME,WAAW,GAAG,CAAC;AACrB,MAAMC,eAAe,GAAG,CAAC;AACzB,MAAMC,YAAY,GAAG,CAAC;AACtB,MAAMC,sBAAsB,GAAG,CAAC;AAEhC,IAAIC,SAAS,GAAG;EACdC,SAAS,EAAE,IAAI;EACfF,sBAAsB,EAAEA,sBAAsB;EAC9CH,WAAW,EAAEA,WAAW;EACxBE,YAAY,EAAEA,YAAY;EAC1BD,eAAe,EAAEA;AACnB,CAAC;AAED,eAAeK,cAAcA,CAAC,GAAGV,IAAI,EAAE;EACnC,MAAM,CAACrC,KAAK,EAAEgD,IAAI,EAAEC,KAAK,EAAEC,KAAK,CAAC,GAAGb,IAAI;EACxC;EACA;EACA,MAAMc,OAAO,GAAG1F,YAAY,CAAC;IACzB2F,aAAa,EAAE,IAAI;IACnBC,YAAY,EAAE;EAClB,CAAC,EAAE,OAAOH,KAAK,KAAK,SAAS,GAAG;IAC5BI,UAAU,EAAEJ;EAChB,CAAC,GAAGA,KAAK,IAAI,CAAC,CAAC,CAAC;EAChB,IAAIE,aAAa,GAAGD,OAAO,CAACC,aAAa;EACzC,MAAMG,qBAAqB,GAAGJ,OAAO,CAACK,eAAe;EACrD,IAAIC,cAAc,GAAGN,OAAO,CAACM,cAAc;EAC3C,MAAMH,UAAU,GAAGH,OAAO,CAACG,UAAU,KAAK,KAAK;EAC/C,MAAME,eAAe,GAAIE,KAAK,IAAG;IAC7B,OAAO,OAAOH,qBAAqB,KAAK,UAAU,GAAGA,qBAAqB,CAACG,KAAK,CAAC,GAAGH,qBAAqB,KAAK,KAAK;EACvH,CAAC;EACD,MAAMF,YAAY,GAAGF,OAAO,CAACE,YAAY;EACzC;EACA;EACA,IAAI7F,UAAU,CAACwF,IAAI,CAAC,EAAE;IAClB,MAAMW,SAAS,GAAGX,IAAI;IACtB,MAAMY,WAAW,GAAG,EAAE;IACtB,MAAMC,EAAE,GAAG7D,KAAK,CAACjB,IAAI,CAAC,CAAC;IACvB,KAAK,MAAMkB,GAAG,IAAI4D,EAAE,EAAC;MACjB;MAAI;MACJ,CAAC,gBAAgB,CAACC,IAAI,CAAC7D,GAAG,CAAC,IAAI0D,SAAS,CAAC3D,KAAK,CAACrB,GAAG,CAACsB,GAAG,CAAC,CAAC8D,EAAE,CAAC,EAAE;QACzDH,WAAW,CAACI,IAAI,CAAC/D,GAAG,CAAC;MACzB;IACJ;IACA,OAAOgE,OAAO,CAACC,GAAG,CAACN,WAAW,CAACO,GAAG,CAACC,WAAW,CAAC,CAAC;EACpD;EACA,OAAOA,WAAW,CAACpB,IAAI,CAAC;EACxB,eAAeoB,WAAWA,CAACL,EAAE,EAAE;IAC3B;IACA,MAAM,CAAC9D,GAAG,CAAC,GAAGkC,SAAS,CAAC4B,EAAE,CAAC;IAC3B,IAAI,CAAC9D,GAAG,EAAE;IACV,MAAM,CAACtB,GAAG,EAAEC,GAAG,CAAC,GAAGmB,iBAAiB,CAACC,KAAK,EAAEC,GAAG,CAAC;IAChD,MAAM,CAACoE,kBAAkB,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,CAAC,GAAGlF,cAAc,CAACX,GAAG,CAACqB,KAAK,CAAC;IAChF,MAAMyE,YAAY,GAAGJ,kBAAkB,CAACpE,GAAG,CAAC;IAC5C,MAAMyE,eAAe,GAAGA,CAAA,KAAI;MACxB,IAAIpB,UAAU,EAAE;QACZ;QACA;QACA,OAAOiB,KAAK,CAACtE,GAAG,CAAC;QACjB,OAAOuE,OAAO,CAACvE,GAAG,CAAC;QACnB,IAAIwE,YAAY,IAAIA,YAAY,CAAC,CAAC,CAAC,EAAE;UACjC,OAAOA,YAAY,CAAC,CAAC,CAAC,CAAC9B,YAAY,CAAC,CAAC7E,IAAI,CAAC,MAAIa,GAAG,CAAC,CAAC,CAACgG,IAAI,CAAC;QAC7D;MACJ;MACA,OAAOhG,GAAG,CAAC,CAAC,CAACgG,IAAI;IACrB,CAAC;IACD;IACA,IAAItC,IAAI,CAACvD,MAAM,GAAG,CAAC,EAAE;MACjB;MACA,OAAO4F,eAAe,CAAC,CAAC;IAC5B;IACA,IAAIC,IAAI,GAAG1B,KAAK;IAChB,IAAIS,KAAK;IACT;IACA,MAAMkB,gBAAgB,GAAGpC,YAAY,CAAC,CAAC;IACvC8B,QAAQ,CAACrE,GAAG,CAAC,GAAG,CACZ2E,gBAAgB,EAChB,CAAC,CACJ;IACD,MAAMC,iBAAiB,GAAG,CAACvH,WAAW,CAACmG,cAAc,CAAC;IACtD,MAAMvD,KAAK,GAAGvB,GAAG,CAAC,CAAC;IACnB;IACA;IACA;IACA,MAAMmG,aAAa,GAAG5E,KAAK,CAACyE,IAAI;IAChC,MAAMI,WAAW,GAAG7E,KAAK,CAAC8E,EAAE;IAC5B,MAAMC,aAAa,GAAG3H,WAAW,CAACyH,WAAW,CAAC,GAAGD,aAAa,GAAGC,WAAW;IAC5E;IACA,IAAIF,iBAAiB,EAAE;MACnBpB,cAAc,GAAGjG,UAAU,CAACiG,cAAc,CAAC,GAAGA,cAAc,CAACwB,aAAa,EAAEH,aAAa,CAAC,GAAGrB,cAAc;MAC3G;MACA7E,GAAG,CAAC;QACA+F,IAAI,EAAElB,cAAc;QACpBuB,EAAE,EAAEC;MACR,CAAC,CAAC;IACN;IACA,IAAIzH,UAAU,CAACmH,IAAI,CAAC,EAAE;MAClB;MACA,IAAI;QACAA,IAAI,GAAGA,IAAI,CAACM,aAAa,CAAC;MAC9B,CAAC,CAAC,OAAO7C,GAAG,EAAE;QACV;QACAsB,KAAK,GAAGtB,GAAG;MACf;IACJ;IACA;IACA,IAAIuC,IAAI,IAAI/G,aAAa,CAAC+G,IAAI,CAAC,EAAE;MAC7B;MACA;MACAA,IAAI,GAAG,MAAMA,IAAI,CAACO,KAAK,CAAE9C,GAAG,IAAG;QAC3BsB,KAAK,GAAGtB,GAAG;MACf,CAAC,CAAC;MACF;MACA;MACA;MACA,IAAIwC,gBAAgB,KAAKN,QAAQ,CAACrE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;QACvC,IAAIyD,KAAK,EAAE,MAAMA,KAAK;QACtB,OAAOiB,IAAI;MACf,CAAC,MAAM,IAAIjB,KAAK,IAAImB,iBAAiB,IAAIrB,eAAe,CAACE,KAAK,CAAC,EAAE;QAC7D;QACA;QACAN,aAAa,GAAG,IAAI;QACpBuB,IAAI,GAAGM,aAAa;QACpB;QACArG,GAAG,CAAC;UACA+F,IAAI;UACJK,EAAE,EAAE7H;QACR,CAAC,CAAC;MACN;IACJ;IACA;IACA,IAAIiG,aAAa,EAAE;MACf,IAAI,CAACM,KAAK,EAAE;QACR;QACA,IAAIlG,UAAU,CAAC4F,aAAa,CAAC,EAAE;UAC3BuB,IAAI,GAAGvB,aAAa,CAACuB,IAAI,EAAEM,aAAa,CAAC;QAC7C;QACA;QACArG,GAAG,CAAC;UACA+F,IAAI;UACJjB,KAAK,EAAEvG,SAAS;UAChB6H,EAAE,EAAE7H;QACR,CAAC,CAAC;MACN;IACJ;IACA;IACAmH,QAAQ,CAACrE,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGuC,YAAY,CAAC,CAAC;IACjC;IACA,MAAM2C,GAAG,GAAG,MAAMT,eAAe,CAAC,CAAC;IACnC;IACA;IACA9F,GAAG,CAAC;MACAoG,EAAE,EAAE7H;IACR,CAAC,CAAC;IACF;IACA,IAAIuG,KAAK,EAAE;MACP,IAAIL,YAAY,EAAE,MAAMK,KAAK;MAC7B;IACJ;IACA,OAAON,aAAa,GAAG+B,GAAG,GAAGR,IAAI;EACrC;AACJ;AAEA,MAAMS,iBAAiB,GAAGA,CAACX,YAAY,EAAErG,IAAI,KAAG;EAC5C,KAAI,MAAM6B,GAAG,IAAIwE,YAAY,EAAC;IAC1B,IAAIA,YAAY,CAACxE,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEwE,YAAY,CAACxE,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC7B,IAAI,CAAC;EACxD;AACJ,CAAC;AACD,MAAMiH,SAAS,GAAGA,CAACC,QAAQ,EAAEnC,OAAO,KAAG;EACnC;EACA;EACA;EACA;EACA;EACA,IAAI,CAAC7D,cAAc,CAACiG,GAAG,CAACD,QAAQ,CAAC,EAAE;IAC/B,MAAME,IAAI,GAAG/H,YAAY,CAAC2D,oBAAoB,EAAE+B,OAAO,CAAC;IACxD;IACA;IACA,MAAMkB,kBAAkB,GAAG,CAAC,CAAC;IAC7B,MAAMoB,MAAM,GAAG1C,cAAc,CAACrC,IAAI,CAACvD,SAAS,EAAEmI,QAAQ,CAAC;IACvD,IAAII,OAAO,GAAGxI,IAAI;IAClB,MAAMyI,aAAa,GAAG,CAAC,CAAC;IACxB,MAAMC,SAAS,GAAGA,CAAC3F,GAAG,EAAEc,QAAQ,KAAG;MAC/B,MAAM8E,IAAI,GAAGF,aAAa,CAAC1F,GAAG,CAAC,IAAI,EAAE;MACrC0F,aAAa,CAAC1F,GAAG,CAAC,GAAG4F,IAAI;MACzBA,IAAI,CAAC7B,IAAI,CAACjD,QAAQ,CAAC;MACnB,OAAO,MAAI8E,IAAI,CAACC,MAAM,CAACD,IAAI,CAACE,OAAO,CAAChF,QAAQ,CAAC,EAAE,CAAC,CAAC;IACrD,CAAC;IACD,MAAMiF,MAAM,GAAGA,CAAC/F,GAAG,EAAEgG,KAAK,EAAE7F,IAAI,KAAG;MAC/BkF,QAAQ,CAAC1G,GAAG,CAACqB,GAAG,EAAEgG,KAAK,CAAC;MACxB,MAAMJ,IAAI,GAAGF,aAAa,CAAC1F,GAAG,CAAC;MAC/B,IAAI4F,IAAI,EAAE;QACN,KAAK,MAAMK,EAAE,IAAIL,IAAI,EAAC;UAClBK,EAAE,CAACD,KAAK,EAAE7F,IAAI,CAAC;QACnB;MACJ;IACJ,CAAC;IACD,MAAM+F,YAAY,GAAGA,CAAA,KAAI;MACrB,IAAI,CAAC7G,cAAc,CAACiG,GAAG,CAACD,QAAQ,CAAC,EAAE;QAC/B;QACAhG,cAAc,CAACV,GAAG,CAAC0G,QAAQ,EAAE,CACzBjB,kBAAkB,EAClB,CAAC,CAAC,EACF,CAAC,CAAC,EACF,CAAC,CAAC,EACFoB,MAAM,EACNO,MAAM,EACNJ,SAAS,CACZ,CAAC;QACF,IAAI,CAACrE,SAAS,EAAE;UACZ;UACA;UACA;UACA;UACA;UACA;UACA,MAAM6E,YAAY,GAAGZ,IAAI,CAAC1E,SAAS,CAACY,UAAU,CAAChB,IAAI,CAACvD,SAAS,EAAEiI,iBAAiB,CAAC1E,IAAI,CAACvD,SAAS,EAAEkH,kBAAkB,EAAE5B,WAAW,CAAC,CAAC,CAAC;UACnI,MAAM4D,gBAAgB,GAAGb,IAAI,CAACxE,aAAa,CAACU,UAAU,CAAChB,IAAI,CAACvD,SAAS,EAAEiI,iBAAiB,CAAC1E,IAAI,CAACvD,SAAS,EAAEkH,kBAAkB,EAAE3B,eAAe,CAAC,CAAC,CAAC;UAC/IgD,OAAO,GAAGA,CAAA,KAAI;YACVU,YAAY,IAAIA,YAAY,CAAC,CAAC;YAC9BC,gBAAgB,IAAIA,gBAAgB,CAAC,CAAC;YACtC;YACA;YACA;YACA/G,cAAc,CAACgH,MAAM,CAAChB,QAAQ,CAAC;UACnC,CAAC;QACL;MACJ;IACJ,CAAC;IACDa,YAAY,CAAC,CAAC;IACd;IACA;IACA;IACA;IACA;IACA,OAAO,CACHb,QAAQ,EACRG,MAAM,EACNU,YAAY,EACZT,OAAO,CACV;EACL;EACA,OAAO,CACHJ,QAAQ,EACRhG,cAAc,CAACX,GAAG,CAAC2G,QAAQ,CAAC,CAAC,CAAC,CAAC,CAClC;AACL,CAAC;;AAED;AACA,MAAMiB,YAAY,GAAGA,CAACC,CAAC,EAAEC,EAAE,EAAEC,MAAM,EAAEpD,UAAU,EAAEkC,IAAI,KAAG;EACpD,MAAMmB,aAAa,GAAGD,MAAM,CAACE,eAAe;EAC5C,MAAMC,iBAAiB,GAAGrB,IAAI,CAACsB,UAAU;EACzC;EACA,MAAMC,OAAO,GAAG,CAAC,EAAE,CAACC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG,KAAK,CAAC,KAAKJ,iBAAiB,GAAG,CAAC,GAAGA,iBAAiB,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGH,MAAM,CAACQ,kBAAkB;EAC9H,IAAI,CAAC5J,WAAW,CAACqJ,aAAa,CAAC,IAAIE,iBAAiB,GAAGF,aAAa,EAAE;IAClE;EACJ;EACAjF,UAAU,CAAC4B,UAAU,EAAEyD,OAAO,EAAEvB,IAAI,CAAC;AACzC,CAAC;AACD,MAAM2B,OAAO,GAAGA,CAACpC,WAAW,EAAEqC,OAAO,KAAGlJ,UAAU,CAAC6G,WAAW,CAAC,IAAI7G,UAAU,CAACkJ,OAAO,CAAC;AACtF;AACA,MAAM,CAACpH,KAAK,EAAEyF,MAAM,CAAC,GAAGJ,SAAS,CAAC,IAAIgC,GAAG,CAAC,CAAC,CAAC;AAC5C;AACA,MAAMC,aAAa,GAAG7J,YAAY,CAAC;EAC/B;EACA8J,aAAa,EAAErK,IAAI;EACnBsK,SAAS,EAAEtK,IAAI;EACfuK,OAAO,EAAEvK,IAAI;EACbqJ,YAAY;EACZmB,WAAW,EAAExK,IAAI;EACjB;EACAyK,iBAAiB,EAAE,IAAI;EACvBC,qBAAqB,EAAE,IAAI;EAC3BC,iBAAiB,EAAE,IAAI;EACvBC,kBAAkB,EAAE,IAAI;EACxB;EACAZ,kBAAkB,EAAEnF,cAAc,GAAG,KAAK,GAAG,IAAI;EACjDgG,qBAAqB,EAAE,CAAC,GAAG,IAAI;EAC/BC,gBAAgB,EAAE,CAAC,GAAG,IAAI;EAC1BC,cAAc,EAAElG,cAAc,GAAG,IAAI,GAAG,IAAI;EAC5C;EACAoF,OAAO;EACPe,QAAQ,EAAEA,CAAA,KAAI,KAAK;EACnBlI,KAAK;EACLyF,MAAM;EACN0C,QAAQ,EAAE,CAAC;AACf,CAAC;AAAE;AACHhH,MAAM,CAAC;AAEP,MAAMiH,YAAY,GAAGA,CAAC1K,CAAC,EAAEC,CAAC,KAAG;EACzB;EACA,MAAMJ,CAAC,GAAGE,YAAY,CAACC,CAAC,EAAEC,CAAC,CAAC;EAC5B;EACA,IAAIA,CAAC,EAAE;IACH,MAAM;MAAE0K,GAAG,EAAEC,EAAE;MAAGH,QAAQ,EAAEI;IAAI,CAAC,GAAG7K,CAAC;IACrC,MAAM;MAAE2K,GAAG,EAAEG,EAAE;MAAGL,QAAQ,EAAEM;IAAI,CAAC,GAAG9K,CAAC;IACrC,IAAI2K,EAAE,IAAIE,EAAE,EAAE;MACVjL,CAAC,CAAC8K,GAAG,GAAGC,EAAE,CAACI,MAAM,CAACF,EAAE,CAAC;IACzB;IACA,IAAID,EAAE,IAAIE,EAAE,EAAE;MACVlL,CAAC,CAAC4K,QAAQ,GAAG1K,YAAY,CAAC8K,EAAE,EAAEE,EAAE,CAAC;IACrC;EACJ;EACA,OAAOlL,CAAC;AACZ,CAAC;AAED,MAAMoL,gBAAgB,GAAGhM,aAAa,CAAC,CAAC,CAAC,CAAC;AAC1C,MAAMiM,SAAS,GAAIC,KAAK,IAAG;EACvB,MAAM;IAAE5C;EAAO,CAAC,GAAG4C,KAAK;EACxB,MAAMC,YAAY,GAAGlM,UAAU,CAAC+L,gBAAgB,CAAC;EACjD,MAAMI,kBAAkB,GAAGvL,UAAU,CAACyI,KAAK,CAAC;EAC5C,MAAMS,MAAM,GAAG7J,OAAO,CAAC,MAAIkM,kBAAkB,GAAG9C,KAAK,CAAC6C,YAAY,CAAC,GAAG7C,KAAK,EAAE,CACzE8C,kBAAkB,EAClBD,YAAY,EACZ7C,KAAK,CACR,CAAC;EACF;EACA,MAAM+C,cAAc,GAAGnM,OAAO,CAAC,MAAIkM,kBAAkB,GAAGrC,MAAM,GAAG0B,YAAY,CAACU,YAAY,EAAEpC,MAAM,CAAC,EAAE,CACjGqC,kBAAkB,EAClBD,YAAY,EACZpC,MAAM,CACT,CAAC;EACF;EACA,MAAMpB,QAAQ,GAAGoB,MAAM,IAAIA,MAAM,CAACpB,QAAQ;EAC1C;EACA,MAAM2D,eAAe,GAAGnM,MAAM,CAACK,SAAS,CAAC;EACzC,IAAImI,QAAQ,IAAI,CAAC2D,eAAe,CAACC,OAAO,EAAE;IACtCD,eAAe,CAACC,OAAO,GAAG7D,SAAS,CAACC,QAAQ,CAAC0D,cAAc,CAAChJ,KAAK,IAAIA,KAAK,CAAC,EAAE0G,MAAM,CAAC;EACxF;EACA,MAAMyC,YAAY,GAAGF,eAAe,CAACC,OAAO;EAC5C;EACA,IAAIC,YAAY,EAAE;IACdH,cAAc,CAAChJ,KAAK,GAAGmJ,YAAY,CAAC,CAAC,CAAC;IACtCH,cAAc,CAACvD,MAAM,GAAG0D,YAAY,CAAC,CAAC,CAAC;EAC3C;EACA;EACAxH,yBAAyB,CAAC,MAAI;IAC1B,IAAIwH,YAAY,EAAE;MACdA,YAAY,CAAC,CAAC,CAAC,IAAIA,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;MACpC,OAAOA,YAAY,CAAC,CAAC,CAAC;IAC1B;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,OAAOpM,aAAa,CAAC4L,gBAAgB,CAACS,QAAQ,EAAE3L,YAAY,CAACoL,KAAK,EAAE;IAChE5C,KAAK,EAAE+C;EACX,CAAC,CAAC,CAAC;AACP,CAAC;;AAED;AACA,MAAMK,cAAc,GAAG3J,eAAe,IAAIC,MAAM,CAAC2J,oBAAoB;AACrE,MAAMjB,GAAG,GAAGgB,cAAc,GAAG1J,MAAM,CAAC2J,oBAAoB,GAAG,EAAE;AAC7D,MAAMC,aAAa,GAAGA,CAAA,KAAI;EACtB,IAAIF,cAAc,EAAE;IAChB;IACA1J,MAAM,CAAC6J,sBAAsB,GAAGhN,KAAK;EACzC;AACJ,CAAC;AAED,MAAMiN,SAAS,GAAIpH,IAAI,IAAG;EACtB,OAAO7E,UAAU,CAAC6E,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CACzBA,IAAI,CAAC,CAAC,CAAC,EACPA,IAAI,CAAC,CAAC,CAAC,EACPA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAChB,GAAG,CACAA,IAAI,CAAC,CAAC,CAAC,EACP,IAAI,EACJ,CAACA,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAGA,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAC/C;AACL,CAAC;AAED,MAAMqH,YAAY,GAAGA,CAAA,KAAI;EACrB,OAAOjM,YAAY,CAAC6J,aAAa,EAAE1K,UAAU,CAAC+L,gBAAgB,CAAC,CAAC;AACpE,CAAC;AAED,MAAMgB,OAAO,GAAGA,CAACC,IAAI,EAAEC,OAAO,KAAG;EAC7B,MAAM,CAAC5J,GAAG,EAAE6J,KAAK,CAAC,GAAG3H,SAAS,CAACyH,IAAI,CAAC;EACpC,MAAM,KAAOpF,OAAO,CAAC,GAAGlF,cAAc,CAACX,GAAG,CAACqB,KAAK,CAAC;EACjD;EACA,IAAIwE,OAAO,CAACvE,GAAG,CAAC,EAAE,OAAOuE,OAAO,CAACvE,GAAG,CAAC;EACrC,MAAM8J,GAAG,GAAGF,OAAO,CAACC,KAAK,CAAC;EAC1BtF,OAAO,CAACvE,GAAG,CAAC,GAAG8J,GAAG;EAClB,OAAOA,GAAG;AACd,CAAC;AACD,MAAMC,UAAU,GAAIC,UAAU,IAAG,CAACL,IAAI,EAAEM,QAAQ,EAAExD,MAAM,KAAG;EACnD;EACA,MAAMmD,OAAO,GAAGK,QAAQ,KAAK,CAAC,GAAG7H,IAAI,KAAG;IACpC,MAAM,CAACpC,GAAG,CAAC,GAAGkC,SAAS,CAACyH,IAAI,CAAC;IAC7B,MAAM,KAAOpF,OAAO,CAAC,GAAGlF,cAAc,CAACX,GAAG,CAACqB,KAAK,CAAC;IACjD,MAAM+J,GAAG,GAAGvF,OAAO,CAACvE,GAAG,CAAC;IACxB,IAAI3C,WAAW,CAACyM,GAAG,CAAC,EAAE,OAAOG,QAAQ,CAAC,GAAG7H,IAAI,CAAC;IAC9C,OAAOmC,OAAO,CAACvE,GAAG,CAAC;IACnB,OAAO8J,GAAG;EACd,CAAC,CAAC;EACF,OAAOE,UAAU,CAACL,IAAI,EAAEC,OAAO,EAAEnD,MAAM,CAAC;AAC5C,CAAC;AAEL,MAAMyD,mBAAmB,GAAG9B,GAAG,CAACK,MAAM,CAACsB,UAAU,CAAC;;AAElD;AACA;AACA,MAAMI,QAAQ,GAAIC,IAAI,IAAG;EACrB,OAAO,SAASC,UAAUA,CAAC,GAAGjI,IAAI,EAAE;IAChC;IACA,MAAMkI,cAAc,GAAGb,YAAY,CAAC,CAAC;IACrC;IACA,MAAM,CAACzJ,GAAG,EAAEiG,EAAE,EAAEsE,OAAO,CAAC,GAAGf,SAAS,CAACpH,IAAI,CAAC;IAC1C;IACA,MAAMqE,MAAM,GAAG0B,YAAY,CAACmC,cAAc,EAAEC,OAAO,CAAC;IACpD;IACA,IAAIC,IAAI,GAAGJ,IAAI;IACf,MAAM;MAAEhC;IAAK,CAAC,GAAG3B,MAAM;IACvB,MAAMsD,UAAU,GAAG,CAAC3B,GAAG,IAAI,EAAE,EAAEK,MAAM,CAACyB,mBAAmB,CAAC;IAC1D,KAAI,IAAIO,CAAC,GAAGV,UAAU,CAAClL,MAAM,EAAE4L,CAAC,EAAE,GAAE;MAChCD,IAAI,GAAGT,UAAU,CAACU,CAAC,CAAC,CAACD,IAAI,CAAC;IAC9B;IACA,OAAOA,IAAI,CAACxK,GAAG,EAAEiG,EAAE,IAAIQ,MAAM,CAACmD,OAAO,IAAI,IAAI,EAAEnD,MAAM,CAAC;EAC1D,CAAC;AACL,CAAC;;AAED;AACA;AACA;AAAI,MAAMiE,gBAAgB,GAAIzK,KAAK,IAAG;EAClC,MAAM0K,QAAQ,GAAG5N,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAChC,MAAM6N,YAAY,GAAG/N,MAAM,CAAC,KAAK,CAAC;EAClC,MAAMgO,QAAQ,GAAGhO,MAAM,CAACoD,KAAK,CAAC;EAC9B;EACA;EACA;EACA;EACA,MAAM6K,oBAAoB,GAAGjO,MAAM,CAAC;IAChC6H,IAAI,EAAE,KAAK;IACXjB,KAAK,EAAE,KAAK;IACZsH,YAAY,EAAE;EAClB,CAAC,CAAC;EACF;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EAAM,MAAMC,QAAQ,GAAGhO,WAAW,CAAEiO,OAAO,IAAG;IACtC,IAAIC,cAAc,GAAG,KAAK;IAC1B,MAAMC,YAAY,GAAGN,QAAQ,CAAC5B,OAAO;IACrC,KAAI,MAAM1C,CAAC,IAAI0E,OAAO,EAAC;MACnB,MAAMG,CAAC,GAAG7E,CAAC;MACX;MACA;MACA,IAAI4E,YAAY,CAACC,CAAC,CAAC,KAAKH,OAAO,CAACG,CAAC,CAAC,EAAE;QAChCD,YAAY,CAACC,CAAC,CAAC,GAAGH,OAAO,CAACG,CAAC,CAAC;QAC5B;QACA;QACA,IAAIN,oBAAoB,CAAC7B,OAAO,CAACmC,CAAC,CAAC,EAAE;UACjCF,cAAc,GAAG,IAAI;QACzB;MACJ;IACJ;IACA,IAAIA,cAAc,IAAI,CAACN,YAAY,CAAC3B,OAAO,EAAE;MACzC,IAAI7H,eAAe,EAAE;QACjBuJ,QAAQ,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,MAAM;QACHpO,KAAK,CAAC8O,eAAe,CAAC,MAAIV,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3C;IACJ;EACJ,CAAC,EAAE,CACCA,QAAQ,CACX,CAAC;EACFjJ,yBAAyB,CAAC,MAAI;IAC1BkJ,YAAY,CAAC3B,OAAO,GAAG,KAAK;IAC5B,OAAO,MAAI;MACP2B,YAAY,CAAC3B,OAAO,GAAG,IAAI;IAC/B,CAAC;EACL,CAAC,CAAC;EACF,OAAO,CACH4B,QAAQ,EACRC,oBAAoB,CAAC7B,OAAO,EAC5B+B,QAAQ,CACX;AACL,CAAC;;AAED;AACA;AACA,MAAMM,iBAAiB,GAAGA,CAACtL,GAAG,EAAEuL,SAAS,EAAEzK,QAAQ,KAAG;EAClD,MAAM0K,iBAAiB,GAAGD,SAAS,CAACvL,GAAG,CAAC,KAAKuL,SAAS,CAACvL,GAAG,CAAC,GAAG,EAAE,CAAC;EACjEwL,iBAAiB,CAACzH,IAAI,CAACjD,QAAQ,CAAC;EAChC,OAAO,MAAI;IACP,MAAMtC,KAAK,GAAGgN,iBAAiB,CAAC1F,OAAO,CAAChF,QAAQ,CAAC;IACjD,IAAItC,KAAK,IAAI,CAAC,EAAE;MACZ;MACAgN,iBAAiB,CAAChN,KAAK,CAAC,GAAGgN,iBAAiB,CAACA,iBAAiB,CAAC3M,MAAM,GAAG,CAAC,CAAC;MAC1E2M,iBAAiB,CAACxM,GAAG,CAAC,CAAC;IAC3B;EACJ,CAAC;AACL,CAAC;;AAED;AACA,MAAMyM,cAAc,GAAGA,CAACC,MAAM,EAAE3B,UAAU,KAAG;EACzC,OAAO,CAAC,GAAG3H,IAAI,KAAG;IACd,MAAM,CAACpC,GAAG,EAAEiG,EAAE,EAAEQ,MAAM,CAAC,GAAG+C,SAAS,CAACpH,IAAI,CAAC;IACzC,MAAMuJ,IAAI,GAAG,CAAClF,MAAM,CAAC2B,GAAG,IAAI,EAAE,EAAEK,MAAM,CAACsB,UAAU,CAAC;IAClD,OAAO2B,MAAM,CAAC1L,GAAG,EAAEiG,EAAE,EAAE;MACnB,GAAGQ,MAAM;MACT2B,GAAG,EAAEuD;IACT,CAAC,CAAC;EACN,CAAC;AACL,CAAC;AAEDrC,aAAa,CAAC,CAAC;AAEf,SAASlI,eAAe,EAAEE,SAAS,EAAEnE,MAAM,EAAEwL,SAAS,EAAEtJ,cAAc,EAAEnC,SAAS,EAAE6C,KAAK,EAAEmH,OAAO,EAAEpH,iBAAiB,EAAEuH,aAAa,EAAElG,oBAAoB,EAAEoB,YAAY,EAAE1C,wBAAwB,EAAEuF,SAAS,EAAEtC,cAAc,EAAEnD,iBAAiB,EAAEpC,UAAU,EAAEI,aAAa,EAAEN,WAAW,EAAEoC,eAAe,EAAE0I,YAAY,EAAE3K,YAAY,EAAEgI,MAAM,EAAEvI,IAAI,EAAEuM,SAAS,EAAEE,OAAO,EAAExI,MAAM,EAAEK,GAAG,EAAEqB,SAAS,IAAIgJ,gBAAgB,EAAE1J,SAAS,EAAEJ,cAAc,EAAE7D,UAAU,EAAEqN,iBAAiB,EAAE5J,yBAAyB,EAAE+H,YAAY,EAAEiB,gBAAgB,EAAEP,QAAQ,EAAEsB,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}