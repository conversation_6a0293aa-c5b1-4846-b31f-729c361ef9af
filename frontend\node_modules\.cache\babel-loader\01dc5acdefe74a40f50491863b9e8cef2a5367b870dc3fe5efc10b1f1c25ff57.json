{"ast": null, "code": "// src/utils/runtimeEnvironment.ts\nvar isDevelopmentEnvironment = () => {\n  try {\n    return process.env.NODE_ENV === \"development\";\n  } catch (err) {}\n  return false;\n};\nvar isTestEnvironment = () => {\n  try {\n    return process.env.NODE_ENV === \"test\";\n  } catch (err) {}\n  return false;\n};\nvar isProductionEnvironment = () => {\n  try {\n    return process.env.NODE_ENV === \"production\";\n  } catch (err) {}\n  return false;\n};\n\n// src/deprecated.ts\nvar displayedWarnings = /* @__PURE__ */new Set();\nvar deprecated = (fnName, warning, key) => {\n  const hideWarning = isTestEnvironment() || isProductionEnvironment();\n  const messageId = key != null ? key : fnName;\n  if (displayedWarnings.has(messageId) || hideWarning) {\n    return;\n  }\n  displayedWarnings.add(messageId);\n  console.warn(`Clerk - DEPRECATION WARNING: \"${fnName}\" is deprecated and will be removed in the next major release.\n${warning}`);\n};\nvar deprecatedProperty = (cls, propName, warning, isStatic = false) => {\n  const target = isStatic ? cls : cls.prototype;\n  let value = target[propName];\n  Object.defineProperty(target, propName, {\n    get() {\n      deprecated(propName, warning, `${cls.name}:${propName}`);\n      return value;\n    },\n    set(v) {\n      value = v;\n    }\n  });\n};\nvar deprecatedObjectProperty = (obj, propName, warning, key) => {\n  let value = obj[propName];\n  Object.defineProperty(obj, propName, {\n    get() {\n      deprecated(propName, warning, key);\n      return value;\n    },\n    set(v) {\n      value = v;\n    }\n  });\n};\nexport { isDevelopmentEnvironment, isTestEnvironment, isProductionEnvironment, deprecated, deprecatedProperty, deprecatedObjectProperty };", "map": {"version": 3, "names": ["isDevelopmentEnvironment", "process", "env", "NODE_ENV", "err", "isTestEnvironment", "isProductionEnvironment", "displayedWarnings", "Set", "deprecated", "fnName", "warning", "key", "hideWarning", "messageId", "has", "add", "console", "warn", "deprecatedProperty", "cls", "propName", "isStatic", "target", "prototype", "value", "Object", "defineProperty", "get", "name", "set", "v", "deprecatedObjectProperty", "obj"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\utils\\runtimeEnvironment.ts", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\deprecated.ts"], "sourcesContent": ["export const isDevelopmentEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'development';\n    // eslint-disable-next-line no-empty\n  } catch (err) {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n\n  return false;\n};\n\nexport const isTestEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'test';\n    // eslint-disable-next-line no-empty\n  } catch (err) {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n\nexport const isProductionEnvironment = (): boolean => {\n  try {\n    return process.env.NODE_ENV === 'production';\n    // eslint-disable-next-line no-empty\n  } catch (err) {}\n\n  // TODO: add support for import.meta.env.DEV that is being used by vite\n  return false;\n};\n", "import { isProductionEnvironment, isTestEnvironment } from './utils/runtimeEnvironment';\n/**\n * Mark class method / function as deprecated.\n *\n * A console WARNING will be displayed when class method / function is invoked.\n *\n * Examples\n * 1. Deprecate class method\n * class Example {\n *   getSomething = (arg1, arg2) => {\n *       deprecated('Example.getSomething', 'Use `getSomethingElse` instead.');\n *       return `getSomethingValue:${arg1 || '-'}:${arg2 || '-'}`;\n *   };\n * }\n *\n * 2. Deprecate function\n * const getSomething = () => {\n *   deprecated('getSomething', 'Use `getSomethingElse` instead.');\n *   return 'getSomethingValue';\n * };\n */\nconst displayedWarnings = new Set<string>();\nexport const deprecated = (fnName: string, warning: string, key?: string): void => {\n  const hideWarning = isTestEnvironment() || isProductionEnvironment();\n  const messageId = key ?? fnName;\n  if (displayedWarnings.has(messageId) || hideWarning) {\n    return;\n  }\n  displayedWarnings.add(messageId);\n\n  console.warn(\n    `Clerk - DEPRECATION WARNING: \"${fnName}\" is deprecated and will be removed in the next major release.\\n${warning}`,\n  );\n};\n/**\n * Mark class property as deprecated.\n *\n * A console WARNING will be displayed when class property is being accessed.\n *\n * 1. Deprecate class property\n * class Example {\n *   something: string;\n *   constructor(something: string) {\n *     this.something = something;\n *   }\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.');\n *\n * 2. Deprecate class static property\n * class Example {\n *   static something: string;\n * }\n *\n * deprecatedProperty(Example, 'something', 'Use `somethingElse` instead.', true);\n */\ntype AnyClass = new (...args: any[]) => any;\n\nexport const deprecatedProperty = (cls: AnyClass, propName: string, warning: string, isStatic = false): void => {\n  const target = isStatic ? cls : cls.prototype;\n\n  let value = target[propName];\n  Object.defineProperty(target, propName, {\n    get() {\n      deprecated(propName, warning, `${cls.name}:${propName}`);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n\n/**\n * Mark object property as deprecated.\n *\n * A console WARNING will be displayed when object property is being accessed.\n *\n * 1. Deprecate object property\n * const obj = { something: 'aloha' };\n *\n * deprecatedObjectProperty(obj, 'something', 'Use `somethingElse` instead.');\n */\nexport const deprecatedObjectProperty = (\n  obj: Record<string, any>,\n  propName: string,\n  warning: string,\n  key?: string,\n): void => {\n  let value = obj[propName];\n  Object.defineProperty(obj, propName, {\n    get() {\n      deprecated(propName, warning, key);\n      return value;\n    },\n    set(v: unknown) {\n      value = v;\n    },\n  });\n};\n"], "mappings": ";AAAO,IAAMA,wBAAA,GAA2BA,CAAA,KAAe;EACrD,IAAI;IACF,OAAOC,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa;EAElC,SAASC,GAAA,EAAK,CAAC;EAIf,OAAO;AACT;AAEO,IAAMC,iBAAA,GAAoBA,CAAA,KAAe;EAC9C,IAAI;IACF,OAAOJ,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa;EAElC,SAASC,GAAA,EAAK,CAAC;EAGf,OAAO;AACT;AAEO,IAAME,uBAAA,GAA0BA,CAAA,KAAe;EACpD,IAAI;IACF,OAAOL,OAAA,CAAQC,GAAA,CAAIC,QAAA,KAAa;EAElC,SAASC,GAAA,EAAK,CAAC;EAGf,OAAO;AACT;;;ACRA,IAAMG,iBAAA,GAAoB,mBAAIC,GAAA,CAAY;AACnC,IAAMC,UAAA,GAAaA,CAACC,MAAA,EAAgBC,OAAA,EAAiBC,GAAA,KAAuB;EACjF,MAAMC,WAAA,GAAcR,iBAAA,CAAkB,KAAKC,uBAAA,CAAwB;EACnE,MAAMQ,SAAA,GAAYF,GAAA,WAAAA,GAAA,GAAOF,MAAA;EACzB,IAAIH,iBAAA,CAAkBQ,GAAA,CAAID,SAAS,KAAKD,WAAA,EAAa;IACnD;EACF;EACAN,iBAAA,CAAkBS,GAAA,CAAIF,SAAS;EAE/BG,OAAA,CAAQC,IAAA,CACN,iCAAiCR,MAAM;AAAA,EAAmEC,OAAO,EACnH;AACF;AAyBO,IAAMQ,kBAAA,GAAqBA,CAACC,GAAA,EAAeC,QAAA,EAAkBV,OAAA,EAAiBW,QAAA,GAAW,UAAgB;EAC9G,MAAMC,MAAA,GAASD,QAAA,GAAWF,GAAA,GAAMA,GAAA,CAAII,SAAA;EAEpC,IAAIC,KAAA,GAAQF,MAAA,CAAOF,QAAQ;EAC3BK,MAAA,CAAOC,cAAA,CAAeJ,MAAA,EAAQF,QAAA,EAAU;IACtCO,IAAA,EAAM;MACJnB,UAAA,CAAWY,QAAA,EAAUV,OAAA,EAAS,GAAGS,GAAA,CAAIS,IAAI,IAAIR,QAAQ,EAAE;MACvD,OAAOI,KAAA;IACT;IACAK,IAAIC,CAAA,EAAY;MACdN,KAAA,GAAQM,CAAA;IACV;EACF,CAAC;AACH;AAYO,IAAMC,wBAAA,GAA2BA,CACtCC,GAAA,EACAZ,QAAA,EACAV,OAAA,EACAC,GAAA,KACS;EACT,IAAIa,KAAA,GAAQQ,GAAA,CAAIZ,QAAQ;EACxBK,MAAA,CAAOC,cAAA,CAAeM,GAAA,EAAKZ,QAAA,EAAU;IACnCO,IAAA,EAAM;MACJnB,UAAA,CAAWY,QAAA,EAAUV,OAAA,EAASC,GAAG;MACjC,OAAOa,KAAA;IACT;IACAK,IAAIC,CAAA,EAAY;MACdN,KAAA,GAAQM,CAAA;IACV;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}