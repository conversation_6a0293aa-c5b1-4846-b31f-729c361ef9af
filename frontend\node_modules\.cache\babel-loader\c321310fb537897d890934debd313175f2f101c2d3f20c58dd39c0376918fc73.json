{"ast": null, "code": "// src/loadScript.ts\nvar NO_DOCUMENT_ERROR = \"loadScript cannot be called when document does not exist\";\nvar NO_SRC_ERROR = \"loadScript cannot be called without a src\";\nasync function loadScript(src = \"\", opts) {\n  const {\n    async,\n    defer,\n    beforeLoad,\n    crossOrigin\n  } = opts || {};\n  return new Promise((resolve, reject) => {\n    if (!src) {\n      reject(NO_SRC_ERROR);\n    }\n    if (!document || !document.body) {\n      reject(NO_DOCUMENT_ERROR);\n    }\n    const script = document.createElement(\"script\");\n    crossOrigin && script.setAttribute(\"crossorigin\", crossOrigin);\n    script.async = async || false;\n    script.defer = defer || false;\n    script.addEventListener(\"load\", () => {\n      script.remove();\n      resolve(script);\n    });\n    script.addEventListener(\"error\", () => {\n      script.remove();\n      reject();\n    });\n    script.src = src;\n    beforeLoad == null ? void 0 : beforeLoad(script);\n    document.body.appendChild(script);\n  });\n}\nexport { loadScript };", "map": {"version": 3, "names": ["NO_DOCUMENT_ERROR", "NO_SRC_ERROR", "loadScript", "src", "opts", "async", "defer", "beforeLoad", "crossOrigin", "Promise", "resolve", "reject", "document", "body", "script", "createElement", "setAttribute", "addEventListener", "remove", "append<PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\loadScript.ts"], "sourcesContent": ["const NO_DOCUMENT_ERROR = 'loadScript cannot be called when document does not exist';\nconst NO_SRC_ERROR = 'loadScript cannot be called without a src';\n\ntype LoadScriptOptions = {\n  async?: boolean;\n  defer?: boolean;\n  crossOrigin?: 'anonymous' | 'use-credentials';\n  beforeLoad?: (script: HTMLScriptElement) => void;\n};\n\nexport async function loadScript(src = '', opts: LoadScriptOptions): Promise<HTMLScriptElement> {\n  const { async, defer, beforeLoad, crossOrigin } = opts || {};\n  return new Promise((resolve, reject) => {\n    if (!src) {\n      reject(NO_SRC_ERROR);\n    }\n\n    if (!document || !document.body) {\n      reject(NO_DOCUMENT_ERROR);\n    }\n\n    const script = document.createElement('script');\n\n    crossOrigin && script.setAttribute('crossorigin', crossOrigin);\n    script.async = async || false;\n    script.defer = defer || false;\n\n    script.addEventListener('load', () => {\n      script.remove();\n      resolve(script);\n    });\n\n    script.addEventListener('error', () => {\n      script.remove();\n      reject();\n    });\n\n    script.src = src;\n    beforeLoad?.(script);\n    document.body.appendChild(script);\n  });\n}\n"], "mappings": ";AAAA,IAAMA,iBAAA,GAAoB;AAC1B,IAAMC,YAAA,GAAe;AASrB,eAAsBC,WAAWC,GAAA,GAAM,IAAIC,IAAA,EAAqD;EAC9F,MAAM;IAAEC,KAAA;IAAOC,KAAA;IAAOC,UAAA;IAAYC;EAAY,IAAIJ,IAAA,IAAQ,CAAC;EAC3D,OAAO,IAAIK,OAAA,CAAQ,CAACC,OAAA,EAASC,MAAA,KAAW;IACtC,IAAI,CAACR,GAAA,EAAK;MACRQ,MAAA,CAAOV,YAAY;IACrB;IAEA,IAAI,CAACW,QAAA,IAAY,CAACA,QAAA,CAASC,IAAA,EAAM;MAC/BF,MAAA,CAAOX,iBAAiB;IAC1B;IAEA,MAAMc,MAAA,GAASF,QAAA,CAASG,aAAA,CAAc,QAAQ;IAE9CP,WAAA,IAAeM,MAAA,CAAOE,YAAA,CAAa,eAAeR,WAAW;IAC7DM,MAAA,CAAOT,KAAA,GAAQA,KAAA,IAAS;IACxBS,MAAA,CAAOR,KAAA,GAAQA,KAAA,IAAS;IAExBQ,MAAA,CAAOG,gBAAA,CAAiB,QAAQ,MAAM;MACpCH,MAAA,CAAOI,MAAA,CAAO;MACdR,OAAA,CAAQI,MAAM;IAChB,CAAC;IAEDA,MAAA,CAAOG,gBAAA,CAAiB,SAAS,MAAM;MACrCH,MAAA,CAAOI,MAAA,CAAO;MACdP,MAAA,CAAO;IACT,CAAC;IAEDG,MAAA,CAAOX,GAAA,GAAMA,GAAA;IACbI,UAAA,oBAAAA,UAAA,CAAaO,MAAA;IACbF,QAAA,CAASC,IAAA,CAAKM,WAAA,CAAYL,MAAM;EAClC,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}