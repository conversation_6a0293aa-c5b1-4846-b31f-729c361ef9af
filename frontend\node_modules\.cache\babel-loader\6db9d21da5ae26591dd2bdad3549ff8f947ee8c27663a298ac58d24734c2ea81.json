{"ast": null, "code": "import \"../chunk-XTU7I5IS.js\";\nimport { createContextAndHook } from \"@clerk/shared/react\";\nconst [AuthContext, useAuthContext] = createContextAndHook(\"AuthContext\");\nexport { AuthContext, useAuthContext };", "map": {"version": 3, "names": ["createContextAndHook", "AuthContext", "useAuthContext"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\clerk-react\\src\\contexts\\AuthContext.ts"], "sourcesContent": ["import { createContextAndHook } from '@clerk/shared/react';\nimport type { ActJWTClaim, MembershipRole, OrganizationCustomPermissionKey } from '@clerk/types';\n\nexport const [AuthContext, useAuthContext] = createContextAndHook<{\n  userId: string | null | undefined;\n  sessionId: string | null | undefined;\n  actor: ActJWTClaim | null | undefined;\n  orgId: string | null | undefined;\n  orgRole: MembershipRole | null | undefined;\n  orgSlug: string | null | undefined;\n  orgPermissions: OrganizationCustomPermissionKey[] | null | undefined;\n}>('AuthContext');\n"], "mappings": ";AAAA,SAASA,oBAAA,QAA4B;AAG9B,MAAM,CAACC,WAAA,EAAaC,cAAc,IAAIF,oBAAA,CAQ1C,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}