{"ast": null, "code": "import { deprecated, deprecatedObjectProperty } from \"../chunk-IC4FGZI3.mjs\";\nimport { __export, __reExport } from \"../chunk-NDCDZYN6.mjs\";\n\n// src/react/hooks/createContextAndHook.ts\nimport React from \"react\";\nfunction assertContextExists(contextVal, msgOrCtx) {\n  if (!contextVal) {\n    throw typeof msgOrCtx === \"string\" ? new Error(msgOrCtx) : new Error(`${msgOrCtx.displayName} not found`);\n  }\n}\nvar createContextAndHook = (displayName, options) => {\n  const {\n    assertCtxFn = assertContextExists\n  } = options || {};\n  const Ctx = React.createContext(void 0);\n  Ctx.displayName = displayName;\n  const useCtx = () => {\n    const ctx = React.useContext(Ctx);\n    assertCtxFn(ctx, `${displayName} not found`);\n    return ctx.value;\n  };\n  const useCtxWithoutGuarantee = () => {\n    const ctx = React.useContext(Ctx);\n    return ctx ? ctx.value : {};\n  };\n  return [Ctx, useCtx, useCtxWithoutGuarantee];\n};\n\n// src/react/clerk-swr.ts\nvar clerk_swr_exports = {};\n__export(clerk_swr_exports, {\n  SWRConfig: () => SWRConfig,\n  useSWR: () => default2,\n  useSWRInfinite: () => default3\n});\n__reExport(clerk_swr_exports, swr_star);\nimport * as swr_star from \"swr\";\nimport { default as default2, SWRConfig } from \"swr\";\nimport { default as default3 } from \"swr/infinite\";\n\n// src/react/contexts.tsx\nimport React2 from \"react\";\nvar [ClerkInstanceContext, useClerkInstanceContext] = createContextAndHook(\"ClerkInstanceContext\");\nvar [UserContext, useUserContext] = createContextAndHook(\"UserContext\");\nvar [ClientContext, useClientContext] = createContextAndHook(\"ClientContext\");\nvar [SessionContext, useSessionContext] = createContextAndHook(\"SessionContext\");\nvar [OrganizationContextInternal, useOrganizationContext] = createContextAndHook(\"OrganizationContext\");\nvar OrganizationProvider = ({\n  children,\n  organization,\n  lastOrganizationMember,\n  lastOrganizationInvitation,\n  swrConfig\n}) => {\n  return /* @__PURE__ */React2.createElement(SWRConfig, {\n    value: swrConfig\n  }, /* @__PURE__ */React2.createElement(OrganizationContextInternal.Provider, {\n    value: {\n      value: {\n        organization,\n        lastOrganizationMember,\n        lastOrganizationInvitation\n      }\n    }\n  }, children));\n};\nvar OrganizationContext = (...args) => {\n  deprecated(\"OrganizationContext\", \"Use `OrganizationProvider` instead\");\n  return OrganizationProvider(...args);\n};\n\n// src/react/hooks/usePagesOrInfinite.ts\nimport { useCallback, useMemo, useRef, useState } from \"react\";\nfunction getDifferentKeys(obj1, obj2) {\n  const keysSet = new Set(Object.keys(obj2));\n  const differentKeysObject = {};\n  for (const key1 of Object.keys(obj1)) {\n    if (!keysSet.has(key1)) {\n      differentKeysObject[key1] = obj1[key1];\n    }\n  }\n  return differentKeysObject;\n}\nvar useWithSafeValues = (params, defaultValues) => {\n  var _a, _b, _c;\n  const shouldUseDefaults = typeof params === \"boolean\" && params;\n  const initialPageRef = useRef(shouldUseDefaults ? defaultValues.initialPage : (_a = params == null ? void 0 : params.initialPage) != null ? _a : defaultValues.initialPage);\n  const pageSizeRef = useRef(shouldUseDefaults ? defaultValues.pageSize : (_b = params == null ? void 0 : params.pageSize) != null ? _b : defaultValues.pageSize);\n  const newObj = {};\n  for (const key of Object.keys(defaultValues)) {\n    newObj[key] = shouldUseDefaults ? defaultValues[key] : (_c = params == null ? void 0 : params[key]) != null ? _c : defaultValues[key];\n  }\n  return {\n    ...newObj,\n    initialPage: initialPageRef.current,\n    pageSize: pageSizeRef.current\n  };\n};\nvar usePagesOrInfinite = (params, fetcher, options, cacheKeys) => {\n  var _a, _b, _c, _d, _e, _f;\n  const [paginatedPage, setPaginatedPage] = useState((_a = params.initialPage) != null ? _a : 1);\n  const initialPageRef = useRef((_b = params.initialPage) != null ? _b : 1);\n  const pageSizeRef = useRef((_c = params.pageSize) != null ? _c : 10);\n  const enabled = (_d = options.enabled) != null ? _d : true;\n  const triggerInfinite = (_e = options.infinite) != null ? _e : false;\n  const keepPreviousData = (_f = options.keepPreviousData) != null ? _f : false;\n  const pagesCacheKey = {\n    ...cacheKeys,\n    ...params,\n    initialPage: paginatedPage,\n    pageSize: pageSizeRef.current\n  };\n  const {\n    data: swrData,\n    isValidating: swrIsValidating,\n    isLoading: swrIsLoading,\n    error: swrError,\n    mutate: swrMutate\n  } = default2(!triggerInfinite && !!fetcher && enabled ? pagesCacheKey : null, cacheKeyParams => {\n    const requestParams = getDifferentKeys(cacheKeyParams, cacheKeys);\n    return fetcher == null ? void 0 : fetcher(requestParams);\n  }, {\n    keepPreviousData\n  });\n  const {\n    data: swrInfiniteData,\n    isLoading: swrInfiniteIsLoading,\n    isValidating: swrInfiniteIsValidating,\n    error: swrInfiniteError,\n    size,\n    setSize,\n    mutate: swrInfiniteMutate\n  } = default3(pageIndex => {\n    if (!triggerInfinite || !enabled) {\n      return null;\n    }\n    return {\n      ...params,\n      ...cacheKeys,\n      initialPage: initialPageRef.current + pageIndex,\n      pageSize: pageSizeRef.current\n    };\n  }, cacheKeyParams => {\n    const requestParams = getDifferentKeys(cacheKeyParams, cacheKeys);\n    return fetcher == null ? void 0 : fetcher(requestParams);\n  });\n  const page = useMemo(() => {\n    if (triggerInfinite) {\n      return size;\n    }\n    return paginatedPage;\n  }, [triggerInfinite, size, paginatedPage]);\n  const fetchPage = useCallback(numberOrgFn => {\n    if (triggerInfinite) {\n      void setSize(numberOrgFn);\n      return;\n    }\n    return setPaginatedPage(numberOrgFn);\n  }, [setSize]);\n  const data = useMemo(() => {\n    var _a2, _b2;\n    if (triggerInfinite) {\n      return (_a2 = swrInfiniteData == null ? void 0 : swrInfiniteData.map(a => a == null ? void 0 : a.data).flat()) != null ? _a2 : [];\n    }\n    return (_b2 = swrData == null ? void 0 : swrData.data) != null ? _b2 : [];\n  }, [triggerInfinite, swrData, swrInfiniteData]);\n  const count = useMemo(() => {\n    var _a2, _b2;\n    if (triggerInfinite) {\n      return ((_a2 = swrInfiniteData == null ? void 0 : swrInfiniteData[(swrInfiniteData == null ? void 0 : swrInfiniteData.length) - 1]) == null ? void 0 : _a2.total_count) || 0;\n    }\n    return (_b2 = swrData == null ? void 0 : swrData.total_count) != null ? _b2 : 0;\n  }, [triggerInfinite, swrData, swrInfiniteData]);\n  const isLoading = triggerInfinite ? swrInfiniteIsLoading : swrIsLoading;\n  const isFetching = triggerInfinite ? swrInfiniteIsValidating : swrIsValidating;\n  const isError = !!(triggerInfinite ? swrInfiniteError : swrError);\n  const fetchNext = useCallback(() => {\n    fetchPage(n => Math.max(0, n + 1));\n  }, [fetchPage]);\n  const fetchPrevious = useCallback(() => {\n    fetchPage(n => Math.max(0, n - 1));\n  }, [fetchPage]);\n  const offsetCount = (initialPageRef.current - 1) * pageSizeRef.current;\n  const pageCount = Math.ceil((count - offsetCount) / pageSizeRef.current);\n  const hasNextPage = count - offsetCount * pageSizeRef.current > page * pageSizeRef.current;\n  const hasPreviousPage = (page - 1) * pageSizeRef.current > offsetCount * pageSizeRef.current;\n  const setData = triggerInfinite ? value => swrInfiniteMutate(value, {\n    revalidate: false\n  }) : value => swrMutate(value, {\n    revalidate: false\n  });\n  const revalidate = triggerInfinite ? () => swrInfiniteMutate() : () => swrMutate();\n  return {\n    data,\n    count,\n    isLoading,\n    isFetching,\n    isError,\n    page,\n    pageCount,\n    fetchPage,\n    fetchNext,\n    fetchPrevious,\n    hasNextPage,\n    hasPreviousPage,\n    // Let the hook return type define this type\n    revalidate,\n    // Let the hook return type define this type\n    setData\n  };\n};\n\n// src/react/hooks/useOrganization.tsx\nvar undefinedPaginatedResource = {\n  data: void 0,\n  count: void 0,\n  isLoading: false,\n  isFetching: false,\n  isError: false,\n  page: void 0,\n  pageCount: void 0,\n  fetchPage: void 0,\n  fetchNext: void 0,\n  fetchPrevious: void 0,\n  hasNextPage: false,\n  hasPreviousPage: false,\n  revalidate: void 0,\n  setData: void 0\n};\nvar useOrganization = params => {\n  const {\n    invitationList: invitationListParams,\n    membershipList: membershipListParams,\n    domains: domainListParams,\n    membershipRequests: membershipRequestsListParams,\n    memberships: membersListParams,\n    invitations: invitationsListParams\n  } = params || {};\n  const {\n    organization,\n    lastOrganizationMember,\n    lastOrganizationInvitation\n  } = useOrganizationContext();\n  const session = useSessionContext();\n  const domainSafeValues = useWithSafeValues(domainListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    keepPreviousData: false,\n    infinite: false,\n    enrollmentMode: void 0\n  });\n  const membershipRequestSafeValues = useWithSafeValues(membershipRequestsListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    status: \"pending\",\n    keepPreviousData: false,\n    infinite: false\n  });\n  const membersSafeValues = useWithSafeValues(membersListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    role: void 0,\n    keepPreviousData: false,\n    infinite: false\n  });\n  const invitationsSafeValues = useWithSafeValues(invitationsListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    status: [\"pending\"],\n    keepPreviousData: false,\n    infinite: false\n  });\n  const clerk = useClerkInstanceContext();\n  const shouldFetch = !!(clerk.loaded && session && organization);\n  const domainParams = typeof domainListParams === \"undefined\" ? void 0 : {\n    initialPage: domainSafeValues.initialPage,\n    pageSize: domainSafeValues.pageSize,\n    enrollmentMode: domainSafeValues.enrollmentMode\n  };\n  const membershipRequestParams = typeof membershipRequestsListParams === \"undefined\" ? void 0 : {\n    initialPage: membershipRequestSafeValues.initialPage,\n    pageSize: membershipRequestSafeValues.pageSize,\n    status: membershipRequestSafeValues.status\n  };\n  const membersParams = typeof membersListParams === \"undefined\" ? void 0 : {\n    initialPage: membersSafeValues.initialPage,\n    pageSize: membersSafeValues.pageSize,\n    role: membersSafeValues.role\n  };\n  const invitationsParams = typeof invitationsListParams === \"undefined\" ? void 0 : {\n    initialPage: invitationsSafeValues.initialPage,\n    pageSize: invitationsSafeValues.pageSize,\n    status: invitationsSafeValues.status\n  };\n  const domains = usePagesOrInfinite({\n    ...domainParams\n  }, organization == null ? void 0 : organization.getDomains, {\n    keepPreviousData: domainSafeValues.keepPreviousData,\n    infinite: domainSafeValues.infinite,\n    enabled: !!domainParams\n  }, {\n    type: \"domains\",\n    organizationId: organization == null ? void 0 : organization.id\n  });\n  const membershipRequests = usePagesOrInfinite({\n    ...membershipRequestParams\n  }, organization == null ? void 0 : organization.getMembershipRequests, {\n    keepPreviousData: membershipRequestSafeValues.keepPreviousData,\n    infinite: membershipRequestSafeValues.infinite,\n    enabled: !!membershipRequestParams\n  }, {\n    type: \"membershipRequests\",\n    organizationId: organization == null ? void 0 : organization.id\n  });\n  const memberships = usePagesOrInfinite({\n    ...membersParams,\n    paginated: true\n  }, organization == null ? void 0 : organization.getMemberships, {\n    keepPreviousData: membersSafeValues.keepPreviousData,\n    infinite: membersSafeValues.infinite,\n    enabled: !!membersParams\n  }, {\n    type: \"members\",\n    organizationId: organization == null ? void 0 : organization.id\n  });\n  const invitations = usePagesOrInfinite({\n    ...invitationsParams\n  }, organization == null ? void 0 : organization.getInvitations, {\n    keepPreviousData: invitationsSafeValues.keepPreviousData,\n    infinite: invitationsSafeValues.infinite,\n    enabled: !!invitationsParams\n  }, {\n    type: \"invitations\",\n    organizationId: organization == null ? void 0 : organization.id\n  });\n  const pendingInvitations = !clerk.loaded ? () => [] : () => {\n    var _a;\n    return (_a = clerk.organization) == null ? void 0 : _a.getPendingInvitations(invitationListParams);\n  };\n  const currentOrganizationMemberships = !clerk.loaded ? () => [] : () => {\n    var _a;\n    return (_a = clerk.organization) == null ? void 0 : _a.getMemberships(membershipListParams);\n  };\n  if (invitationListParams) {\n    deprecated(\"invitationList in useOrganization\", \"Use the `invitations` property and return value instead.\");\n  }\n  const {\n    data: invitationList,\n    isValidating: isInvitationsLoading,\n    mutate: mutateInvitationList\n  } = default2(shouldFetch && invitationListParams ? cacheKey(\"invites\", organization, lastOrganizationInvitation, invitationListParams) : null, pendingInvitations);\n  if (membershipListParams) {\n    deprecated(\"membershipList in useOrganization\", \"Use the `memberships` property and return value instead.\");\n  }\n  const {\n    data: membershipList,\n    isValidating: isMembershipsLoading,\n    mutate: mutateMembershipList\n  } = default2(shouldFetch && membershipListParams ? cacheKey(\"memberships\", organization, lastOrganizationMember, membershipListParams) : null, currentOrganizationMemberships);\n  if (organization === void 0) {\n    return {\n      isLoaded: false,\n      organization: void 0,\n      invitationList: void 0,\n      membershipList: void 0,\n      membership: void 0,\n      domains: undefinedPaginatedResource,\n      membershipRequests: undefinedPaginatedResource,\n      memberships: undefinedPaginatedResource,\n      invitations: undefinedPaginatedResource\n    };\n  }\n  if (organization === null) {\n    return {\n      isLoaded: true,\n      organization: null,\n      invitationList: null,\n      membershipList: null,\n      membership: null,\n      domains: null,\n      membershipRequests: null,\n      memberships: null,\n      invitations: null\n    };\n  }\n  if (!clerk.loaded && organization) {\n    return {\n      isLoaded: true,\n      organization,\n      invitationList: void 0,\n      membershipList: void 0,\n      membership: void 0,\n      domains: undefinedPaginatedResource,\n      membershipRequests: undefinedPaginatedResource,\n      memberships: undefinedPaginatedResource,\n      invitations: undefinedPaginatedResource\n    };\n  }\n  return {\n    isLoaded: !isMembershipsLoading && !isInvitationsLoading,\n    organization,\n    membershipList,\n    membership: getCurrentOrganizationMembership(session.user.organizationMemberships, organization.id),\n    // your membership in the current org\n    invitationList,\n    unstable__mutate: () => {\n      void mutateMembershipList();\n      void mutateInvitationList();\n    },\n    domains,\n    membershipRequests,\n    memberships,\n    invitations\n  };\n};\nfunction getCurrentOrganizationMembership(organizationMemberships, activeOrganizationId) {\n  return organizationMemberships.find(organizationMembership => organizationMembership.organization.id === activeOrganizationId);\n}\nfunction cacheKey(type, organization, resource, pagination) {\n  return [type, organization.id, resource == null ? void 0 : resource.id, resource == null ? void 0 : resource.updatedAt, pagination.offset, pagination.limit].filter(Boolean).join(\"-\");\n}\n\n// src/react/hooks/useOrganizationList.tsx\nvar undefinedPaginatedResource2 = {\n  data: void 0,\n  count: void 0,\n  isLoading: false,\n  isFetching: false,\n  isError: false,\n  page: void 0,\n  pageCount: void 0,\n  fetchPage: void 0,\n  fetchNext: void 0,\n  fetchPrevious: void 0,\n  hasNextPage: false,\n  hasPreviousPage: false,\n  revalidate: void 0,\n  setData: void 0\n};\nvar useOrganizationList = params => {\n  const {\n    userMemberships,\n    userInvitations,\n    userSuggestions\n  } = params || {};\n  const userMembershipsSafeValues = useWithSafeValues(userMemberships, {\n    initialPage: 1,\n    pageSize: 10,\n    keepPreviousData: false,\n    infinite: false\n  });\n  const userInvitationsSafeValues = useWithSafeValues(userInvitations, {\n    initialPage: 1,\n    pageSize: 10,\n    status: \"pending\",\n    keepPreviousData: false,\n    infinite: false\n  });\n  const userSuggestionsSafeValues = useWithSafeValues(userSuggestions, {\n    initialPage: 1,\n    pageSize: 10,\n    status: \"pending\",\n    keepPreviousData: false,\n    infinite: false\n  });\n  const clerk = useClerkInstanceContext();\n  const user = useUserContext();\n  const userMembershipsParams = typeof userMemberships === \"undefined\" ? void 0 : {\n    initialPage: userMembershipsSafeValues.initialPage,\n    pageSize: userMembershipsSafeValues.pageSize\n  };\n  const userInvitationsParams = typeof userInvitations === \"undefined\" ? void 0 : {\n    initialPage: userInvitationsSafeValues.initialPage,\n    pageSize: userInvitationsSafeValues.pageSize,\n    status: userInvitationsSafeValues.status\n  };\n  const userSuggestionsParams = typeof userSuggestions === \"undefined\" ? void 0 : {\n    initialPage: userSuggestionsSafeValues.initialPage,\n    pageSize: userSuggestionsSafeValues.pageSize,\n    status: userSuggestionsSafeValues.status\n  };\n  const isClerkLoaded = !!(clerk.loaded && user);\n  const memberships = usePagesOrInfinite({\n    ...userMembershipsParams,\n    paginated: true\n  }, user == null ? void 0 : user.getOrganizationMemberships, {\n    keepPreviousData: userMembershipsSafeValues.keepPreviousData,\n    infinite: userMembershipsSafeValues.infinite,\n    enabled: !!userMembershipsParams\n  }, {\n    type: \"userMemberships\",\n    userId: user == null ? void 0 : user.id\n  });\n  const invitations = usePagesOrInfinite({\n    ...userInvitationsParams\n  }, user == null ? void 0 : user.getOrganizationInvitations, {\n    keepPreviousData: userInvitationsSafeValues.keepPreviousData,\n    infinite: userInvitationsSafeValues.infinite,\n    enabled: !!userInvitationsParams\n  }, {\n    type: \"userInvitations\",\n    userId: user == null ? void 0 : user.id\n  });\n  const suggestions = usePagesOrInfinite({\n    ...userSuggestionsParams\n  }, user == null ? void 0 : user.getOrganizationSuggestions, {\n    keepPreviousData: userSuggestionsSafeValues.keepPreviousData,\n    infinite: userSuggestionsSafeValues.infinite,\n    enabled: !!userSuggestionsParams\n  }, {\n    type: \"userSuggestions\",\n    userId: user == null ? void 0 : user.id\n  });\n  if (!isClerkLoaded) {\n    return {\n      isLoaded: false,\n      organizationList: void 0,\n      createOrganization: void 0,\n      setActive: void 0,\n      userMemberships: undefinedPaginatedResource2,\n      userInvitations: undefinedPaginatedResource2,\n      userSuggestions: undefinedPaginatedResource2\n    };\n  }\n  const result = {\n    isLoaded: isClerkLoaded,\n    organizationList: createOrganizationList(user.organizationMemberships),\n    setActive: clerk.setActive,\n    createOrganization: clerk.createOrganization,\n    userMemberships: memberships,\n    userInvitations: invitations,\n    userSuggestions: suggestions\n  };\n  deprecatedObjectProperty(result, \"organizationList\", \"Use `userMemberships` instead.\");\n  return result;\n};\nfunction createOrganizationList(organizationMemberships) {\n  return organizationMemberships.map(organizationMembership => ({\n    membership: organizationMembership,\n    organization: organizationMembership.organization\n  }));\n}\n\n// src/react/hooks/useOrganizations.tsx\nvar useOrganizations = () => {\n  deprecated(\"useOrganizations\", \"Use useOrganizationList, useOrganization, or useClerk instead.\");\n  const clerk = useClerkInstanceContext();\n  if (!clerk.loaded) {\n    return {\n      isLoaded: false,\n      createOrganization: void 0,\n      getOrganizationMemberships: void 0,\n      getOrganization: void 0\n    };\n  }\n  return {\n    isLoaded: true,\n    createOrganization: clerk.createOrganization,\n    getOrganizationMemberships: clerk.getOrganizationMemberships,\n    getOrganization: clerk.getOrganization\n  };\n};\n\n// src/react/hooks/useSafeLayoutEffect.tsx\nimport React3 from \"react\";\nvar useSafeLayoutEffect = typeof window !== \"undefined\" ? React3.useLayoutEffect : React3.useEffect;\nexport { ClerkInstanceContext, ClientContext, OrganizationContext, OrganizationProvider, SessionContext, UserContext, assertContextExists, createContextAndHook, useClerkInstanceContext, useClientContext, useOrganization, useOrganizationContext, useOrganizationList, useOrganizations, useSafeLayoutEffect, useSessionContext, useUserContext };", "map": {"version": 3, "names": ["React", "assertContextExists", "contextVal", "msgOrCtx", "Error", "displayName", "createContextAndHook", "options", "assertCtxFn", "Ctx", "createContext", "useCtx", "ctx", "useContext", "value", "useCtxWithoutGuarantee", "clerk_swr_exports", "__export", "SWRConfig", "useSWR", "default2", "useSWRInfinite", "default3", "__reExport", "swr_star", "default", "React2", "ClerkInstanceContext", "useClerkInstanceContext", "UserContext", "useUserContext", "ClientContext", "useClientContext", "SessionContext", "useSessionContext", "OrganizationContextInternal", "useOrganizationContext", "OrganizationProvider", "children", "organization", "lastOrganizationMember", "lastOrganizationInvitation", "swrConfig", "createElement", "Provider", "OrganizationContext", "args", "deprecated", "useCallback", "useMemo", "useRef", "useState", "getDifferent<PERSON><PERSON><PERSON>", "obj1", "obj2", "keysSet", "Set", "Object", "keys", "differentKeysObject", "key1", "has", "useWithSafeValues", "params", "defaultValues", "_a", "_b", "_c", "shouldUseDefaults", "initialPageRef", "initialPage", "pageSizeRef", "pageSize", "newObj", "key", "current", "usePagesOrInfinite", "fetcher", "cacheKeys", "_d", "_e", "_f", "paginatedPage", "setPaginatedPage", "enabled", "triggerInfinite", "infinite", "keepPreviousData", "pagesCache<PERSON>ey", "data", "swrData", "isValidating", "swrIsValidating", "isLoading", "swrIsLoading", "error", "swrError", "mutate", "swrMutate", "cacheKeyParams", "requestParams", "swrInfiniteData", "swrInfiniteIsLoading", "swrInfiniteIsValidating", "swrInfiniteError", "size", "setSize", "swrInfiniteMutate", "pageIndex", "page", "fetchPage", "numberOrgFn", "_a2", "_b2", "map", "a", "flat", "count", "length", "total_count", "isFetching", "isError", "fetchNext", "n", "Math", "max", "fetchPrevious", "offsetCount", "pageCount", "ceil", "hasNextPage", "hasPreviousPage", "setData", "revalidate", "undefinedPaginatedResource", "useOrganization", "invitationList", "invitationListParams", "membershipList", "membershipListParams", "domains", "domainListParams", "membershipRequests", "membershipRequestsListParams", "memberships", "membersListParams", "invitations", "invitationsListParams", "session", "domainSafeValues", "enrollmentMode", "membershipRequestSafeValues", "status", "membersSafeValues", "role", "invitationsSafe<PERSON><PERSON><PERSON>", "clerk", "shouldFetch", "loaded", "domainParams", "membershipRequestParams", "membersParams", "invitationsP<PERSON><PERSON>", "getDomains", "type", "organizationId", "id", "getMembershipRequests", "paginated", "getMemberships", "getInvitations", "pendingInvitations", "getPendingInvitations", "currentOrganizationMemberships", "isInvitationsLoading", "mutateInvitationList", "cache<PERSON>ey", "isMembershipsLoading", "mutateMembershipList", "isLoaded", "membership", "getCurrentOrganizationMembership", "user", "organizationMemberships", "unstable__mutate", "activeOrganizationId", "find", "organizationMembership", "resource", "pagination", "updatedAt", "offset", "limit", "filter", "Boolean", "join", "undefinedPaginatedResource2", "useOrganizationList", "userMemberships", "userInvitations", "userSuggestions", "userMembershipsSafeValues", "userInvitationsSafeValues", "userSuggestionsSafeValues", "userMembershipsParams", "userInvitationsParams", "userSuggestionsParams", "isClerkLoaded", "getOrganizationMemberships", "userId", "getOrganizationInvitations", "suggestions", "getOrganizationSuggestions", "organizationList", "createOrganization", "setActive", "result", "createOrganizationList", "deprecatedObjectProperty", "useOrganizations", "getOrganization", "React3", "useSafeLayoutEffect", "window", "useLayoutEffect", "useEffect"], "sources": ["C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\react\\hooks\\createContextAndHook.ts", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\react\\clerk-swr.ts", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\react\\contexts.tsx", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\react\\hooks\\usePagesOrInfinite.ts", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\react\\hooks\\useOrganization.tsx", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\react\\hooks\\useOrganizationList.tsx", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\react\\hooks\\useOrganizations.tsx", "C:\\Users\\<USER>\\Desktop\\file\\u3summer\\artech\\artech\\frontend\\node_modules\\@clerk\\shared\\src\\react\\hooks\\useSafeLayoutEffect.tsx"], "sourcesContent": ["'use client';\nimport React from 'react';\n\nexport function assertContextExists(contextVal: unknown, msgOrCtx: string | React.Context<any>): asserts contextVal {\n  if (!contextVal) {\n    throw typeof msgOrCtx === 'string' ? new Error(msgOrCtx) : new Error(`${msgOrCtx.displayName} not found`);\n  }\n}\n\ntype Options = { assertCtxFn?: (v: unknown, msg: string) => void };\ntype ContextOf<T> = React.Context<{ value: T } | undefined>;\ntype UseCtxFn<T> = () => T;\n\n/**\n * Creates and returns a Context and two hooks that return the context value.\n * The Context type is derived from the type passed in by the user.\n * The first hook returned guarantees that the context exists so the returned value is always CtxValue\n * The second hook makes no guarantees, so the returned value can be CtxValue | undefined\n */\nexport const createContextAndHook = <CtxVal>(\n  displayName: string,\n  options?: Options,\n): [ContextOf<CtxVal>, UseCtxFn<CtxVal>, UseCtxFn<CtxVal | Partial<CtxVal>>] => {\n  const { assertCtxFn = assertContextExists } = options || {};\n  const Ctx = React.createContext<{ value: CtxVal } | undefined>(undefined);\n  Ctx.displayName = displayName;\n\n  const useCtx = () => {\n    const ctx = React.useContext(Ctx);\n    assertCtxFn(ctx, `${displayName} not found`);\n    return (ctx as any).value as CtxVal;\n  };\n\n  const useCtxWithoutGuarantee = () => {\n    const ctx = React.useContext(Ctx);\n    return ctx ? ctx.value : {};\n  };\n\n  return [Ctx, useCtx, useCtxWithoutGuarantee];\n};\n", "'use client';\nexport * from 'swr';\nexport { default as useSWR, SWRConfig } from 'swr';\nexport { default as useSWRInfinite } from 'swr/infinite';\n", "'use client';\n\nimport type {\n  ActiveSessionResource,\n  ClientResource,\n  LoadedClerk,\n  OrganizationInvitationResource,\n  OrganizationMembershipResource,\n  OrganizationResource,\n  UserResource,\n} from '@clerk/types';\nimport type { PropsWithChildren } from 'react';\nimport React from 'react';\n\nimport { deprecated } from '../deprecated';\nimport { SWRConfig } from './clerk-swr';\nimport { createContextAndHook } from './hooks/createContextAndHook';\n\nconst [ClerkInstanceContext, useClerkInstanceContext] = createContextAndHook<LoadedClerk>('ClerkInstanceContext');\nconst [UserContext, useUserContext] = createContextAndHook<UserResource | null | undefined>('UserContext');\nconst [ClientContext, useClientContext] = createContextAndHook<ClientResource | null | undefined>('ClientContext');\nconst [SessionContext, useSessionContext] = createContextAndHook<ActiveSessionResource | null | undefined>(\n  'SessionContext',\n);\n\ntype OrganizationContextProps = {\n  organization: OrganizationResource | null | undefined;\n\n  /**\n   * @deprecated This property will be dropped in the next major release.\n   * This property is only used in another deprecated part: `invitationList` from useOrganization\n   */\n  lastOrganizationInvitation: OrganizationInvitationResource | null | undefined;\n  /**\n   * @deprecated This property will be dropped in the next major release.\n   * This property is only used in another deprecated part: `membershipList` from useOrganization\n   */\n  lastOrganizationMember: OrganizationMembershipResource | null | undefined;\n};\nconst [OrganizationContextInternal, useOrganizationContext] = createContextAndHook<{\n  organization: OrganizationResource | null | undefined;\n  lastOrganizationInvitation: OrganizationInvitationResource | null | undefined;\n  lastOrganizationMember: OrganizationMembershipResource | null | undefined;\n}>('OrganizationContext');\n\nconst OrganizationProvider = ({\n  children,\n  organization,\n  lastOrganizationMember,\n  lastOrganizationInvitation,\n  swrConfig,\n}: PropsWithChildren<\n  OrganizationContextProps & {\n    // Exporting inferred types  directly from SWR will result in error while building declarations\n    swrConfig?: any;\n  }\n>) => {\n  return (\n    <SWRConfig value={swrConfig}>\n      <OrganizationContextInternal.Provider\n        value={{\n          value: {\n            organization,\n            lastOrganizationMember,\n            lastOrganizationInvitation,\n          },\n        }}\n      >\n        {children}\n      </OrganizationContextInternal.Provider>\n    </SWRConfig>\n  );\n};\n\n/**\n * @deprecated use OrganizationProvider instead\n */\nexport const OrganizationContext = (...args: Parameters<typeof OrganizationProvider>) => {\n  deprecated('OrganizationContext', 'Use `OrganizationProvider` instead');\n  return OrganizationProvider(...args);\n};\n\nexport {\n  ClientContext,\n  useClientContext,\n  OrganizationProvider,\n  useOrganizationContext,\n  UserContext,\n  useUserContext,\n  SessionContext,\n  useSessionContext,\n  ClerkInstanceContext,\n  useClerkInstanceContext,\n};\n", "'use client';\n\nimport { useCallback, useMemo, useRef, useState } from 'react';\n\nimport { useSWR, useSWRInfinite } from '../clerk-swr';\nimport type { CacheSetter, PaginatedResources, ValueOrSetter } from '../types';\n\nfunction getDifferentKeys(obj1: Record<string, unknown>, obj2: Record<string, unknown>): Record<string, unknown> {\n  const keysSet = new Set(Object.keys(obj2));\n  const differentKeysObject: Record<string, unknown> = {};\n\n  for (const key1 of Object.keys(obj1)) {\n    if (!keysSet.has(key1)) {\n      differentKeysObject[key1] = obj1[key1];\n    }\n  }\n\n  return differentKeysObject;\n}\n\ntype PagesOrInfiniteOptions = {\n  /**\n   * This the starting point for your fetched results. The initial value persists between re-renders\n   */\n  initialPage?: number;\n  /**\n   * Maximum number of items returned per request. The initial value persists between re-renders\n   */\n  pageSize?: number;\n};\n\nexport const useWithSafeValues = <T extends PagesOrInfiniteOptions>(params: T | true | undefined, defaultValues: T) => {\n  const shouldUseDefaults = typeof params === 'boolean' && params;\n\n  // Cache initialPage and initialPageSize until unmount\n  const initialPageRef = useRef(\n    shouldUseDefaults ? defaultValues.initialPage : params?.initialPage ?? defaultValues.initialPage,\n  );\n  const pageSizeRef = useRef(shouldUseDefaults ? defaultValues.pageSize : params?.pageSize ?? defaultValues.pageSize);\n\n  const newObj: Record<string, unknown> = {};\n  for (const key of Object.keys(defaultValues)) {\n    // @ts-ignore\n    newObj[key] = shouldUseDefaults ? defaultValues[key] : params?.[key] ?? defaultValues[key];\n  }\n\n  return {\n    ...newObj,\n    initialPage: initialPageRef.current,\n    pageSize: pageSizeRef.current,\n  } as T;\n};\n\ntype ArrayType<DataArray> = DataArray extends Array<infer ElementType> ? ElementType : never;\ntype ExtractData<Type> = Type extends { data: infer Data } ? ArrayType<Data> : Type;\n\ntype DefaultOptions = {\n  /**\n   * Persists the previous pages with new ones in the same array\n   */\n  infinite?: boolean;\n  /**\n   * Return the previous key's data until the new data has been loaded\n   */\n  keepPreviousData?: boolean;\n  /**\n   * Should a request be triggered\n   */\n  enabled?: boolean;\n};\n\ntype UsePagesOrInfinite = <\n  Params extends PagesOrInfiniteOptions,\n  FetcherReturnData extends Record<string, any>,\n  CacheKeys = Record<string, unknown>,\n  TOptions extends DefaultOptions = DefaultOptions,\n>(\n  /**\n   * The parameters will be passed to the fetcher\n   */\n  params: Params,\n  /**\n   * A Promise returning function to fetch your data\n   */\n  fetcher: ((p: Params) => FetcherReturnData | Promise<FetcherReturnData>) | undefined,\n  /**\n   * Internal configuration of the hook\n   */\n  options: TOptions,\n  cacheKeys: CacheKeys,\n) => PaginatedResources<ExtractData<FetcherReturnData>, TOptions['infinite']>;\n\nexport const usePagesOrInfinite: UsePagesOrInfinite = (params, fetcher, options, cacheKeys) => {\n  const [paginatedPage, setPaginatedPage] = useState(params.initialPage ?? 1);\n\n  // Cache initialPage and initialPageSize until unmount\n  const initialPageRef = useRef(params.initialPage ?? 1);\n  const pageSizeRef = useRef(params.pageSize ?? 10);\n\n  const enabled = options.enabled ?? true;\n  const triggerInfinite = options.infinite ?? false;\n  const keepPreviousData = options.keepPreviousData ?? false;\n\n  const pagesCacheKey = {\n    ...cacheKeys,\n    ...params,\n    initialPage: paginatedPage,\n    pageSize: pageSizeRef.current,\n  };\n\n  const {\n    data: swrData,\n    isValidating: swrIsValidating,\n    isLoading: swrIsLoading,\n    error: swrError,\n    mutate: swrMutate,\n  } = useSWR(\n    !triggerInfinite && !!fetcher && enabled ? pagesCacheKey : null,\n    cacheKeyParams => {\n      // @ts-ignore\n      const requestParams = getDifferentKeys(cacheKeyParams, cacheKeys);\n      // @ts-ignore\n      return fetcher?.(requestParams);\n    },\n    { keepPreviousData },\n  );\n\n  const {\n    data: swrInfiniteData,\n    isLoading: swrInfiniteIsLoading,\n    isValidating: swrInfiniteIsValidating,\n    error: swrInfiniteError,\n    size,\n    setSize,\n    mutate: swrInfiniteMutate,\n  } = useSWRInfinite(\n    pageIndex => {\n      if (!triggerInfinite || !enabled) {\n        return null;\n      }\n\n      return {\n        ...params,\n        ...cacheKeys,\n        initialPage: initialPageRef.current + pageIndex,\n        pageSize: pageSizeRef.current,\n      };\n    },\n    cacheKeyParams => {\n      // @ts-ignore\n      const requestParams = getDifferentKeys(cacheKeyParams, cacheKeys);\n      // @ts-ignore\n      return fetcher?.(requestParams);\n    },\n  );\n\n  const page = useMemo(() => {\n    if (triggerInfinite) {\n      return size;\n    }\n    return paginatedPage;\n  }, [triggerInfinite, size, paginatedPage]);\n\n  const fetchPage: ValueOrSetter<number> = useCallback(\n    numberOrgFn => {\n      if (triggerInfinite) {\n        void setSize(numberOrgFn);\n        return;\n      }\n      return setPaginatedPage(numberOrgFn);\n    },\n    [setSize],\n  );\n\n  const data = useMemo(() => {\n    if (triggerInfinite) {\n      return swrInfiniteData?.map(a => a?.data).flat() ?? [];\n    }\n    return swrData?.data ?? [];\n  }, [triggerInfinite, swrData, swrInfiniteData]);\n\n  const count = useMemo(() => {\n    if (triggerInfinite) {\n      return swrInfiniteData?.[swrInfiniteData?.length - 1]?.total_count || 0;\n    }\n    return swrData?.total_count ?? 0;\n  }, [triggerInfinite, swrData, swrInfiniteData]);\n\n  const isLoading = triggerInfinite ? swrInfiniteIsLoading : swrIsLoading;\n  const isFetching = triggerInfinite ? swrInfiniteIsValidating : swrIsValidating;\n  const isError = !!(triggerInfinite ? swrInfiniteError : swrError);\n  /**\n   * Helpers\n   */\n  const fetchNext = useCallback(() => {\n    fetchPage(n => Math.max(0, n + 1));\n  }, [fetchPage]);\n\n  const fetchPrevious = useCallback(() => {\n    fetchPage(n => Math.max(0, n - 1));\n  }, [fetchPage]);\n\n  const offsetCount = (initialPageRef.current - 1) * pageSizeRef.current;\n\n  const pageCount = Math.ceil((count - offsetCount) / pageSizeRef.current);\n  const hasNextPage = count - offsetCount * pageSizeRef.current > page * pageSizeRef.current;\n  const hasPreviousPage = (page - 1) * pageSizeRef.current > offsetCount * pageSizeRef.current;\n\n  const setData: CacheSetter = triggerInfinite\n    ? value =>\n        swrInfiniteMutate(value, {\n          revalidate: false,\n        })\n    : value =>\n        swrMutate(value, {\n          revalidate: false,\n        });\n\n  const revalidate = triggerInfinite ? () => swrInfiniteMutate() : () => swrMutate();\n\n  return {\n    data,\n    count,\n    isLoading,\n    isFetching,\n    isError,\n    page,\n    pageCount,\n    fetchPage,\n    fetchNext,\n    fetchPrevious,\n    hasNextPage,\n    hasPreviousPage,\n    // Let the hook return type define this type\n    revalidate: revalidate as any,\n    // Let the hook return type define this type\n    setData: setData as any,\n  };\n};\n", "import type {\n  ClerkPaginatedResponse,\n  ClerkPaginationParams,\n  GetDomainsParams,\n  GetInvitationsParams,\n  GetMembershipRequestParams,\n  GetMembershipsParams,\n  GetMembersParams,\n  GetPendingInvitationsParams,\n  OrganizationDomainResource,\n  OrganizationInvitationResource,\n  OrganizationMembershipRequestResource,\n  OrganizationMembershipResource,\n  OrganizationResource,\n} from '@clerk/types';\n\nimport { deprecated } from '../../deprecated';\nimport { useSWR } from '../clerk-swr';\nimport { useClerkInstanceContext, useOrganizationContext, useSessionContext } from '../contexts';\nimport type { PaginatedResources, PaginatedResourcesWithDefault } from '../types';\nimport { usePagesOrInfinite, useWithSafeValues } from './usePagesOrInfinite';\n\ntype UseOrganizationParams = {\n  /**\n   * @deprecated Use `invitations` instead\n   */\n  invitationList?: GetPendingInvitationsParams;\n  /**\n   * @deprecated Use `memberships` instead\n   */\n  membershipList?: GetMembershipsParams;\n  domains?:\n    | true\n    | (GetDomainsParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n  membershipRequests?:\n    | true\n    | (GetMembershipRequestParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n  memberships?:\n    | true\n    | (GetMembersParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n\n  invitations?:\n    | true\n    | (GetInvitationsParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n};\n\ntype UseOrganization = <T extends UseOrganizationParams>(\n  params?: T,\n) =>\n  | {\n      isLoaded: false;\n      organization: undefined;\n      /**\n       * @deprecated Use `invitations` instead\n       */\n      invitationList: undefined;\n      /**\n       * @deprecated Use `memberships` instead\n       */\n      membershipList: undefined;\n      membership: undefined;\n      domains: PaginatedResourcesWithDefault<OrganizationDomainResource>;\n      membershipRequests: PaginatedResourcesWithDefault<OrganizationMembershipRequestResource>;\n      memberships: PaginatedResourcesWithDefault<OrganizationMembershipResource>;\n      invitations: PaginatedResourcesWithDefault<OrganizationInvitationResource>;\n    }\n  | {\n      isLoaded: true;\n      organization: OrganizationResource;\n      /**\n       * @deprecated Use `invitations` instead\n       */\n      invitationList: undefined;\n      /**\n       * @deprecated Use `memberships` instead\n       */\n      membershipList: undefined;\n      membership: undefined;\n      domains: PaginatedResourcesWithDefault<OrganizationDomainResource>;\n      membershipRequests: PaginatedResourcesWithDefault<OrganizationMembershipRequestResource>;\n      memberships: PaginatedResourcesWithDefault<OrganizationMembershipResource>;\n      invitations: PaginatedResourcesWithDefault<OrganizationInvitationResource>;\n    }\n  | {\n      isLoaded: boolean;\n      organization: OrganizationResource | null;\n      /**\n       * @deprecated Use `invitations` instead\n       */\n      invitationList: OrganizationInvitationResource[] | null | undefined;\n      /**\n       * @deprecated Use `memberships` instead\n       */\n      membershipList: OrganizationMembershipResource[] | null | undefined;\n      membership: OrganizationMembershipResource | null | undefined;\n      domains: PaginatedResources<\n        OrganizationDomainResource,\n        T['membershipRequests'] extends { infinite: true } ? true : false\n      > | null;\n      membershipRequests: PaginatedResources<\n        OrganizationMembershipRequestResource,\n        T['membershipRequests'] extends { infinite: true } ? true : false\n      > | null;\n      memberships: PaginatedResources<\n        OrganizationMembershipResource,\n        T['memberships'] extends { infinite: true } ? true : false\n      > | null;\n      invitations: PaginatedResources<\n        OrganizationInvitationResource,\n        T['invitations'] extends { infinite: true } ? true : false\n      > | null;\n    };\n\nconst undefinedPaginatedResource = {\n  data: undefined,\n  count: undefined,\n  isLoading: false,\n  isFetching: false,\n  isError: false,\n  page: undefined,\n  pageCount: undefined,\n  fetchPage: undefined,\n  fetchNext: undefined,\n  fetchPrevious: undefined,\n  hasNextPage: false,\n  hasPreviousPage: false,\n  revalidate: undefined,\n  setData: undefined,\n} as const;\n\nexport const useOrganization: UseOrganization = params => {\n  const {\n    invitationList: invitationListParams,\n    membershipList: membershipListParams,\n    domains: domainListParams,\n    membershipRequests: membershipRequestsListParams,\n    memberships: membersListParams,\n    invitations: invitationsListParams,\n  } = params || {};\n  const { organization, lastOrganizationMember, lastOrganizationInvitation } = useOrganizationContext();\n  const session = useSessionContext();\n\n  const domainSafeValues = useWithSafeValues(domainListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    keepPreviousData: false,\n    infinite: false,\n    enrollmentMode: undefined,\n  });\n\n  const membershipRequestSafeValues = useWithSafeValues(membershipRequestsListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    status: 'pending',\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const membersSafeValues = useWithSafeValues(membersListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    role: undefined,\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const invitationsSafeValues = useWithSafeValues(invitationsListParams, {\n    initialPage: 1,\n    pageSize: 10,\n    status: ['pending'],\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const clerk = useClerkInstanceContext();\n\n  const shouldFetch = !!(clerk.loaded && session && organization);\n\n  const domainParams =\n    typeof domainListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: domainSafeValues.initialPage,\n          pageSize: domainSafeValues.pageSize,\n          enrollmentMode: domainSafeValues.enrollmentMode,\n        };\n\n  const membershipRequestParams =\n    typeof membershipRequestsListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: membershipRequestSafeValues.initialPage,\n          pageSize: membershipRequestSafeValues.pageSize,\n          status: membershipRequestSafeValues.status,\n        };\n\n  const membersParams =\n    typeof membersListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: membersSafeValues.initialPage,\n          pageSize: membersSafeValues.pageSize,\n          role: membersSafeValues.role,\n        };\n\n  const invitationsParams =\n    typeof invitationsListParams === 'undefined'\n      ? undefined\n      : {\n          initialPage: invitationsSafeValues.initialPage,\n          pageSize: invitationsSafeValues.pageSize,\n          status: invitationsSafeValues.status,\n        };\n\n  const domains = usePagesOrInfinite<GetDomainsParams, ClerkPaginatedResponse<OrganizationDomainResource>>(\n    {\n      ...domainParams,\n    },\n    organization?.getDomains,\n    {\n      keepPreviousData: domainSafeValues.keepPreviousData,\n      infinite: domainSafeValues.infinite,\n      enabled: !!domainParams,\n    },\n    {\n      type: 'domains',\n      organizationId: organization?.id,\n    },\n  );\n\n  const membershipRequests = usePagesOrInfinite<\n    GetMembershipRequestParams,\n    ClerkPaginatedResponse<OrganizationMembershipRequestResource>\n  >(\n    {\n      ...membershipRequestParams,\n    },\n    organization?.getMembershipRequests,\n    {\n      keepPreviousData: membershipRequestSafeValues.keepPreviousData,\n      infinite: membershipRequestSafeValues.infinite,\n      enabled: !!membershipRequestParams,\n    },\n    {\n      type: 'membershipRequests',\n      organizationId: organization?.id,\n    },\n  );\n\n  const memberships = usePagesOrInfinite<GetMembersParams, ClerkPaginatedResponse<OrganizationMembershipResource>>(\n    {\n      ...membersParams,\n      paginated: true,\n    } as any,\n    organization?.getMemberships as unknown as any,\n    {\n      keepPreviousData: membersSafeValues.keepPreviousData,\n      infinite: membersSafeValues.infinite,\n      enabled: !!membersParams,\n    },\n    {\n      type: 'members',\n      organizationId: organization?.id,\n    },\n  );\n\n  const invitations = usePagesOrInfinite<GetInvitationsParams, ClerkPaginatedResponse<OrganizationInvitationResource>>(\n    {\n      ...invitationsParams,\n    },\n    organization?.getInvitations,\n    {\n      keepPreviousData: invitationsSafeValues.keepPreviousData,\n      infinite: invitationsSafeValues.infinite,\n      enabled: !!invitationsParams,\n    },\n    {\n      type: 'invitations',\n      organizationId: organization?.id,\n    },\n  );\n\n  // Some gymnastics to adhere to the rules of hooks\n  // We need to make sure useSWR is called on every render\n  const pendingInvitations = !clerk.loaded\n    ? () => [] as OrganizationInvitationResource[]\n    : () => clerk.organization?.getPendingInvitations(invitationListParams);\n\n  const currentOrganizationMemberships = !clerk.loaded\n    ? () => [] as OrganizationMembershipResource[]\n    : () => clerk.organization?.getMemberships(membershipListParams);\n\n  if (invitationListParams) {\n    deprecated('invitationList in useOrganization', 'Use the `invitations` property and return value instead.');\n  }\n\n  const {\n    data: invitationList,\n    isValidating: isInvitationsLoading,\n    mutate: mutateInvitationList,\n  } = useSWR(\n    shouldFetch && invitationListParams\n      ? cacheKey('invites', organization, lastOrganizationInvitation, invitationListParams)\n      : null,\n    pendingInvitations,\n  );\n\n  if (membershipListParams) {\n    deprecated('membershipList in useOrganization', 'Use the `memberships` property and return value instead.');\n  }\n\n  const {\n    data: membershipList,\n    isValidating: isMembershipsLoading,\n    mutate: mutateMembershipList,\n  } = useSWR(\n    shouldFetch && membershipListParams\n      ? cacheKey('memberships', organization, lastOrganizationMember, membershipListParams)\n      : null,\n    currentOrganizationMemberships,\n  );\n\n  if (organization === undefined) {\n    return {\n      isLoaded: false,\n      organization: undefined,\n      invitationList: undefined,\n      membershipList: undefined,\n      membership: undefined,\n      domains: undefinedPaginatedResource,\n      membershipRequests: undefinedPaginatedResource,\n      memberships: undefinedPaginatedResource,\n      invitations: undefinedPaginatedResource,\n    };\n  }\n\n  if (organization === null) {\n    return {\n      isLoaded: true,\n      organization: null,\n      invitationList: null,\n      membershipList: null,\n      membership: null,\n      domains: null,\n      membershipRequests: null,\n      memberships: null,\n      invitations: null,\n    };\n  }\n\n  /** In SSR context we include only the organization object when loadOrg is set to true. */\n  if (!clerk.loaded && organization) {\n    return {\n      isLoaded: true,\n      organization,\n      invitationList: undefined,\n      membershipList: undefined,\n      membership: undefined,\n      domains: undefinedPaginatedResource,\n      membershipRequests: undefinedPaginatedResource,\n      memberships: undefinedPaginatedResource,\n      invitations: undefinedPaginatedResource,\n    };\n  }\n\n  return {\n    isLoaded: !isMembershipsLoading && !isInvitationsLoading,\n    organization,\n    membershipList,\n    membership: getCurrentOrganizationMembership(session!.user.organizationMemberships, organization.id), // your membership in the current org\n    invitationList,\n    unstable__mutate: () => {\n      void mutateMembershipList();\n      void mutateInvitationList();\n    },\n    domains,\n    membershipRequests,\n    memberships,\n    invitations,\n  };\n};\n\nfunction getCurrentOrganizationMembership(\n  organizationMemberships: OrganizationMembershipResource[],\n  activeOrganizationId: string,\n) {\n  return organizationMemberships.find(\n    organizationMembership => organizationMembership.organization.id === activeOrganizationId,\n  );\n}\n\nfunction cacheKey(\n  type: 'memberships' | 'invites',\n  organization: OrganizationResource,\n  resource: OrganizationInvitationResource | OrganizationMembershipResource | null | undefined,\n  pagination: ClerkPaginationParams,\n) {\n  return [type, organization.id, resource?.id, resource?.updatedAt, pagination.offset, pagination.limit]\n    .filter(Boolean)\n    .join('-');\n}\n", "import type {\n  ClerkPaginatedResponse,\n  CreateOrganizationParams,\n  GetUserOrganizationInvitationsParams,\n  GetUserOrganizationMembershipParams,\n  GetUserOrganizationSuggestionsParams,\n  OrganizationMembershipResource,\n  OrganizationResource,\n  OrganizationSuggestionResource,\n  SetActive,\n  UserOrganizationInvitationResource,\n} from '@clerk/types';\n\nimport { deprecatedObjectProperty } from '../../deprecated';\nimport { useClerkInstanceContext, useUserContext } from '../contexts';\nimport type { PaginatedResources, PaginatedResourcesWithDefault } from '../types';\nimport { usePagesOrInfinite, useWithSafeValues } from './usePagesOrInfinite';\n\ntype UseOrganizationListParams = {\n  userMemberships?:\n    | true\n    | (GetUserOrganizationMembershipParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n  userInvitations?:\n    | true\n    | (GetUserOrganizationInvitationsParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n  userSuggestions?:\n    | true\n    | (GetUserOrganizationSuggestionsParams & {\n        infinite?: boolean;\n        keepPreviousData?: boolean;\n      });\n};\n\ntype OrganizationList = ReturnType<typeof createOrganizationList>;\nconst undefinedPaginatedResource = {\n  data: undefined,\n  count: undefined,\n  isLoading: false,\n  isFetching: false,\n  isError: false,\n  page: undefined,\n  pageCount: undefined,\n  fetchPage: undefined,\n  fetchNext: undefined,\n  fetchPrevious: undefined,\n  hasNextPage: false,\n  hasPreviousPage: false,\n  revalidate: undefined,\n  setData: undefined,\n} as const;\n\ntype UseOrganizationList = <T extends UseOrganizationListParams>(\n  params?: T,\n) =>\n  | {\n      isLoaded: false;\n      /**\n       * @deprecated Use userMemberships instead\n       */\n      organizationList: undefined;\n      createOrganization: undefined;\n      setActive: undefined;\n      userMemberships: PaginatedResourcesWithDefault<OrganizationMembershipResource>;\n      userInvitations: PaginatedResourcesWithDefault<UserOrganizationInvitationResource>;\n      userSuggestions: PaginatedResourcesWithDefault<OrganizationSuggestionResource>;\n    }\n  | {\n      isLoaded: boolean;\n      /**\n       * @deprecated Use userMemberships instead\n       */\n      organizationList: OrganizationList;\n      createOrganization: (params: CreateOrganizationParams) => Promise<OrganizationResource>;\n      setActive: SetActive;\n      userMemberships: PaginatedResources<\n        OrganizationMembershipResource,\n        T['userMemberships'] extends { infinite: true } ? true : false\n      >;\n      userInvitations: PaginatedResources<\n        UserOrganizationInvitationResource,\n        T['userInvitations'] extends { infinite: true } ? true : false\n      >;\n      userSuggestions: PaginatedResources<\n        OrganizationSuggestionResource,\n        T['userSuggestions'] extends { infinite: true } ? true : false\n      >;\n    };\n\nexport const useOrganizationList: UseOrganizationList = params => {\n  const { userMemberships, userInvitations, userSuggestions } = params || {};\n\n  const userMembershipsSafeValues = useWithSafeValues(userMemberships, {\n    initialPage: 1,\n    pageSize: 10,\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const userInvitationsSafeValues = useWithSafeValues(userInvitations, {\n    initialPage: 1,\n    pageSize: 10,\n    status: 'pending',\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const userSuggestionsSafeValues = useWithSafeValues(userSuggestions, {\n    initialPage: 1,\n    pageSize: 10,\n    status: 'pending',\n    keepPreviousData: false,\n    infinite: false,\n  });\n\n  const clerk = useClerkInstanceContext();\n  const user = useUserContext();\n\n  const userMembershipsParams =\n    typeof userMemberships === 'undefined'\n      ? undefined\n      : {\n          initialPage: userMembershipsSafeValues.initialPage,\n          pageSize: userMembershipsSafeValues.pageSize,\n        };\n\n  const userInvitationsParams =\n    typeof userInvitations === 'undefined'\n      ? undefined\n      : {\n          initialPage: userInvitationsSafeValues.initialPage,\n          pageSize: userInvitationsSafeValues.pageSize,\n          status: userInvitationsSafeValues.status,\n        };\n\n  const userSuggestionsParams =\n    typeof userSuggestions === 'undefined'\n      ? undefined\n      : {\n          initialPage: userSuggestionsSafeValues.initialPage,\n          pageSize: userSuggestionsSafeValues.pageSize,\n          status: userSuggestionsSafeValues.status,\n        };\n\n  const isClerkLoaded = !!(clerk.loaded && user);\n\n  const memberships = usePagesOrInfinite<\n    GetUserOrganizationMembershipParams,\n    ClerkPaginatedResponse<OrganizationMembershipResource>\n  >(\n    {\n      ...userMembershipsParams,\n      paginated: true,\n    } as any,\n    user?.getOrganizationMemberships as unknown as any,\n    {\n      keepPreviousData: userMembershipsSafeValues.keepPreviousData,\n      infinite: userMembershipsSafeValues.infinite,\n      enabled: !!userMembershipsParams,\n    },\n    {\n      type: 'userMemberships',\n      userId: user?.id,\n    },\n  );\n\n  const invitations = usePagesOrInfinite<\n    GetUserOrganizationInvitationsParams,\n    ClerkPaginatedResponse<UserOrganizationInvitationResource>\n  >(\n    {\n      ...userInvitationsParams,\n    },\n    user?.getOrganizationInvitations,\n    {\n      keepPreviousData: userInvitationsSafeValues.keepPreviousData,\n      infinite: userInvitationsSafeValues.infinite,\n      enabled: !!userInvitationsParams,\n    },\n    {\n      type: 'userInvitations',\n      userId: user?.id,\n    },\n  );\n\n  const suggestions = usePagesOrInfinite<\n    GetUserOrganizationSuggestionsParams,\n    ClerkPaginatedResponse<OrganizationSuggestionResource>\n  >(\n    {\n      ...userSuggestionsParams,\n    },\n    user?.getOrganizationSuggestions,\n    {\n      keepPreviousData: userSuggestionsSafeValues.keepPreviousData,\n      infinite: userSuggestionsSafeValues.infinite,\n      enabled: !!userSuggestionsParams,\n    },\n    {\n      type: 'userSuggestions',\n      userId: user?.id,\n    },\n  );\n\n  // TODO: Properly check for SSR user values\n  if (!isClerkLoaded) {\n    return {\n      isLoaded: false,\n      organizationList: undefined,\n      createOrganization: undefined,\n      setActive: undefined,\n      userMemberships: undefinedPaginatedResource,\n      userInvitations: undefinedPaginatedResource,\n      userSuggestions: undefinedPaginatedResource,\n    };\n  }\n\n  const result = {\n    isLoaded: isClerkLoaded,\n    organizationList: createOrganizationList(user.organizationMemberships),\n    setActive: clerk.setActive,\n    createOrganization: clerk.createOrganization,\n    userMemberships: memberships,\n    userInvitations: invitations,\n    userSuggestions: suggestions,\n  };\n  deprecatedObjectProperty(result, 'organizationList', 'Use `userMemberships` instead.');\n\n  return result;\n};\n\nfunction createOrganizationList(organizationMemberships: OrganizationMembershipResource[]) {\n  return organizationMemberships.map(organizationMembership => ({\n    membership: organizationMembership,\n    organization: organizationMembership.organization,\n  }));\n}\n", "import type { CreateOrganizationParams, OrganizationMembershipResource, OrganizationResource } from '@clerk/types';\n\nimport { deprecated } from '../../deprecated';\nimport { useClerkInstanceContext } from '../contexts';\n\ntype UseOrganizationsReturn =\n  | {\n      isLoaded: false;\n\n      /**\n       * @deprecated Use `createOrganization` from `useOrganizationList`\n       * Example: `const {createOrganization} = useOrganizationList()`\n       */\n      createOrganization: undefined;\n\n      /**\n       * @deprecated Use `memberships` from `useOrganization`\n       * Example: `const {memberships} = useOrganization()`\n       */\n      getOrganizationMemberships: undefined;\n\n      /**\n       * @deprecated Use `getOrganization` from `useClerk`\n       * Example: `const {getOrganization} = useClerk()`\n       */\n      getOrganization: undefined;\n    }\n  | {\n      isLoaded: true;\n      /**\n       * @deprecated Use `createOrganization` from `useOrganizationList`\n       * Example: `const {createOrganization} = useOrganizationList()`\n       */\n      createOrganization: (params: CreateOrganizationParams) => Promise<OrganizationResource>;\n\n      /**\n       * @deprecated Use `memberships` from `useOrganization`\n       * Example: `const {memberships} = useOrganization()`\n       */\n      getOrganizationMemberships: () => Promise<OrganizationMembershipResource[]>;\n\n      /**\n       * @deprecated Use `getOrganization` from `useClerk`\n       * Example: `const {getOrganization} = useClerk()`\n       */\n      getOrganization: (organizationId: string) => Promise<OrganizationResource | undefined>;\n    };\n\ntype UseOrganizations = () => UseOrganizationsReturn;\n\n/**\n * @deprecated Use useOrganizationList, useOrganization, or useClerk instead\n */\nexport const useOrganizations: UseOrganizations = () => {\n  deprecated('useOrganizations', 'Use useOrganizationList, useOrganization, or useClerk instead.');\n  const clerk = useClerkInstanceContext();\n  if (!clerk.loaded) {\n    return {\n      isLoaded: false,\n      createOrganization: undefined,\n      getOrganizationMemberships: undefined,\n      getOrganization: undefined,\n    };\n  }\n\n  return {\n    isLoaded: true,\n    createOrganization: clerk.createOrganization,\n    getOrganizationMemberships: clerk.getOrganizationMemberships,\n    getOrganization: clerk.getOrganization,\n  };\n};\n", "import React from 'react';\n\nexport const useSafeLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\n"], "mappings": ";;;;AACA,OAAOA,KAAA,MAAW;AAEX,SAASC,oBAAoBC,UAAA,EAAqBC,QAAA,EAA2D;EAClH,IAAI,CAACD,UAAA,EAAY;IACf,MAAM,OAAOC,QAAA,KAAa,WAAW,IAAIC,KAAA,CAAMD,QAAQ,IAAI,IAAIC,KAAA,CAAM,GAAGD,QAAA,CAASE,WAAW,YAAY;EAC1G;AACF;AAYO,IAAMC,oBAAA,GAAuBA,CAClCD,WAAA,EACAE,OAAA,KAC8E;EAC9E,MAAM;IAAEC,WAAA,GAAcP;EAAoB,IAAIM,OAAA,IAAW,CAAC;EAC1D,MAAME,GAAA,GAAMT,KAAA,CAAMU,aAAA,CAA6C,MAAS;EACxED,GAAA,CAAIJ,WAAA,GAAcA,WAAA;EAElB,MAAMM,MAAA,GAASA,CAAA,KAAM;IACnB,MAAMC,GAAA,GAAMZ,KAAA,CAAMa,UAAA,CAAWJ,GAAG;IAChCD,WAAA,CAAYI,GAAA,EAAK,GAAGP,WAAW,YAAY;IAC3C,OAAQO,GAAA,CAAYE,KAAA;EACtB;EAEA,MAAMC,sBAAA,GAAyBA,CAAA,KAAM;IACnC,MAAMH,GAAA,GAAMZ,KAAA,CAAMa,UAAA,CAAWJ,GAAG;IAChC,OAAOG,GAAA,GAAMA,GAAA,CAAIE,KAAA,GAAQ,CAAC;EAC5B;EAEA,OAAO,CAACL,GAAA,EAAKE,MAAA,EAAQI,sBAAsB;AAC7C;;;ACvCA,IAAAC,iBAAA;AAAAC,QAAA,CAAAD,iBAAA;EAAAE,SAAA,EAAAA,CAAA,KAAAA,SAAA;EAAAC,MAAA,EAAAA,CAAA,KAAAC,QAAA;EAAAC,cAAA,EAAAA,CAAA,KAAAC;AAAA;AACAC,UAAA,CAAAP,iBAAA,EAAAQ,QAAA;AAAA,YAAAA,QAAA,MAAc;AACd,SAAoBC,OAAA,IAAXL,QAAA,EAAmBF,SAAA,QAAiB;AAC7C,SAAoBO,OAAA,IAAXH,QAAA,QAAiC;;;ACS1C,OAAOI,MAAA,MAAW;AAMlB,IAAM,CAACC,oBAAA,EAAsBC,uBAAuB,IAAItB,oBAAA,CAAkC,sBAAsB;AAChH,IAAM,CAACuB,WAAA,EAAaC,cAAc,IAAIxB,oBAAA,CAAsD,aAAa;AACzG,IAAM,CAACyB,aAAA,EAAeC,gBAAgB,IAAI1B,oBAAA,CAAwD,eAAe;AACjH,IAAM,CAAC2B,cAAA,EAAgBC,iBAAiB,IAAI5B,oBAAA,CAC1C,gBACF;AAgBA,IAAM,CAAC6B,2BAAA,EAA6BC,sBAAsB,IAAI9B,oBAAA,CAI3D,qBAAqB;AAExB,IAAM+B,oBAAA,GAAuBA,CAAC;EAC5BC,QAAA;EACAC,YAAA;EACAC,sBAAA;EACAC,0BAAA;EACAC;AACF,MAKM;EACJ,OACE,eAAAhB,MAAA,CAAAiB,aAAA,CAACzB,SAAA;IAAUJ,KAAA,EAAO4B;EAAA,GAChB,eAAAhB,MAAA,CAAAiB,aAAA,CAACR,2BAAA,CAA4BS,QAAA,EAA5B;IACC9B,KAAA,EAAO;MACLA,KAAA,EAAO;QACLyB,YAAA;QACAC,sBAAA;QACAC;MACF;IACF;EAAA,GAECH,QACH,CACF;AAEJ;AAKO,IAAMO,mBAAA,GAAsBA,CAAA,GAAIC,IAAA,KAAkD;EACvFC,UAAA,CAAW,uBAAuB,oCAAoC;EACtE,OAAOV,oBAAA,CAAqB,GAAGS,IAAI;AACrC;;;AC9EA,SAASE,WAAA,EAAaC,OAAA,EAASC,MAAA,EAAQC,QAAA,QAAgB;AAKvD,SAASC,iBAAiBC,IAAA,EAA+BC,IAAA,EAAwD;EAC/G,MAAMC,OAAA,GAAU,IAAIC,GAAA,CAAIC,MAAA,CAAOC,IAAA,CAAKJ,IAAI,CAAC;EACzC,MAAMK,mBAAA,GAA+C,CAAC;EAEtD,WAAWC,IAAA,IAAQH,MAAA,CAAOC,IAAA,CAAKL,IAAI,GAAG;IACpC,IAAI,CAACE,OAAA,CAAQM,GAAA,CAAID,IAAI,GAAG;MACtBD,mBAAA,CAAoBC,IAAI,IAAIP,IAAA,CAAKO,IAAI;IACvC;EACF;EAEA,OAAOD,mBAAA;AACT;AAaO,IAAMG,iBAAA,GAAoBA,CAAmCC,MAAA,EAA8BC,aAAA,KAAqB;EA/BvH,IAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;EAgCE,MAAMC,iBAAA,GAAoB,OAAOL,MAAA,KAAW,aAAaA,MAAA;EAGzD,MAAMM,cAAA,GAAiBnB,MAAA,CACrBkB,iBAAA,GAAoBJ,aAAA,CAAcM,WAAA,IAAcL,EAAA,GAAAF,MAAA,oBAAAA,MAAA,CAAQO,WAAA,KAAR,OAAAL,EAAA,GAAuBD,aAAA,CAAcM,WACvF;EACA,MAAMC,WAAA,GAAcrB,MAAA,CAAOkB,iBAAA,GAAoBJ,aAAA,CAAcQ,QAAA,IAAWN,EAAA,GAAAH,MAAA,oBAAAA,MAAA,CAAQS,QAAA,KAAR,OAAAN,EAAA,GAAoBF,aAAA,CAAcQ,QAAQ;EAElH,MAAMC,MAAA,GAAkC,CAAC;EACzC,WAAWC,GAAA,IAAOjB,MAAA,CAAOC,IAAA,CAAKM,aAAa,GAAG;IAE5CS,MAAA,CAAOC,GAAG,IAAIN,iBAAA,GAAoBJ,aAAA,CAAcU,GAAG,KAAIP,EAAA,GAAAJ,MAAA,oBAAAA,MAAA,CAASW,GAAA,MAAT,OAAAP,EAAA,GAAiBH,aAAA,CAAcU,GAAG;EAC3F;EAEA,OAAO;IACL,GAAGD,MAAA;IACHH,WAAA,EAAaD,cAAA,CAAeM,OAAA;IAC5BH,QAAA,EAAUD,WAAA,CAAYI;EACxB;AACF;AAyCO,IAAMC,kBAAA,GAAyCA,CAACb,MAAA,EAAQc,OAAA,EAAStE,OAAA,EAASuE,SAAA,KAAc;EA5F/F,IAAAb,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAY,EAAA,EAAAC,EAAA,EAAAC,EAAA;EA6FE,MAAM,CAACC,aAAA,EAAeC,gBAAgB,IAAIhC,QAAA,EAASc,EAAA,GAAAF,MAAA,CAAOO,WAAA,KAAP,OAAAL,EAAA,GAAsB,CAAC;EAG1E,MAAMI,cAAA,GAAiBnB,MAAA,EAAOgB,EAAA,GAAAH,MAAA,CAAOO,WAAA,KAAP,OAAAJ,EAAA,GAAsB,CAAC;EACrD,MAAMK,WAAA,GAAcrB,MAAA,EAAOiB,EAAA,GAAAJ,MAAA,CAAOS,QAAA,KAAP,OAAAL,EAAA,GAAmB,EAAE;EAEhD,MAAMiB,OAAA,IAAUL,EAAA,GAAAxE,OAAA,CAAQ6E,OAAA,KAAR,OAAAL,EAAA,GAAmB;EACnC,MAAMM,eAAA,IAAkBL,EAAA,GAAAzE,OAAA,CAAQ+E,QAAA,KAAR,OAAAN,EAAA,GAAoB;EAC5C,MAAMO,gBAAA,IAAmBN,EAAA,GAAA1E,OAAA,CAAQgF,gBAAA,KAAR,OAAAN,EAAA,GAA4B;EAErD,MAAMO,aAAA,GAAgB;IACpB,GAAGV,SAAA;IACH,GAAGf,MAAA;IACHO,WAAA,EAAaY,aAAA;IACbV,QAAA,EAAUD,WAAA,CAAYI;EACxB;EAEA,MAAM;IACJc,IAAA,EAAMC,OAAA;IACNC,YAAA,EAAcC,eAAA;IACdC,SAAA,EAAWC,YAAA;IACXC,KAAA,EAAOC,QAAA;IACPC,MAAA,EAAQC;EACV,IAAI9E,QAAA,CACF,CAACiE,eAAA,IAAmB,CAAC,CAACR,OAAA,IAAWO,OAAA,GAAUI,aAAA,GAAgB,MAC3DW,cAAA,IAAkB;IAEhB,MAAMC,aAAA,GAAgBhD,gBAAA,CAAiB+C,cAAA,EAAgBrB,SAAS;IAEhE,OAAOD,OAAA,oBAAAA,OAAA,CAAUuB,aAAA;EACnB,GACA;IAAEb;EAAiB,CACrB;EAEA,MAAM;IACJE,IAAA,EAAMY,eAAA;IACNR,SAAA,EAAWS,oBAAA;IACXX,YAAA,EAAcY,uBAAA;IACdR,KAAA,EAAOS,gBAAA;IACPC,IAAA;IACAC,OAAA;IACAT,MAAA,EAAQU;EACV,IAAIrF,QAAA,CACFsF,SAAA,IAAa;IACX,IAAI,CAACvB,eAAA,IAAmB,CAACD,OAAA,EAAS;MAChC,OAAO;IACT;IAEA,OAAO;MACL,GAAGrB,MAAA;MACH,GAAGe,SAAA;MACHR,WAAA,EAAaD,cAAA,CAAeM,OAAA,GAAUiC,SAAA;MACtCpC,QAAA,EAAUD,WAAA,CAAYI;IACxB;EACF,GACAwB,cAAA,IAAkB;IAEhB,MAAMC,aAAA,GAAgBhD,gBAAA,CAAiB+C,cAAA,EAAgBrB,SAAS;IAEhE,OAAOD,OAAA,oBAAAA,OAAA,CAAUuB,aAAA;EACnB,CACF;EAEA,MAAMS,IAAA,GAAO5D,OAAA,CAAQ,MAAM;IACzB,IAAIoC,eAAA,EAAiB;MACnB,OAAOoB,IAAA;IACT;IACA,OAAOvB,aAAA;EACT,GAAG,CAACG,eAAA,EAAiBoB,IAAA,EAAMvB,aAAa,CAAC;EAEzC,MAAM4B,SAAA,GAAmC9D,WAAA,CACvC+D,WAAA,IAAe;IACb,IAAI1B,eAAA,EAAiB;MACnB,KAAKqB,OAAA,CAAQK,WAAW;MACxB;IACF;IACA,OAAO5B,gBAAA,CAAiB4B,WAAW;EACrC,GACA,CAACL,OAAO,CACV;EAEA,MAAMjB,IAAA,GAAOxC,OAAA,CAAQ,MAAM;IA9K7B,IAAA+D,GAAA,EAAAC,GAAA;IA+KI,IAAI5B,eAAA,EAAiB;MACnB,QAAO2B,GAAA,GAAAX,eAAA,oBAAAA,eAAA,CAAiBa,GAAA,CAAIC,CAAA,IAAKA,CAAA,oBAAAA,CAAA,CAAG1B,IAAA,EAAM2B,IAAA,OAAnC,OAAAJ,GAAA,GAA6C,EAAC;IACvD;IACA,QAAOC,GAAA,GAAAvB,OAAA,oBAAAA,OAAA,CAASD,IAAA,KAAT,OAAAwB,GAAA,GAAiB,EAAC;EAC3B,GAAG,CAAC5B,eAAA,EAAiBK,OAAA,EAASW,eAAe,CAAC;EAE9C,MAAMgB,KAAA,GAAQpE,OAAA,CAAQ,MAAM;IArL9B,IAAA+D,GAAA,EAAAC,GAAA;IAsLI,IAAI5B,eAAA,EAAiB;MACnB,SAAO2B,GAAA,GAAAX,eAAA,oBAAAA,eAAA,EAAkBA,eAAA,oBAAAA,eAAA,CAAiBiB,MAAA,IAAS,OAA5C,gBAAAN,GAAA,CAAgDO,WAAA,KAAe;IACxE;IACA,QAAON,GAAA,GAAAvB,OAAA,oBAAAA,OAAA,CAAS6B,WAAA,KAAT,OAAAN,GAAA,GAAwB;EACjC,GAAG,CAAC5B,eAAA,EAAiBK,OAAA,EAASW,eAAe,CAAC;EAE9C,MAAMR,SAAA,GAAYR,eAAA,GAAkBiB,oBAAA,GAAuBR,YAAA;EAC3D,MAAM0B,UAAA,GAAanC,eAAA,GAAkBkB,uBAAA,GAA0BX,eAAA;EAC/D,MAAM6B,OAAA,GAAU,CAAC,EAAEpC,eAAA,GAAkBmB,gBAAA,GAAmBR,QAAA;EAIxD,MAAM0B,SAAA,GAAY1E,WAAA,CAAY,MAAM;IAClC8D,SAAA,CAAUa,CAAA,IAAKC,IAAA,CAAKC,GAAA,CAAI,GAAGF,CAAA,GAAI,CAAC,CAAC;EACnC,GAAG,CAACb,SAAS,CAAC;EAEd,MAAMgB,aAAA,GAAgB9E,WAAA,CAAY,MAAM;IACtC8D,SAAA,CAAUa,CAAA,IAAKC,IAAA,CAAKC,GAAA,CAAI,GAAGF,CAAA,GAAI,CAAC,CAAC;EACnC,GAAG,CAACb,SAAS,CAAC;EAEd,MAAMiB,WAAA,IAAe1D,cAAA,CAAeM,OAAA,GAAU,KAAKJ,WAAA,CAAYI,OAAA;EAE/D,MAAMqD,SAAA,GAAYJ,IAAA,CAAKK,IAAA,EAAMZ,KAAA,GAAQU,WAAA,IAAexD,WAAA,CAAYI,OAAO;EACvE,MAAMuD,WAAA,GAAcb,KAAA,GAAQU,WAAA,GAAcxD,WAAA,CAAYI,OAAA,GAAUkC,IAAA,GAAOtC,WAAA,CAAYI,OAAA;EACnF,MAAMwD,eAAA,IAAmBtB,IAAA,GAAO,KAAKtC,WAAA,CAAYI,OAAA,GAAUoD,WAAA,GAAcxD,WAAA,CAAYI,OAAA;EAErF,MAAMyD,OAAA,GAAuB/C,eAAA,GACzBvE,KAAA,IACE6F,iBAAA,CAAkB7F,KAAA,EAAO;IACvBuH,UAAA,EAAY;EACd,CAAC,IACHvH,KAAA,IACEoF,SAAA,CAAUpF,KAAA,EAAO;IACfuH,UAAA,EAAY;EACd,CAAC;EAEP,MAAMA,UAAA,GAAahD,eAAA,GAAkB,MAAMsB,iBAAA,CAAkB,IAAI,MAAMT,SAAA,CAAU;EAEjF,OAAO;IACLT,IAAA;IACA4B,KAAA;IACAxB,SAAA;IACA2B,UAAA;IACAC,OAAA;IACAZ,IAAA;IACAmB,SAAA;IACAlB,SAAA;IACAY,SAAA;IACAI,aAAA;IACAI,WAAA;IACAC,eAAA;IAAA;IAEAE,UAAA;IAAA;IAEAD;EACF;AACF;;;ACjHA,IAAME,0BAAA,GAA6B;EACjC7C,IAAA,EAAM;EACN4B,KAAA,EAAO;EACPxB,SAAA,EAAW;EACX2B,UAAA,EAAY;EACZC,OAAA,EAAS;EACTZ,IAAA,EAAM;EACNmB,SAAA,EAAW;EACXlB,SAAA,EAAW;EACXY,SAAA,EAAW;EACXI,aAAA,EAAe;EACfI,WAAA,EAAa;EACbC,eAAA,EAAiB;EACjBE,UAAA,EAAY;EACZD,OAAA,EAAS;AACX;AAEO,IAAMG,eAAA,GAAmCxE,MAAA,IAAU;EACxD,MAAM;IACJyE,cAAA,EAAgBC,oBAAA;IAChBC,cAAA,EAAgBC,oBAAA;IAChBC,OAAA,EAASC,gBAAA;IACTC,kBAAA,EAAoBC,4BAAA;IACpBC,WAAA,EAAaC,iBAAA;IACbC,WAAA,EAAaC;EACf,IAAIpF,MAAA,IAAU,CAAC;EACf,MAAM;IAAExB,YAAA;IAAcC,sBAAA;IAAwBC;EAA2B,IAAIL,sBAAA,CAAuB;EACpG,MAAMgH,OAAA,GAAUlH,iBAAA,CAAkB;EAElC,MAAMmH,gBAAA,GAAmBvF,iBAAA,CAAkB+E,gBAAA,EAAkB;IAC3DvE,WAAA,EAAa;IACbE,QAAA,EAAU;IACVe,gBAAA,EAAkB;IAClBD,QAAA,EAAU;IACVgE,cAAA,EAAgB;EAClB,CAAC;EAED,MAAMC,2BAAA,GAA8BzF,iBAAA,CAAkBiF,4BAAA,EAA8B;IAClFzE,WAAA,EAAa;IACbE,QAAA,EAAU;IACVgF,MAAA,EAAQ;IACRjE,gBAAA,EAAkB;IAClBD,QAAA,EAAU;EACZ,CAAC;EAED,MAAMmE,iBAAA,GAAoB3F,iBAAA,CAAkBmF,iBAAA,EAAmB;IAC7D3E,WAAA,EAAa;IACbE,QAAA,EAAU;IACVkF,IAAA,EAAM;IACNnE,gBAAA,EAAkB;IAClBD,QAAA,EAAU;EACZ,CAAC;EAED,MAAMqE,qBAAA,GAAwB7F,iBAAA,CAAkBqF,qBAAA,EAAuB;IACrE7E,WAAA,EAAa;IACbE,QAAA,EAAU;IACVgF,MAAA,EAAQ,CAAC,SAAS;IAClBjE,gBAAA,EAAkB;IAClBD,QAAA,EAAU;EACZ,CAAC;EAED,MAAMsE,KAAA,GAAQhI,uBAAA,CAAwB;EAEtC,MAAMiI,WAAA,GAAc,CAAC,EAAED,KAAA,CAAME,MAAA,IAAUV,OAAA,IAAW7G,YAAA;EAElD,MAAMwH,YAAA,GACJ,OAAOlB,gBAAA,KAAqB,cACxB,SACA;IACEvE,WAAA,EAAa+E,gBAAA,CAAiB/E,WAAA;IAC9BE,QAAA,EAAU6E,gBAAA,CAAiB7E,QAAA;IAC3B8E,cAAA,EAAgBD,gBAAA,CAAiBC;EACnC;EAEN,MAAMU,uBAAA,GACJ,OAAOjB,4BAAA,KAAiC,cACpC,SACA;IACEzE,WAAA,EAAaiF,2BAAA,CAA4BjF,WAAA;IACzCE,QAAA,EAAU+E,2BAAA,CAA4B/E,QAAA;IACtCgF,MAAA,EAAQD,2BAAA,CAA4BC;EACtC;EAEN,MAAMS,aAAA,GACJ,OAAOhB,iBAAA,KAAsB,cACzB,SACA;IACE3E,WAAA,EAAamF,iBAAA,CAAkBnF,WAAA;IAC/BE,QAAA,EAAUiF,iBAAA,CAAkBjF,QAAA;IAC5BkF,IAAA,EAAMD,iBAAA,CAAkBC;EAC1B;EAEN,MAAMQ,iBAAA,GACJ,OAAOf,qBAAA,KAA0B,cAC7B,SACA;IACE7E,WAAA,EAAaqF,qBAAA,CAAsBrF,WAAA;IACnCE,QAAA,EAAUmF,qBAAA,CAAsBnF,QAAA;IAChCgF,MAAA,EAAQG,qBAAA,CAAsBH;EAChC;EAEN,MAAMZ,OAAA,GAAUhE,kBAAA,CACd;IACE,GAAGmF;EACL,GACAxH,YAAA,oBAAAA,YAAA,CAAc4H,UAAA,EACd;IACE5E,gBAAA,EAAkB8D,gBAAA,CAAiB9D,gBAAA;IACnCD,QAAA,EAAU+D,gBAAA,CAAiB/D,QAAA;IAC3BF,OAAA,EAAS,CAAC,CAAC2E;EACb,GACA;IACEK,IAAA,EAAM;IACNC,cAAA,EAAgB9H,YAAA,oBAAAA,YAAA,CAAc+H;EAChC,CACF;EAEA,MAAMxB,kBAAA,GAAqBlE,kBAAA,CAIzB;IACE,GAAGoF;EACL,GACAzH,YAAA,oBAAAA,YAAA,CAAcgI,qBAAA,EACd;IACEhF,gBAAA,EAAkBgE,2BAAA,CAA4BhE,gBAAA;IAC9CD,QAAA,EAAUiE,2BAAA,CAA4BjE,QAAA;IACtCF,OAAA,EAAS,CAAC,CAAC4E;EACb,GACA;IACEI,IAAA,EAAM;IACNC,cAAA,EAAgB9H,YAAA,oBAAAA,YAAA,CAAc+H;EAChC,CACF;EAEA,MAAMtB,WAAA,GAAcpE,kBAAA,CAClB;IACE,GAAGqF,aAAA;IACHO,SAAA,EAAW;EACb,GACAjI,YAAA,oBAAAA,YAAA,CAAckI,cAAA,EACd;IACElF,gBAAA,EAAkBkE,iBAAA,CAAkBlE,gBAAA;IACpCD,QAAA,EAAUmE,iBAAA,CAAkBnE,QAAA;IAC5BF,OAAA,EAAS,CAAC,CAAC6E;EACb,GACA;IACEG,IAAA,EAAM;IACNC,cAAA,EAAgB9H,YAAA,oBAAAA,YAAA,CAAc+H;EAChC,CACF;EAEA,MAAMpB,WAAA,GAActE,kBAAA,CAClB;IACE,GAAGsF;EACL,GACA3H,YAAA,oBAAAA,YAAA,CAAcmI,cAAA,EACd;IACEnF,gBAAA,EAAkBoE,qBAAA,CAAsBpE,gBAAA;IACxCD,QAAA,EAAUqE,qBAAA,CAAsBrE,QAAA;IAChCF,OAAA,EAAS,CAAC,CAAC8E;EACb,GACA;IACEE,IAAA,EAAM;IACNC,cAAA,EAAgB9H,YAAA,oBAAAA,YAAA,CAAc+H;EAChC,CACF;EAIA,MAAMK,kBAAA,GAAqB,CAACf,KAAA,CAAME,MAAA,GAC9B,MAAM,EAAC,GACP,MAAG;IA1ST,IAAA7F,EAAA;IA0SY,QAAAA,EAAA,GAAA2F,KAAA,CAAMrH,YAAA,KAAN,gBAAA0B,EAAA,CAAoB2G,qBAAA,CAAsBnC,oBAAA;EAAA;EAEpD,MAAMoC,8BAAA,GAAiC,CAACjB,KAAA,CAAME,MAAA,GAC1C,MAAM,EAAC,GACP,MAAG;IA9ST,IAAA7F,EAAA;IA8SY,QAAAA,EAAA,GAAA2F,KAAA,CAAMrH,YAAA,KAAN,gBAAA0B,EAAA,CAAoBwG,cAAA,CAAe9B,oBAAA;EAAA;EAE7C,IAAIF,oBAAA,EAAsB;IACxB1F,UAAA,CAAW,qCAAqC,0DAA0D;EAC5G;EAEA,MAAM;IACJ0C,IAAA,EAAM+C,cAAA;IACN7C,YAAA,EAAcmF,oBAAA;IACd7E,MAAA,EAAQ8E;EACV,IAAI3J,QAAA,CACFyI,WAAA,IAAepB,oBAAA,GACXuC,QAAA,CAAS,WAAWzI,YAAA,EAAcE,0BAAA,EAA4BgG,oBAAoB,IAClF,MACJkC,kBACF;EAEA,IAAIhC,oBAAA,EAAsB;IACxB5F,UAAA,CAAW,qCAAqC,0DAA0D;EAC5G;EAEA,MAAM;IACJ0C,IAAA,EAAMiD,cAAA;IACN/C,YAAA,EAAcsF,oBAAA;IACdhF,MAAA,EAAQiF;EACV,IAAI9J,QAAA,CACFyI,WAAA,IAAelB,oBAAA,GACXqC,QAAA,CAAS,eAAezI,YAAA,EAAcC,sBAAA,EAAwBmG,oBAAoB,IAClF,MACJkC,8BACF;EAEA,IAAItI,YAAA,KAAiB,QAAW;IAC9B,OAAO;MACL4I,QAAA,EAAU;MACV5I,YAAA,EAAc;MACdiG,cAAA,EAAgB;MAChBE,cAAA,EAAgB;MAChB0C,UAAA,EAAY;MACZxC,OAAA,EAASN,0BAAA;MACTQ,kBAAA,EAAoBR,0BAAA;MACpBU,WAAA,EAAaV,0BAAA;MACbY,WAAA,EAAaZ;IACf;EACF;EAEA,IAAI/F,YAAA,KAAiB,MAAM;IACzB,OAAO;MACL4I,QAAA,EAAU;MACV5I,YAAA,EAAc;MACdiG,cAAA,EAAgB;MAChBE,cAAA,EAAgB;MAChB0C,UAAA,EAAY;MACZxC,OAAA,EAAS;MACTE,kBAAA,EAAoB;MACpBE,WAAA,EAAa;MACbE,WAAA,EAAa;IACf;EACF;EAGA,IAAI,CAACU,KAAA,CAAME,MAAA,IAAUvH,YAAA,EAAc;IACjC,OAAO;MACL4I,QAAA,EAAU;MACV5I,YAAA;MACAiG,cAAA,EAAgB;MAChBE,cAAA,EAAgB;MAChB0C,UAAA,EAAY;MACZxC,OAAA,EAASN,0BAAA;MACTQ,kBAAA,EAAoBR,0BAAA;MACpBU,WAAA,EAAaV,0BAAA;MACbY,WAAA,EAAaZ;IACf;EACF;EAEA,OAAO;IACL6C,QAAA,EAAU,CAACF,oBAAA,IAAwB,CAACH,oBAAA;IACpCvI,YAAA;IACAmG,cAAA;IACA0C,UAAA,EAAYC,gCAAA,CAAiCjC,OAAA,CAASkC,IAAA,CAAKC,uBAAA,EAAyBhJ,YAAA,CAAa+H,EAAE;IAAA;IACnG9B,cAAA;IACAgD,gBAAA,EAAkBA,CAAA,KAAM;MACtB,KAAKN,oBAAA,CAAqB;MAC1B,KAAKH,oBAAA,CAAqB;IAC5B;IACAnC,OAAA;IACAE,kBAAA;IACAE,WAAA;IACAE;EACF;AACF;AAEA,SAASmC,iCACPE,uBAAA,EACAE,oBAAA,EACA;EACA,OAAOF,uBAAA,CAAwBG,IAAA,CAC7BC,sBAAA,IAA0BA,sBAAA,CAAuBpJ,YAAA,CAAa+H,EAAA,KAAOmB,oBACvE;AACF;AAEA,SAAST,SACPZ,IAAA,EACA7H,YAAA,EACAqJ,QAAA,EACAC,UAAA,EACA;EACA,OAAO,CAACzB,IAAA,EAAM7H,YAAA,CAAa+H,EAAA,EAAIsB,QAAA,oBAAAA,QAAA,CAAUtB,EAAA,EAAIsB,QAAA,oBAAAA,QAAA,CAAUE,SAAA,EAAWD,UAAA,CAAWE,MAAA,EAAQF,UAAA,CAAWG,KAAK,EAClGC,MAAA,CAAOC,OAAO,EACdC,IAAA,CAAK,GAAG;AACb;;;ACpXA,IAAMC,2BAAA,GAA6B;EACjC3G,IAAA,EAAM;EACN4B,KAAA,EAAO;EACPxB,SAAA,EAAW;EACX2B,UAAA,EAAY;EACZC,OAAA,EAAS;EACTZ,IAAA,EAAM;EACNmB,SAAA,EAAW;EACXlB,SAAA,EAAW;EACXY,SAAA,EAAW;EACXI,aAAA,EAAe;EACfI,WAAA,EAAa;EACbC,eAAA,EAAiB;EACjBE,UAAA,EAAY;EACZD,OAAA,EAAS;AACX;AAuCO,IAAMiE,mBAAA,GAA2CtI,MAAA,IAAU;EAChE,MAAM;IAAEuI,eAAA;IAAiBC,eAAA;IAAiBC;EAAgB,IAAIzI,MAAA,IAAU,CAAC;EAEzE,MAAM0I,yBAAA,GAA4B3I,iBAAA,CAAkBwI,eAAA,EAAiB;IACnEhI,WAAA,EAAa;IACbE,QAAA,EAAU;IACVe,gBAAA,EAAkB;IAClBD,QAAA,EAAU;EACZ,CAAC;EAED,MAAMoH,yBAAA,GAA4B5I,iBAAA,CAAkByI,eAAA,EAAiB;IACnEjI,WAAA,EAAa;IACbE,QAAA,EAAU;IACVgF,MAAA,EAAQ;IACRjE,gBAAA,EAAkB;IAClBD,QAAA,EAAU;EACZ,CAAC;EAED,MAAMqH,yBAAA,GAA4B7I,iBAAA,CAAkB0I,eAAA,EAAiB;IACnElI,WAAA,EAAa;IACbE,QAAA,EAAU;IACVgF,MAAA,EAAQ;IACRjE,gBAAA,EAAkB;IAClBD,QAAA,EAAU;EACZ,CAAC;EAED,MAAMsE,KAAA,GAAQhI,uBAAA,CAAwB;EACtC,MAAM0J,IAAA,GAAOxJ,cAAA,CAAe;EAE5B,MAAM8K,qBAAA,GACJ,OAAON,eAAA,KAAoB,cACvB,SACA;IACEhI,WAAA,EAAamI,yBAAA,CAA0BnI,WAAA;IACvCE,QAAA,EAAUiI,yBAAA,CAA0BjI;EACtC;EAEN,MAAMqI,qBAAA,GACJ,OAAON,eAAA,KAAoB,cACvB,SACA;IACEjI,WAAA,EAAaoI,yBAAA,CAA0BpI,WAAA;IACvCE,QAAA,EAAUkI,yBAAA,CAA0BlI,QAAA;IACpCgF,MAAA,EAAQkD,yBAAA,CAA0BlD;EACpC;EAEN,MAAMsD,qBAAA,GACJ,OAAON,eAAA,KAAoB,cACvB,SACA;IACElI,WAAA,EAAaqI,yBAAA,CAA0BrI,WAAA;IACvCE,QAAA,EAAUmI,yBAAA,CAA0BnI,QAAA;IACpCgF,MAAA,EAAQmD,yBAAA,CAA0BnD;EACpC;EAEN,MAAMuD,aAAA,GAAgB,CAAC,EAAEnD,KAAA,CAAME,MAAA,IAAUwB,IAAA;EAEzC,MAAMtC,WAAA,GAAcpE,kBAAA,CAIlB;IACE,GAAGgI,qBAAA;IACHpC,SAAA,EAAW;EACb,GACAc,IAAA,oBAAAA,IAAA,CAAM0B,0BAAA,EACN;IACEzH,gBAAA,EAAkBkH,yBAAA,CAA0BlH,gBAAA;IAC5CD,QAAA,EAAUmH,yBAAA,CAA0BnH,QAAA;IACpCF,OAAA,EAAS,CAAC,CAACwH;EACb,GACA;IACExC,IAAA,EAAM;IACN6C,MAAA,EAAQ3B,IAAA,oBAAAA,IAAA,CAAMhB;EAChB,CACF;EAEA,MAAMpB,WAAA,GAActE,kBAAA,CAIlB;IACE,GAAGiI;EACL,GACAvB,IAAA,oBAAAA,IAAA,CAAM4B,0BAAA,EACN;IACE3H,gBAAA,EAAkBmH,yBAAA,CAA0BnH,gBAAA;IAC5CD,QAAA,EAAUoH,yBAAA,CAA0BpH,QAAA;IACpCF,OAAA,EAAS,CAAC,CAACyH;EACb,GACA;IACEzC,IAAA,EAAM;IACN6C,MAAA,EAAQ3B,IAAA,oBAAAA,IAAA,CAAMhB;EAChB,CACF;EAEA,MAAM6C,WAAA,GAAcvI,kBAAA,CAIlB;IACE,GAAGkI;EACL,GACAxB,IAAA,oBAAAA,IAAA,CAAM8B,0BAAA,EACN;IACE7H,gBAAA,EAAkBoH,yBAAA,CAA0BpH,gBAAA;IAC5CD,QAAA,EAAUqH,yBAAA,CAA0BrH,QAAA;IACpCF,OAAA,EAAS,CAAC,CAAC0H;EACb,GACA;IACE1C,IAAA,EAAM;IACN6C,MAAA,EAAQ3B,IAAA,oBAAAA,IAAA,CAAMhB;EAChB,CACF;EAGA,IAAI,CAACyC,aAAA,EAAe;IAClB,OAAO;MACL5B,QAAA,EAAU;MACVkC,gBAAA,EAAkB;MAClBC,kBAAA,EAAoB;MACpBC,SAAA,EAAW;MACXjB,eAAA,EAAiBF,2BAAA;MACjBG,eAAA,EAAiBH,2BAAA;MACjBI,eAAA,EAAiBJ;IACnB;EACF;EAEA,MAAMoB,MAAA,GAAS;IACbrC,QAAA,EAAU4B,aAAA;IACVM,gBAAA,EAAkBI,sBAAA,CAAuBnC,IAAA,CAAKC,uBAAuB;IACrEgC,SAAA,EAAW3D,KAAA,CAAM2D,SAAA;IACjBD,kBAAA,EAAoB1D,KAAA,CAAM0D,kBAAA;IAC1BhB,eAAA,EAAiBtD,WAAA;IACjBuD,eAAA,EAAiBrD,WAAA;IACjBsD,eAAA,EAAiBW;EACnB;EACAO,wBAAA,CAAyBF,MAAA,EAAQ,oBAAoB,gCAAgC;EAErF,OAAOA,MAAA;AACT;AAEA,SAASC,uBAAuBlC,uBAAA,EAA2D;EACzF,OAAOA,uBAAA,CAAwBrE,GAAA,CAAIyE,sBAAA,KAA2B;IAC5DP,UAAA,EAAYO,sBAAA;IACZpJ,YAAA,EAAcoJ,sBAAA,CAAuBpJ;EACvC,EAAE;AACJ;;;AC5LO,IAAMoL,gBAAA,GAAqCA,CAAA,KAAM;EACtD5K,UAAA,CAAW,oBAAoB,gEAAgE;EAC/F,MAAM6G,KAAA,GAAQhI,uBAAA,CAAwB;EACtC,IAAI,CAACgI,KAAA,CAAME,MAAA,EAAQ;IACjB,OAAO;MACLqB,QAAA,EAAU;MACVmC,kBAAA,EAAoB;MACpBN,0BAAA,EAA4B;MAC5BY,eAAA,EAAiB;IACnB;EACF;EAEA,OAAO;IACLzC,QAAA,EAAU;IACVmC,kBAAA,EAAoB1D,KAAA,CAAM0D,kBAAA;IAC1BN,0BAAA,EAA4BpD,KAAA,CAAMoD,0BAAA;IAClCY,eAAA,EAAiBhE,KAAA,CAAMgE;EACzB;AACF;;;ACvEA,OAAOC,MAAA,MAAW;AAEX,IAAMC,mBAAA,GAAsB,OAAOC,MAAA,KAAW,cAAcF,MAAA,CAAMG,eAAA,GAAkBH,MAAA,CAAMI,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}