["services/tests/test_gemini_migration.py::test_gemini_service", "test/test_chat_api.py::test_chat_api", "test/test_chat_api.py::test_server_health", "test/test_recommend_api.py::test_recommend_api", "test/test_recommend_api.py::test_server_health", "test/test_user_management_system.py::test_database_connection", "test/test_user_management_system.py::test_user_management_system", "test/test_user_management_system.py::test_user_repository", "user_management/tests/test_user_repository.py::test_add_user_preference", "user_management/tests/test_user_repository.py::test_create_user", "user_management/tests/test_user_repository.py::test_get_current_mood", "user_management/tests/test_user_repository.py::test_update_user"]